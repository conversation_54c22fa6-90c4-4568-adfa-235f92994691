## 系统配置管理 API 文档

本页描述系统配置管理相关的所有路由与请求/响应规范。为保持兼容性，保留了“按组名”的旧接口，同时新增“分组化（按分组ID）”管理接口。

### 基本信息
- 基础路径：/system-config
- 返回格式：application/json
- 成功响应统一结构：
  - { code: 200, message: "success", data: {...}, timestamp, request_id }
- 失败响应统一结构：
  - { code: 4xx/5xx, message: "错误信息", errors?: [...], timestamp, request_id }

---

## 一、配置分组（新：按分组ID）

用于管理系统配置的“分组”本身，每个配置项必须归属于某个分组。

### 1. 创建配置分组
- POST /system-config/group
- Body
  - group_name string 必填
  - group_description string 可选
  - sort_order int 可选，默认 0
- Response
  - data.group: { id, group_name, group_description, sort_order, created_at, updated_at }

### 2. 获取配置分组列表
- GET /system-config/group
- Query: 无
- Response
  - data.groups: [{ id, group_name, group_description, sort_order, config_count, created_at, updated_at }]
  - total

说明：为兼容旧前端，返回项同时包含兼容字段 group/name/count（等同 group_name / group_name / config_count）。

### 3. 更新配置分组
- PUT /system-config/group/{id}
- Body
  - group_name string 可选（如修改会校验唯一）
  - group_description string 可选
  - sort_order int 可选
- Response
  - data: { id }

### 4. 删除配置分组
- DELETE /system-config/group/{id}
- 条件
  - 当该分组下仍有关联配置项时，拒绝删除
- Response
  - data: {}

### 5. 获取指定分组下的配置项
- GET /system-config/group/{id}/items
- Response
  - data.configs: [{ 配置项摘要信息 }], total
  - 配置摘要包含：id, config_key, config_name, formatted_value, config_type, config_group, config_description, default_value, is_encrypted, is_public, is_editable, options, sort_order, updated_at 等

---

## 二、配置项管理（兼容增强）

### 1. 创建配置项
- POST /system-config
- Body（二选一指定分组）
  - group_id int（推荐，新的分组管理方式）
    或
  - config_group string（兼容旧用法，如分组不存在将自动创建同名分组）
- 其他必填
  - config_key string（字母开头，允许字母/数字/下划线/点）
  - config_name string
  - config_type string（下述类型之一）
- 可选
  - config_description string
  - default_value string|number|bool|json
  - options array（select/multiselect/radio/checkbox 等类型的候选值）
  - is_encrypted boolean（加密存储标记）
  - is_public boolean（是否公开）
  - is_editable boolean（是否可编辑）
  - sort_order int
- 支持的 config_type:
  - string, integer, float, boolean, json, array, email, url, password, text, select, multiselect, radio, checkbox, date, datetime, time, file, image
- Response
  - data.config: 配置项摘要信息

说明：
- 当前服务层支持 group_id 或 config_group 任一参数，推荐使用 group_id
- 仍保留 config_group 字段以兼容旧展示

### 2. 获取所有配置（按组聚合）
- GET /system-config
- Query
  - include_encrypted bool 可选，默认 false；为 true 时包含加密项（值以不可见形式展示）
- Response
  - data.configs: { [groupName]: [配置项摘要...] }
  - total

### 3. 获取某个分组（按组名，旧）
- GET /system-config/group/{group}
- Response
  - data: { group, configs: [配置项摘要...], total }

### 4. 获取配置类型列表
- GET /system-config/types
- Response
  - data.types: [{ value, label }]

### 5. 获取公开配置（无需认证）
- GET /system-config/public
- Response
  - data.configs: { [groupName]: [配置项摘要...（值为适配/隐藏形式）] }
  - total

### 6. 批量更新配置
- POST /system-config/batch-update
- Body
  - configs object，如：{ "site.title": "新标题", "site.switch": true, ... }
- Response
  - data.results: { [key]: { success, old_value, new_value } }
  - data.errors: { [key]: "原因" }
  - success_count, error_count, message

### 7. 验证配置值
- POST /system-config/validate
- Body
  - config_key string
  - config_value any
- Response
  - data: { valid: boolean, message?: string }

### 8. 获取单个配置
- GET /system-config/{key}
- Response
  - data.config: 配置项摘要信息

### 9. 更新单个配置
- PUT /system-config/{key}
- Body
  - config_value any
- Response
  - data.config: 配置项摘要信息

### 10. 删除配置
- DELETE /system-config/{key}
- Response
  - data: { message }

---

## 三、示例

- 创建分组
  - POST /system-config/group
  - body: { "group_name": "booking", "group_description": "预订相关配置", "sort_order": 10 }

- 在指定分组（ID=3）下创建配置项
  - POST /system-config
  - body:
    - {
      "group_id": 3,
      "config_key": "booking.enabled",
      "config_name": "启用预订",
      "config_type": "boolean",
      "config_description": "是否启用在线预订",
      "default_value": "1",
      "is_public": false,
      "is_editable": true,
      "sort_order": 1
      }

- 获取分组列表
  - GET /system-config/group

- 获取指定分组（ID=3）下的配置项
  - GET /system-config/group/3/items

- 批量更新配置
  - POST /system-config/batch-update
  - body:
    - { "booking.enabled": false, "site.title": "旅行商户后台" }

---

## 四、备注与建议
- 建议后续新接入的前端/服务统一使用 group_id 管理分组，减少同名歧义
- 对于现有依赖 config_group 的调用仍可使用旧接口，返回中保留兼容字段
- 如需“在指定分组下创建/更新/删除配置项”的专用路由，也可拓展 /system-config/group/{id}/items 的 POST/PUT/DELETE 端点（目前通过通用 /system-config 接口即可完成创建/更新/删除）

若需要将本页导出为 README 方便团队协作，我可以把这份内容落盘到 docs/system-config-api.md。
