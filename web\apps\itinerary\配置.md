# 全局公共参数

**全局Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| X-Token | {{zy_token}} | string | 是 | - |
| version | 2 | number | 是 | - |

**全局Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局认证方式**

> 无需认证

# 状态码说明

| 状态码 | 中文描述 |
| --- | ---- |
| 暂无参数 |

# 小红书小程序

> 创建人: 江汉波

> 更新人: 江汉波

> 创建时间: 2025-08-06 14:53:30

> 更新时间: 2025-08-06 14:53:30

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

## 后台

> 创建人: 江汉波

> 更新人: 江汉波

> 创建时间: 2025-08-11 13:53:48

> 更新时间: 2025-08-11 13:53:48

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

### 配置相关

> 创建人: 江汉波

> 更新人: 江汉波

> 创建时间: 2025-08-07 15:18:33

> 更新时间: 2025-08-11 13:54:02

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 创建配置

> 创建人: 江汉波

> 更新人: 江汉波

> 创建时间: 2025-08-07 15:17:59

> 更新时间: 2025-08-07 15:41:49

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> 127.0.0.1:8899/system-config

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "config_key":"test3",
    "config_name":"测试",
    "config_type":"string",
    "config_group":"system"
}
```

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取所有配置

> 创建人: 江汉波

> 更新人: 江汉波

> 创建时间: 2025-08-07 15:01:30

> 更新时间: 2025-08-07 15:41:55

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> 127.0.0.1:8899/system-config

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
	"code": 200,
	"message": "success",
	"data": {
		"title": "马尔代夫阿雅达岛7日5晚奢华度假之旅",
		"price": 17999,
		"days": 7,
		"nights": 5,
		"destination": "50080018,50080017,50080019,1733684",
		"images": [
			"https://images.unsplash.com/photo-1573843981267-be1999ff37cd?q=80&w=1000",
			"https://images.unsplash.com/photo-1514282401047-d79a71a590e8?q=80&w=1000",
			"https://images.unsplash.com/photo-1540202404-a2f29016b523?q=80&w=1000",
			"https://images.unsplash.com/photo-1540202403-b7abd6747a18?q=80&w=1000"
		],
		"description": "奢享印度洋上的世外桃源，入住五星级水上别墅，畅游纯净海域，体验浪漫的海底餐厅和SPA。行程包含浮潜、深潜、海钓、海豚巡游等丰富水上活动。",
		"features": [
			"豪华水上别墅",
			"全程半餐或全餐",
			"内飞+快艇接送",
			"浮潜装备免费使用",
			"赠送一次海底餐厅体验",
			"赠送一次SPA体验"
		],
		"status": "online",
		"deposit": 8000,
		"scheduleDays": [
			{
				"day": 7,
				"title": "阿雅达岛-马累-北京",
				"description": "乘坐水上飞机返回马累，转机返回北京。",
				"meetingPoint": "酒店大堂",
				"meetingTime": "09:00:00",
				"schedule": [
					{
						"day": 7,
						"title": "返程航班",
						"description": "乘坐国际航班返回北京。",
						"activities": [
							"办理登机",
							"登机"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "19:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "飞机",
						"flightNumber": "SQ437+SQ800",
						"departureCity": "1337624",
						"arrivalCity": "110000",
						"stayType": "",
						"hotelName": "",
						"remark": "经新加坡转机返回北京，结束愉快的马尔代夫之旅。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 14,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 14,
							"minutes": 0
						}
					},
					{
						"day": 7,
						"title": "前往机场",
						"description": "前往马累国际机场。",
						"activities": [],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "16:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "专车",
						"flightNumber": "",
						"departureCity": "马累市区",
						"arrivalCity": "马累机场",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 30
						}
					},
					{
						"day": 7,
						"title": "午餐",
						"description": "在马累市区享用午餐。",
						"activities": [],
						"meals": [
							"马尔代夫特色海鲜"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "13:00:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 80,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 7,
						"title": "马累自由活动",
						"description": "在马累自由活动，购买纪念品。",
						"activities": [
							"购物",
							"市区游览"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "12:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "可在当地市场购买纪念品。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 3,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 3,
							"minutes": 0
						}
					},
					{
						"day": 7,
						"title": "前往马累",
						"description": "乘坐水上飞机返回马累。",
						"activities": [],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "11:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "水上飞机",
						"flightNumber": "",
						"departureCity": "阿雅达岛",
						"arrivalCity": "1337624",
						"stayType": "",
						"hotelName": "",
						"remark": "欣赏马尔代夫群岛鸟瞰美景。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 1,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 7,
						"title": "退房",
						"description": "办理退房手续，行李寄存。",
						"activities": [
							"退房",
							"行李寄存"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "accommodation",
						"startTime": "09:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 30
						}
					},
					{
						"day": 7,
						"title": "早餐",
						"description": "享用最后一顿岛上早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "07:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					}
				]
			},
			{
				"day": 6,
				"title": "阿雅达岛最后一天",
				"description": "自由活动，尽情享受马尔代夫的阳光和海滩。",
				"meetingPoint": "岛上接待中心",
				"meetingTime": "08:00:00",
				"schedule": [
					{
						"day": 6,
						"title": "告别晚宴",
						"description": "享用特别安排的私人沙滩烛光晚宴。",
						"activities": [],
						"meals": [
							"龙虾",
							"高级海鲜",
							"香槟"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:00:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "在星空下的沙滩享用浪漫的告别晚餐。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 30
						}
					},
					{
						"day": 6,
						"title": "继续自由活动",
						"description": "下午继续自由活动。",
						"activities": [
							"水上活动",
							"沙滩活动"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "14:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 4,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 4,
							"minutes": 0
						}
					},
					{
						"day": 6,
						"title": "午餐",
						"description": "在岛上餐厅享用午餐。",
						"activities": [],
						"meals": [
							"国际自助餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "12:30:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 6,
						"title": "自由活动",
						"description": "全天自由活动，尽情享受岛上设施。",
						"activities": [
							"游泳",
							"潜水",
							"日光浴",
							"沙滩活动"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "09:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "最后一天充分享受马尔代夫的阳光、沙滩和海水。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 5,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 5,
							"minutes": 0
						}
					},
					{
						"day": 6,
						"title": "早餐",
						"description": "在别墅内或餐厅享用早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "08:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					}
				]
			},
			{
				"day": 5,
				"title": "SPA及海钓体验",
				"description": "上午SPA护理，下午海钓体验。",
				"meetingPoint": "岛上接待中心",
				"meetingTime": "09:00:00",
				"schedule": [
					{
						"day": 5,
						"title": "晚餐",
						"description": "享用自己钓到的海鲜晚餐。",
						"activities": [],
						"meals": [
							"新鲜海鲜",
							"本地特色烹饪"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:00:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "厨师将根据您的喜好烹饪您钓到的海鱼。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 5,
						"title": "海钓体验",
						"description": "参加专业向导带领的海钓活动。",
						"activities": [
							"海钓",
							"日落观赏"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "15:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "体验马尔代夫传统海钓，可能钓到金枪鱼、旗鱼等。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 3,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 3,
							"minutes": 0
						}
					},
					{
						"day": 5,
						"title": "午餐",
						"description": "在岛上餐厅享用午餐。",
						"activities": [],
						"meals": [
							"轻食午餐",
							"热带水果"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "13:00:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					},
					{
						"day": 5,
						"title": "SPA体验",
						"description": "享受特色马尔代夫SPA理疗。",
						"activities": [
							"精油按摩",
							"面部护理",
							"身体护理"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "10:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "在海边的SPA亭享受专业理疗服务。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 2,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 5,
						"title": "早餐",
						"description": "在别墅内或餐厅享用早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "08:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					}
				]
			},
			{
				"day": 4,
				"title": "海底餐厅体验",
				"description": "早晚自由活动，中午特别安排海底餐厅用餐体验。",
				"meetingPoint": "岛上接待中心",
				"meetingTime": "08:00:00",
				"schedule": [
					{
						"day": 4,
						"title": "晚餐",
						"description": "在主餐厅享用晚餐。",
						"activities": [],
						"meals": [
							"国际美食",
							"特色甜点"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:30:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 4,
						"title": "自由活动",
						"description": "下午自由活动时间。",
						"activities": [
							"深潜",
							"海钓",
							"水上摩托"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "15:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "可选择参加度假村提供的各种活动，部分活动需额外付费。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 4,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 4,
							"minutes": 0
						}
					},
					{
						"day": 4,
						"title": "海底餐厅体验",
						"description": "在世界著名的海底餐厅享用午餐。",
						"activities": [],
						"meals": [
							"高级海鲜",
							"精选葡萄酒"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "12:30:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "在水下5米处的餐厅用餐，欣赏海底景观。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 4,
						"title": "自由活动",
						"description": "上午自由活动时间。",
						"activities": [
							"游泳",
							"日光浴",
							"按摩"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "09:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 2,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 30
						}
					},
					{
						"day": 4,
						"title": "早餐",
						"description": "在别墅内或餐厅享用早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "08:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					}
				]
			},
			{
				"day": 3,
				"title": "阿雅达岛休闲日",
				"description": "自由活动，可选择浮潜、海滩休闲等。",
				"meetingPoint": "岛上接待中心",
				"meetingTime": "08:00:00",
				"schedule": [
					{
						"day": 3,
						"title": "晚餐",
						"description": "在岛上餐厅享用晚餐。",
						"activities": [],
						"meals": [
							"国际美食",
							"本地特色菜"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:00:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 3,
						"title": "自由活动",
						"description": "自由时间，可选择各种水上活动。",
						"activities": [
							"皮划艇",
							"帆板",
							"沙滩排球"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "14:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "可选择参加度假村提供的各种活动，部分活动需额外付费。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 5,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 5,
							"minutes": 0
						}
					},
					{
						"day": 3,
						"title": "午餐",
						"description": "回到岛上享用午餐。",
						"activities": [],
						"meals": [
							"国际自助餐",
							"本地特色菜"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "12:30:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 3,
						"title": "浮潜体验",
						"description": "参加专业教练指导的浮潜活动。",
						"activities": [
							"浮潜装备",
							"珊瑚观赏",
							"热带鱼观赏"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "10:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "探索马尔代夫丰富的海底世界。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 2,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 3,
						"title": "早餐",
						"description": "在别墅内或餐厅享用早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "07:30:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "可选择在别墅内享用早餐服务。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					}
				]
			},
			{
				"day": 2,
				"title": "马累-阿雅达岛",
				"description": "乘坐水上飞机或快艇前往阿雅达岛，入住水上别墅。",
				"meetingPoint": "马累机场接待大厅",
				"meetingTime": "12:00:00",
				"schedule": [
					{
						"day": 2,
						"title": "欢迎晚宴",
						"description": "在沙滩餐厅享用特色晚餐。",
						"activities": [],
						"meals": [
							"烧烤海鲜",
							"热带饮品"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:00:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "在星空下享用浪漫晚餐。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "自由活动",
						"description": "自由时间，可在私人泳池或海滩放松。",
						"activities": [
							"游泳",
							"日光浴"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "16:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "享受马尔代夫的阳光与海滩。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 2,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "度假村介绍",
						"description": "参加度假村介绍会，了解各项设施与活动。",
						"activities": [
							"设施介绍",
							"活动预订"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "14:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "了解度假村提供的各项娱乐活动与服务。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 1,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "午餐",
						"description": "在岛上餐厅享用午餐。",
						"activities": [],
						"meals": [
							"海鲜自助餐",
							"热带水果"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "12:30:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "品尝新鲜的马尔代夫特色美食。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 2,
						"title": "入住水上别墅",
						"description": "入住豪华水上别墅，办理入住手续。",
						"activities": [
							"办理入住",
							"行李服务"
						],
						"meals": [],
						"accommodation": "阿雅达岛水上别墅",
						"images": [],
						"type": "accommodation",
						"startTime": "11:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "水上别墅",
						"hotelName": "Ayada Maldives Water Villa",
						"remark": "豪华水上别墅，配有私人露台和直接入海通道。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "抵达阿雅达岛",
						"description": "抵达阿雅达岛，工作人员迎接。",
						"activities": [
							"迎宾仪式",
							"椰子欢迎饮料"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "10:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "体验马尔代夫传统迎宾仪式。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 30
						}
					},
					{
						"day": 2,
						"title": "前往阿雅达岛",
						"description": "乘坐水上飞机前往阿雅达岛。",
						"activities": [
							"退房",
							"前往码头",
							"水上飞机"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "09:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "水上飞机",
						"flightNumber": "",
						"departureCity": "1337624",
						"arrivalCity": "阿雅达岛",
						"stayType": "",
						"hotelName": "",
						"remark": "欣赏马尔代夫环礁鸟瞰美景。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 1,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "早餐",
						"description": "酒店内享用早餐。",
						"activities": [],
						"meals": [
							"马尔代夫早餐",
							"热带水果"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "07:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					}
				]
			},
			{
				"day": 1,
				"title": "北京-马累",
				"description": "国际航班飞往马尔代夫首都马累。",
				"meetingPoint": "北京首都国际机场T3航站楼",
				"meetingTime": "10:00:00",
				"schedule": [
					{
						"day": 1,
						"title": "入住马累酒店",
						"description": "入住马累市区酒店，短暂休息。",
						"activities": [
							"办理入住",
							"休息"
						],
						"meals": [],
						"accommodation": "马累市区酒店",
						"images": [],
						"type": "accommodation",
						"startTime": "23:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "酒店",
						"hotelName": "Hotel Jen Malé",
						"remark": "马累市区豪华酒店，为第二天前往度假岛做准备。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 0
						}
					},
					{
						"day": 1,
						"title": "抵达马累",
						"description": "抵达马累国际机场，专人接机。",
						"activities": [
							"接机",
							"办理入境"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "22:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "导游将在机场出口处举\"马尔代夫度假\"接机牌。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 1,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					},
					{
						"day": 1,
						"title": "北京-马累",
						"description": "乘坐国际航班前往马尔代夫首都马累。",
						"activities": [
							"办理登机手续",
							"登机"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "10:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "飞机",
						"flightNumber": "SQ801+SQ436",
						"departureCity": "110000",
						"arrivalCity": "1337624",
						"stayType": "",
						"hotelName": "",
						"remark": "请提前3小时到达机场办理登机手续，经新加坡转机。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 12,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 12,
							"minutes": 0
						}
					}
				]
			}
		],
		"id": 16,
		"originalPrice": 29999,
		"departureCity": "440100,441900,441300,440300",
		"productType": "自由行",
		"coverImage": "https://images.unsplash.com/photo-1573843981267-be1999ff37cd?q=80&w=1000",
		"imageDescription": [
			"https://images.unsplash.com/photo-1512100356356-de1b84283e18?q=80&w=2000&auto=format",
			"https://images.unsplash.com/photo-1586861256632-42e108743990?q=80&w=2000&auto=format",
			"https://images.unsplash.com/photo-1514282401047-d79a71a590e8?q=80&w=2000&auto=format",
			"https://images.unsplash.com/photo-1602157947535-51a09392fcf0?q=80&w=2000&auto=format"
		],
		"travelMode": "休闲度假",
		"groupType": "自助",
		"minGroupSize": 2,
		"maxGroupSize": 6,
		"collectType": "capitation",
		"promotionPrice": 26999,
		"categoryId": "32002001",
		"categoryPath": [
			"32000000",
			"32002000",
			"32002001"
		],
		"validFrom": "2024-06-01",
		"validTo": "2026-05-31",
		"isLongTerm": false,
		"singleRoomSurcharge": "400.00",
		"roomUpgradeSurcharge": "600.00",
		"themesIds": [
			"13",
			"12",
			"11",
			"14",
			"10",
			"9",
			"8",
			"7"
		],
		"consumableDateType": "specificDays",
		"consumableDays": 120,
		"consumableDates": null,
		"purchaseLimitSingle": 1,
		"purchaseLimitPerOrder": 5,
		"purchaseLimitRules": 1,
		"purchaseLimitDetails": "需持有效护照，单人限购2个名额",
		"earliestBookingDays": 30,
		"advanceBookingRequired": 1,
		"advanceBookingRange": "oneDayBefore",
		"advanceBookingTime": 48,
		"advanceBookingTimeUnit": "hours",
		"hasNoCancellationAfterAppointment": 1,
		"refundBeforeAppointment": 1,
		"autoRefundAfterExpiration": 0,
		"hasBreachOfContractTerms": 1,
		"breachOfContractTerms": [
			{
				"days": 30,
				"buyerPenalty": 20,
				"sellerPenalty": 20
			},
			{
				"days": 15,
				"buyerPenalty": 50,
				"sellerPenalty": 50
			},
			{
				"days": 7,
				"buyerPenalty": 80,
				"sellerPenalty": 80
			},
			{
				"days": 3,
				"buyerPenalty": 100,
				"sellerPenalty": 100
			}
		],
		"breachOfContractBase": "amountTotal"
	}
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | 该参数用于表示API请求的状态码，200通常表示请求成功。 |
| message | success | string | 该参数用于返回API请求相关的消息，描述请求执行的结果。 |
| data | {} | object | 该参数用于承载API请求返回的具体数据内容，是请求结果的主体部分。 |
| data.title | 马尔代夫阿雅达岛7日5晚奢华度假之旅 | string | 该参数表示产品的标题，用于简洁概括产品的主要内容。 |
| data.price | 17999 | number | 该参数代表产品的价格，反映购买该产品所需支付的金额。 |
| data.days | 7 | number | 该参数表示整个旅行行程所持续的天数。 |
| data.nights | 5 | number | 该参数代表在旅行期间需要住宿的夜晚数量。 |
| data.destination | 50080018,50080017,50080019,1733684 | string | 该参数以编号形式表示旅行目的地，可包含多个目的地编号。 |
| data.images | [] | array | 该参数用于记录与产品相关的图片信息，可能是图片链接等内容。 |
| data.description | 奢享印度洋上的世外桃源，入住五星级水上别墅，畅游纯净海域，体验浪漫的海底餐厅和SPA。行程包含浮潜、深潜、海钓、海豚巡游等丰富水上活动。 | string | 该参数对产品进行详细描述，介绍产品特色、行程安排等内容。 |
| data.features | 奢华度假、丰富水上活动、优质住宿 | array | 该参数用于突出产品区别于其他产品的独特特点和优势。 |
| data.status | online | string | 该参数表示产品当前所处的状态，如上线、下线等。 |
| data.deposit | 8000 | number | 该参数代表预订产品时需要支付的定金数额。 |
| data.scheduleDays | [] | array | 该参数用于存储整个旅行行程中每天的详细安排信息。 |
| data.scheduleDays.day | 7 | number | 该参数表示行程安排中具体某一天的编号或顺序。 |
| data.scheduleDays.title | 阿雅达岛-马累-北京 | string | 该参数用于概括行程安排中某一天的主要内容或地点信息。 |
| data.scheduleDays.description | 乘坐水上飞机返回马累，转机返回北京。 | string | 该参数对行程安排中某一天的活动、交通等情况进行详细说明。 |
| data.scheduleDays.meetingPoint | 酒店大堂 | string | 该参数表示行程中当天活动的集合地点。 |
| data.scheduleDays.meetingTime | 09:00:00 | string | 该参数代表行程中当天活动的集合具体时间。 |
| data.scheduleDays.schedule | [] | array | 该参数用于存储行程安排中某一天具体的行程细节，如活动、餐饮、住宿等。 |
| data.scheduleDays.schedule.day | 7 | number | 在当日行程详情中，再次明确该行程对应的日数。 |
| data.scheduleDays.schedule.title | 返程航班 | string | 该参数概括当日行程详情的主要内容，如航班、活动等主题。 |
| data.scheduleDays.schedule.description | 乘坐国际航班返回北京。 | string | 该参数对当日行程详情进行详细的文字描述。 |
| data.scheduleDays.schedule.activities | [] | array | 该参数记录行程安排中当天具体的活动项目。 |
| data.scheduleDays.schedule.meals | [] | array | 该参数用于说明行程安排中当天提供的餐饮信息。 |
| data.scheduleDays.schedule.accommodation | [] | string | 该参数表示行程安排中当天的住宿相关信息。 |
| data.scheduleDays.schedule.images | [] | array | 该参数存储行程安排中当天相关的图片信息。 |
| data.scheduleDays.schedule.type | transport | string | 该参数表明行程安排中当天活动的类型，如交通、住宿等。 |
| data.scheduleDays.schedule.startTime | 19:00:00 | string | 该参数代表行程安排中当天某项活动或行程的开始时间。 |
| data.scheduleDays.schedule.mealType | 正餐 | array | 该参数用于明确行程安排中当天餐饮的具体类型。 |
| data.scheduleDays.schedule.adultMealIncluded | 是 | string | 该参数表示行程安排中当天成人餐饮是否包含在费用内。 |
| data.scheduleDays.schedule.childMealIncluded | 是 | string | 该参数表明行程安排中当天儿童餐饮是否包含在费用内。 |
| data.scheduleDays.schedule.mealPrice | 0 | number | 该参数代表行程安排中当天餐饮的价格。 |
| data.scheduleDays.schedule.transportType | 飞机 | string | 该参数说明行程安排中当天所使用的交通方式。 |
| data.scheduleDays.schedule.flightNumber | SQ437+SQ800 | string | 该参数记录行程安排中当天所乘坐航班的航班号码。 |
| data.scheduleDays.schedule.departureCity | 1337624 | string | 该参数以编号形式表示行程安排中当天出发的城市。 |
| data.scheduleDays.schedule.arrivalCity | 110000 | string | 该参数以编号形式表示行程安排中当天到达的城市。 |
| data.scheduleDays.schedule.stayType | 酒店 | string | 该参数表明行程安排中当天住宿的具体类型。 |
| data.scheduleDays.schedule.hotelName | 阿雅达岛酒店 | string | 该参数记录行程安排中当天所入住酒店的名称。 |
| data.scheduleDays.schedule.remark | 经新加坡转机返回北京，结束愉快的马尔代夫之旅。 | string | 该参数对行程安排中当天的特殊情况、注意事项等进行补充说明。 |
| data.scheduleDays.schedule.mealDuration | {} | object | 该参数用于记录行程安排中当天餐饮持续的时间信息。 |
| data.scheduleDays.schedule.mealDuration.hours | 0 | number | 该参数表示行程安排中当天餐饮持续时间的小时数。 |
| data.scheduleDays.schedule.mealDuration.minutes | 0 | number | 该参数代表行程安排中当天餐饮持续时间的分钟数。 |
| data.scheduleDays.schedule.estimatedDuration | {} | object | 该参数用于记录行程安排中当天某项活动或行程预计持续的时间。 |
| data.scheduleDays.schedule.estimatedDuration.hours | 14 | number | 该参数表示行程安排中当天某项活动或行程预计持续时间的小时数。 |
| data.scheduleDays.schedule.estimatedDuration.minutes | 0 | number | 该参数代表行程安排中当天某项活动或行程预计持续时间的分钟数。 |
| data.scheduleDays.schedule.activityDuration | {} | object | 该参数用于记录行程安排中当天某项活动持续的时间信息。 |
| data.scheduleDays.schedule.activityDuration.hours | 14 | number | 该参数表示行程安排中当天某项活动持续时间的小时数。 |
| data.scheduleDays.schedule.activityDuration.minutes | 0 | number | 该参数代表行程安排中当天某项活动持续时间的分钟数。 |
| data.id | 16 | number | 该参数是产品的唯一标识符，用于区分不同的产品。 |
| data.originalPrice | 29999 | number | 该参数表示产品在未进行促销等活动时的原始价格。 |
| data.departureCity | 440100,441900,441300,440300 | string | 该参数以编号集合形式表示产品出发的多个城市。 |
| data.productType | 自由行 | string | 该参数表明产品的类型，如跟团游、自由行等。 |
| data.coverImage | https://images.unsplash.com/photo-1573843981267-be1999ff37cd?q=80&w=1000 | string | 该参数为产品的封面图片链接，用于展示产品形象。 |
| data.imageDescription | 马尔代夫阿雅达岛美景 | array | 该参数对产品相关图片进行文字描述，辅助理解图片内容。 |
| data.travelMode | 休闲度假 | string | 该参数表示产品所提供的旅行方式或主题。 |
| data.groupType | 自助 | string | 该参数说明产品组团的类型，如自助、团队等。 |
| data.minGroupSize | 2 | number | 该参数代表产品组团时的最小人数限制。 |
| data.maxGroupSize | 6 | number | 该参数表示产品组团时的最大人数限制。 |
| data.collectType | capitation | string | 该参数表明产品收费的类型，如按人头收费等方式。 |
| data.promotionPrice | 26999 | number | 该参数代表产品在促销活动期间的价格。 |
| data.categoryId | 32002001 | string | 该参数是产品所属分类的唯一标识符，用于产品分类管理。 |
| data.categoryPath | 旅游度假/海外游/马尔代夫 | array | 该参数表示产品在分类体系中的具体路径，便于定位产品类别。 |
| data.validFrom | 2024-06-01 | string | 该参数代表产品价格、活动等信息的有效开始日期。 |
| data.validTo | 2026-05-31 | string | 该参数表示产品价格、活动等信息的有效结束日期。 |
| data.isLongTerm | false | boolean | 该参数用于判断产品是否属于长期有效的产品类型。 |
| data.singleRoomSurcharge | 400.00 | string | 该参数表示单人入住时需要额外支付的单房差价金额。 |
| data.roomUpgradeSurcharge | 600.00 | string | 该参数代表将房间升级时需要额外支付的差价金额。 |
| data.themesIds | [] | array | 该参数以集合形式存储产品相关的主题ID，用于产品主题分类。 |
| data.consumableDateType | specificDays | string | 该参数表明产品消费日期的类型，如特定日期、时间段等。 |
| data.consumableDays | 120 | number | 该参数代表产品从购买后可进行消费的天数。 |
| data.consumableDates | [] | null | 该参数以集合形式存储产品具体可消费的日期信息。 |
| data.purchaseLimitSingle | 1 | number | 该参数表示每个用户单次购买产品的最大数量限制。 |
| data.purchaseLimitPerOrder | 5 | number | 该参数代表每个订单中可购买产品的最大数量限制。 |
| data.purchaseLimitRules | 1 | number | 该参数是购买限制规则的编号，用于关联具体的限购规则。 |
| data.purchaseLimitDetails | 需持有效护照，单人限购2个名额 | string | 该参数详细说明产品购买限制的具体内容和要求。 |
| data.earliestBookingDays | 30 | number | 该参数表示在出行日期前最早可以预订产品的天数。 |
| data.advanceBookingRequired | 1 | number | 该参数用于判断是否需要提前预订产品，1表示需要。 |
| data.advanceBookingRange | oneDayBefore | string | 该参数表明提前预订产品的时间范围类型，如提前一天等。 |
| data.advanceBookingTime | 48 | number | 该参数代表提前预订产品所需的具体时间数值。 |
| data.advanceBookingTimeUnit | hours | string | 该参数说明提前预订时间的单位，如小时、天等。 |
| data.hasNoCancellationAfterAppointment | 1 | number | 该参数用于判断产品在预约后是否不可以取消，1表示不可取消。 |
| data.refundBeforeAppointment | 1 | number | 该参数用于判断产品在预约前是否可以退款，1表示可以退款。 |
| data.autoRefundAfterExpiration | 0 | number | 该参数用于判断产品过期后是否自动退款，0表示不自动退款。 |
| data.hasBreachOfContractTerms | 1 | number | 该参数用于判断产品是否存在违约相关条款，1表示存在。 |
| data.breachOfContractTerms | {} | array | 该参数用于存储产品违约相关条款的详细内容。 |
| data.breachOfContractTerms.days | 30 | number | 该参数表示违约条款中规定的违约期限天数。 |
| data.breachOfContractTerms.buyerPenalty | 20 | number | 该参数代表买家违约时需支付的违约金比例。 |
| data.breachOfContractTerms.sellerPenalty | 20 | number | 该参数表示卖家违约时需支付的违约金比例。 |
| data.breachOfContractBase | amountTotal | string | 该参数表明违约金计算所依据的基础，如总金额等。 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取配置分组

> 创建人: 江汉波

> 更新人: 江汉波

> 创建时间: 2025-08-07 15:19:33

> 更新时间: 2025-08-11 14:57:46

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system-config/groups

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
	"code": 200,
	"message": "success",
	"data": {
		"title": "马尔代夫阿雅达岛7日5晚奢华度假之旅",
		"price": 17999,
		"days": 7,
		"nights": 5,
		"destination": "50080018,50080017,50080019,1733684",
		"images": [
			"https://images.unsplash.com/photo-1573843981267-be1999ff37cd?q=80&w=1000",
			"https://images.unsplash.com/photo-1514282401047-d79a71a590e8?q=80&w=1000",
			"https://images.unsplash.com/photo-1540202404-a2f29016b523?q=80&w=1000",
			"https://images.unsplash.com/photo-1540202403-b7abd6747a18?q=80&w=1000"
		],
		"description": "奢享印度洋上的世外桃源，入住五星级水上别墅，畅游纯净海域，体验浪漫的海底餐厅和SPA。行程包含浮潜、深潜、海钓、海豚巡游等丰富水上活动。",
		"features": [
			"豪华水上别墅",
			"全程半餐或全餐",
			"内飞+快艇接送",
			"浮潜装备免费使用",
			"赠送一次海底餐厅体验",
			"赠送一次SPA体验"
		],
		"status": "online",
		"deposit": 8000,
		"scheduleDays": [
			{
				"day": 7,
				"title": "阿雅达岛-马累-北京",
				"description": "乘坐水上飞机返回马累，转机返回北京。",
				"meetingPoint": "酒店大堂",
				"meetingTime": "09:00:00",
				"schedule": [
					{
						"day": 7,
						"title": "返程航班",
						"description": "乘坐国际航班返回北京。",
						"activities": [
							"办理登机",
							"登机"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "19:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "飞机",
						"flightNumber": "SQ437+SQ800",
						"departureCity": "1337624",
						"arrivalCity": "110000",
						"stayType": "",
						"hotelName": "",
						"remark": "经新加坡转机返回北京，结束愉快的马尔代夫之旅。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 14,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 14,
							"minutes": 0
						}
					},
					{
						"day": 7,
						"title": "前往机场",
						"description": "前往马累国际机场。",
						"activities": [],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "16:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "专车",
						"flightNumber": "",
						"departureCity": "马累市区",
						"arrivalCity": "马累机场",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 30
						}
					},
					{
						"day": 7,
						"title": "午餐",
						"description": "在马累市区享用午餐。",
						"activities": [],
						"meals": [
							"马尔代夫特色海鲜"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "13:00:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 80,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 7,
						"title": "马累自由活动",
						"description": "在马累自由活动，购买纪念品。",
						"activities": [
							"购物",
							"市区游览"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "12:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "可在当地市场购买纪念品。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 3,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 3,
							"minutes": 0
						}
					},
					{
						"day": 7,
						"title": "前往马累",
						"description": "乘坐水上飞机返回马累。",
						"activities": [],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "11:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "水上飞机",
						"flightNumber": "",
						"departureCity": "阿雅达岛",
						"arrivalCity": "1337624",
						"stayType": "",
						"hotelName": "",
						"remark": "欣赏马尔代夫群岛鸟瞰美景。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 1,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 7,
						"title": "退房",
						"description": "办理退房手续，行李寄存。",
						"activities": [
							"退房",
							"行李寄存"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "accommodation",
						"startTime": "09:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 30
						}
					},
					{
						"day": 7,
						"title": "早餐",
						"description": "享用最后一顿岛上早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "07:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					}
				]
			},
			{
				"day": 6,
				"title": "阿雅达岛最后一天",
				"description": "自由活动，尽情享受马尔代夫的阳光和海滩。",
				"meetingPoint": "岛上接待中心",
				"meetingTime": "08:00:00",
				"schedule": [
					{
						"day": 6,
						"title": "告别晚宴",
						"description": "享用特别安排的私人沙滩烛光晚宴。",
						"activities": [],
						"meals": [
							"龙虾",
							"高级海鲜",
							"香槟"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:00:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "在星空下的沙滩享用浪漫的告别晚餐。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 30
						}
					},
					{
						"day": 6,
						"title": "继续自由活动",
						"description": "下午继续自由活动。",
						"activities": [
							"水上活动",
							"沙滩活动"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "14:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 4,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 4,
							"minutes": 0
						}
					},
					{
						"day": 6,
						"title": "午餐",
						"description": "在岛上餐厅享用午餐。",
						"activities": [],
						"meals": [
							"国际自助餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "12:30:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 6,
						"title": "自由活动",
						"description": "全天自由活动，尽情享受岛上设施。",
						"activities": [
							"游泳",
							"潜水",
							"日光浴",
							"沙滩活动"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "09:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "最后一天充分享受马尔代夫的阳光、沙滩和海水。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 5,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 5,
							"minutes": 0
						}
					},
					{
						"day": 6,
						"title": "早餐",
						"description": "在别墅内或餐厅享用早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "08:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					}
				]
			},
			{
				"day": 5,
				"title": "SPA及海钓体验",
				"description": "上午SPA护理，下午海钓体验。",
				"meetingPoint": "岛上接待中心",
				"meetingTime": "09:00:00",
				"schedule": [
					{
						"day": 5,
						"title": "晚餐",
						"description": "享用自己钓到的海鲜晚餐。",
						"activities": [],
						"meals": [
							"新鲜海鲜",
							"本地特色烹饪"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:00:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "厨师将根据您的喜好烹饪您钓到的海鱼。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 5,
						"title": "海钓体验",
						"description": "参加专业向导带领的海钓活动。",
						"activities": [
							"海钓",
							"日落观赏"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "15:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "体验马尔代夫传统海钓，可能钓到金枪鱼、旗鱼等。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 3,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 3,
							"minutes": 0
						}
					},
					{
						"day": 5,
						"title": "午餐",
						"description": "在岛上餐厅享用午餐。",
						"activities": [],
						"meals": [
							"轻食午餐",
							"热带水果"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "13:00:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					},
					{
						"day": 5,
						"title": "SPA体验",
						"description": "享受特色马尔代夫SPA理疗。",
						"activities": [
							"精油按摩",
							"面部护理",
							"身体护理"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "10:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "在海边的SPA亭享受专业理疗服务。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 2,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 5,
						"title": "早餐",
						"description": "在别墅内或餐厅享用早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "08:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					}
				]
			},
			{
				"day": 4,
				"title": "海底餐厅体验",
				"description": "早晚自由活动，中午特别安排海底餐厅用餐体验。",
				"meetingPoint": "岛上接待中心",
				"meetingTime": "08:00:00",
				"schedule": [
					{
						"day": 4,
						"title": "晚餐",
						"description": "在主餐厅享用晚餐。",
						"activities": [],
						"meals": [
							"国际美食",
							"特色甜点"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:30:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 4,
						"title": "自由活动",
						"description": "下午自由活动时间。",
						"activities": [
							"深潜",
							"海钓",
							"水上摩托"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "15:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "可选择参加度假村提供的各种活动，部分活动需额外付费。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 4,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 4,
							"minutes": 0
						}
					},
					{
						"day": 4,
						"title": "海底餐厅体验",
						"description": "在世界著名的海底餐厅享用午餐。",
						"activities": [],
						"meals": [
							"高级海鲜",
							"精选葡萄酒"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "12:30:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "在水下5米处的餐厅用餐，欣赏海底景观。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 4,
						"title": "自由活动",
						"description": "上午自由活动时间。",
						"activities": [
							"游泳",
							"日光浴",
							"按摩"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "09:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 2,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 30
						}
					},
					{
						"day": 4,
						"title": "早餐",
						"description": "在别墅内或餐厅享用早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "08:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					}
				]
			},
			{
				"day": 3,
				"title": "阿雅达岛休闲日",
				"description": "自由活动，可选择浮潜、海滩休闲等。",
				"meetingPoint": "岛上接待中心",
				"meetingTime": "08:00:00",
				"schedule": [
					{
						"day": 3,
						"title": "晚餐",
						"description": "在岛上餐厅享用晚餐。",
						"activities": [],
						"meals": [
							"国际美食",
							"本地特色菜"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:00:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 3,
						"title": "自由活动",
						"description": "自由时间，可选择各种水上活动。",
						"activities": [
							"皮划艇",
							"帆板",
							"沙滩排球"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "14:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "可选择参加度假村提供的各种活动，部分活动需额外付费。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 5,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 5,
							"minutes": 0
						}
					},
					{
						"day": 3,
						"title": "午餐",
						"description": "回到岛上享用午餐。",
						"activities": [],
						"meals": [
							"国际自助餐",
							"本地特色菜"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "12:30:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 3,
						"title": "浮潜体验",
						"description": "参加专业教练指导的浮潜活动。",
						"activities": [
							"浮潜装备",
							"珊瑚观赏",
							"热带鱼观赏"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "10:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "探索马尔代夫丰富的海底世界。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 2,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 3,
						"title": "早餐",
						"description": "在别墅内或餐厅享用早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "07:30:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "可选择在别墅内享用早餐服务。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					}
				]
			},
			{
				"day": 2,
				"title": "马累-阿雅达岛",
				"description": "乘坐水上飞机或快艇前往阿雅达岛，入住水上别墅。",
				"meetingPoint": "马累机场接待大厅",
				"meetingTime": "12:00:00",
				"schedule": [
					{
						"day": 2,
						"title": "欢迎晚宴",
						"description": "在沙滩餐厅享用特色晚餐。",
						"activities": [],
						"meals": [
							"烧烤海鲜",
							"热带饮品"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:00:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "在星空下享用浪漫晚餐。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "自由活动",
						"description": "自由时间，可在私人泳池或海滩放松。",
						"activities": [
							"游泳",
							"日光浴"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "16:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "享受马尔代夫的阳光与海滩。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 2,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "度假村介绍",
						"description": "参加度假村介绍会，了解各项设施与活动。",
						"activities": [
							"设施介绍",
							"活动预订"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "14:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "了解度假村提供的各项娱乐活动与服务。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 1,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "午餐",
						"description": "在岛上餐厅享用午餐。",
						"activities": [],
						"meals": [
							"海鲜自助餐",
							"热带水果"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "12:30:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "品尝新鲜的马尔代夫特色美食。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 2,
						"title": "入住水上别墅",
						"description": "入住豪华水上别墅，办理入住手续。",
						"activities": [
							"办理入住",
							"行李服务"
						],
						"meals": [],
						"accommodation": "阿雅达岛水上别墅",
						"images": [],
						"type": "accommodation",
						"startTime": "11:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "水上别墅",
						"hotelName": "Ayada Maldives Water Villa",
						"remark": "豪华水上别墅，配有私人露台和直接入海通道。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "抵达阿雅达岛",
						"description": "抵达阿雅达岛，工作人员迎接。",
						"activities": [
							"迎宾仪式",
							"椰子欢迎饮料"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "10:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "体验马尔代夫传统迎宾仪式。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 30
						}
					},
					{
						"day": 2,
						"title": "前往阿雅达岛",
						"description": "乘坐水上飞机前往阿雅达岛。",
						"activities": [
							"退房",
							"前往码头",
							"水上飞机"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "09:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "水上飞机",
						"flightNumber": "",
						"departureCity": "1337624",
						"arrivalCity": "阿雅达岛",
						"stayType": "",
						"hotelName": "",
						"remark": "欣赏马尔代夫环礁鸟瞰美景。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 1,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "早餐",
						"description": "酒店内享用早餐。",
						"activities": [],
						"meals": [
							"马尔代夫早餐",
							"热带水果"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "07:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					}
				]
			},
			{
				"day": 1,
				"title": "北京-马累",
				"description": "国际航班飞往马尔代夫首都马累。",
				"meetingPoint": "北京首都国际机场T3航站楼",
				"meetingTime": "10:00:00",
				"schedule": [
					{
						"day": 1,
						"title": "入住马累酒店",
						"description": "入住马累市区酒店，短暂休息。",
						"activities": [
							"办理入住",
							"休息"
						],
						"meals": [],
						"accommodation": "马累市区酒店",
						"images": [],
						"type": "accommodation",
						"startTime": "23:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "酒店",
						"hotelName": "Hotel Jen Malé",
						"remark": "马累市区豪华酒店，为第二天前往度假岛做准备。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 0
						}
					},
					{
						"day": 1,
						"title": "抵达马累",
						"description": "抵达马累国际机场，专人接机。",
						"activities": [
							"接机",
							"办理入境"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "22:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "导游将在机场出口处举\"马尔代夫度假\"接机牌。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 1,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					},
					{
						"day": 1,
						"title": "北京-马累",
						"description": "乘坐国际航班前往马尔代夫首都马累。",
						"activities": [
							"办理登机手续",
							"登机"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "10:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "飞机",
						"flightNumber": "SQ801+SQ436",
						"departureCity": "110000",
						"arrivalCity": "1337624",
						"stayType": "",
						"hotelName": "",
						"remark": "请提前3小时到达机场办理登机手续，经新加坡转机。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 12,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 12,
							"minutes": 0
						}
					}
				]
			}
		],
		"id": 16,
		"originalPrice": 29999,
		"departureCity": "440100,441900,441300,440300",
		"productType": "自由行",
		"coverImage": "https://images.unsplash.com/photo-1573843981267-be1999ff37cd?q=80&w=1000",
		"imageDescription": [
			"https://images.unsplash.com/photo-1512100356356-de1b84283e18?q=80&w=2000&auto=format",
			"https://images.unsplash.com/photo-1586861256632-42e108743990?q=80&w=2000&auto=format",
			"https://images.unsplash.com/photo-1514282401047-d79a71a590e8?q=80&w=2000&auto=format",
			"https://images.unsplash.com/photo-1602157947535-51a09392fcf0?q=80&w=2000&auto=format"
		],
		"travelMode": "休闲度假",
		"groupType": "自助",
		"minGroupSize": 2,
		"maxGroupSize": 6,
		"collectType": "capitation",
		"promotionPrice": 26999,
		"categoryId": "32002001",
		"categoryPath": [
			"32000000",
			"32002000",
			"32002001"
		],
		"validFrom": "2024-06-01",
		"validTo": "2026-05-31",
		"isLongTerm": false,
		"singleRoomSurcharge": "400.00",
		"roomUpgradeSurcharge": "600.00",
		"themesIds": [
			"13",
			"12",
			"11",
			"14",
			"10",
			"9",
			"8",
			"7"
		],
		"consumableDateType": "specificDays",
		"consumableDays": 120,
		"consumableDates": null,
		"purchaseLimitSingle": 1,
		"purchaseLimitPerOrder": 5,
		"purchaseLimitRules": 1,
		"purchaseLimitDetails": "需持有效护照，单人限购2个名额",
		"earliestBookingDays": 30,
		"advanceBookingRequired": 1,
		"advanceBookingRange": "oneDayBefore",
		"advanceBookingTime": 48,
		"advanceBookingTimeUnit": "hours",
		"hasNoCancellationAfterAppointment": 1,
		"refundBeforeAppointment": 1,
		"autoRefundAfterExpiration": 0,
		"hasBreachOfContractTerms": 1,
		"breachOfContractTerms": [
			{
				"days": 30,
				"buyerPenalty": 20,
				"sellerPenalty": 20
			},
			{
				"days": 15,
				"buyerPenalty": 50,
				"sellerPenalty": 50
			},
			{
				"days": 7,
				"buyerPenalty": 80,
				"sellerPenalty": 80
			},
			{
				"days": 3,
				"buyerPenalty": 100,
				"sellerPenalty": 100
			}
		],
		"breachOfContractBase": "amountTotal"
	}
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | 该参数用于表示API请求的状态码，200通常表示请求成功。 |
| message | success | string | 该参数用于返回API请求相关的消息，描述请求执行的结果。 |
| data | {} | object | 该参数用于承载API请求返回的具体数据内容，是请求结果的主体部分。 |
| data.title | 马尔代夫阿雅达岛7日5晚奢华度假之旅 | string | 该参数表示产品的标题，用于简洁概括产品的主要内容。 |
| data.price | 17999 | number | 该参数代表产品的价格，反映购买该产品所需支付的金额。 |
| data.days | 7 | number | 该参数表示整个旅行行程所持续的天数。 |
| data.nights | 5 | number | 该参数代表在旅行期间需要住宿的夜晚数量。 |
| data.destination | 50080018,50080017,50080019,1733684 | string | 该参数以编号形式表示旅行目的地，可包含多个目的地编号。 |
| data.images | [] | array | 该参数用于记录与产品相关的图片信息，可能是图片链接等内容。 |
| data.description | 奢享印度洋上的世外桃源，入住五星级水上别墅，畅游纯净海域，体验浪漫的海底餐厅和SPA。行程包含浮潜、深潜、海钓、海豚巡游等丰富水上活动。 | string | 该参数对产品进行详细描述，介绍产品特色、行程安排等内容。 |
| data.features | 奢华度假、丰富水上活动、优质住宿 | array | 该参数用于突出产品区别于其他产品的独特特点和优势。 |
| data.status | online | string | 该参数表示产品当前所处的状态，如上线、下线等。 |
| data.deposit | 8000 | number | 该参数代表预订产品时需要支付的定金数额。 |
| data.scheduleDays | [] | array | 该参数用于存储整个旅行行程中每天的详细安排信息。 |
| data.scheduleDays.day | 7 | number | 该参数表示行程安排中具体某一天的编号或顺序。 |
| data.scheduleDays.title | 阿雅达岛-马累-北京 | string | 该参数用于概括行程安排中某一天的主要内容或地点信息。 |
| data.scheduleDays.description | 乘坐水上飞机返回马累，转机返回北京。 | string | 该参数对行程安排中某一天的活动、交通等情况进行详细说明。 |
| data.scheduleDays.meetingPoint | 酒店大堂 | string | 该参数表示行程中当天活动的集合地点。 |
| data.scheduleDays.meetingTime | 09:00:00 | string | 该参数代表行程中当天活动的集合具体时间。 |
| data.scheduleDays.schedule | [] | array | 该参数用于存储行程安排中某一天具体的行程细节，如活动、餐饮、住宿等。 |
| data.scheduleDays.schedule.day | 7 | number | 在当日行程详情中，再次明确该行程对应的日数。 |
| data.scheduleDays.schedule.title | 返程航班 | string | 该参数概括当日行程详情的主要内容，如航班、活动等主题。 |
| data.scheduleDays.schedule.description | 乘坐国际航班返回北京。 | string | 该参数对当日行程详情进行详细的文字描述。 |
| data.scheduleDays.schedule.activities | [] | array | 该参数记录行程安排中当天具体的活动项目。 |
| data.scheduleDays.schedule.meals | [] | array | 该参数用于说明行程安排中当天提供的餐饮信息。 |
| data.scheduleDays.schedule.accommodation | [] | string | 该参数表示行程安排中当天的住宿相关信息。 |
| data.scheduleDays.schedule.images | [] | array | 该参数存储行程安排中当天相关的图片信息。 |
| data.scheduleDays.schedule.type | transport | string | 该参数表明行程安排中当天活动的类型，如交通、住宿等。 |
| data.scheduleDays.schedule.startTime | 19:00:00 | string | 该参数代表行程安排中当天某项活动或行程的开始时间。 |
| data.scheduleDays.schedule.mealType | 正餐 | array | 该参数用于明确行程安排中当天餐饮的具体类型。 |
| data.scheduleDays.schedule.adultMealIncluded | 是 | string | 该参数表示行程安排中当天成人餐饮是否包含在费用内。 |
| data.scheduleDays.schedule.childMealIncluded | 是 | string | 该参数表明行程安排中当天儿童餐饮是否包含在费用内。 |
| data.scheduleDays.schedule.mealPrice | 0 | number | 该参数代表行程安排中当天餐饮的价格。 |
| data.scheduleDays.schedule.transportType | 飞机 | string | 该参数说明行程安排中当天所使用的交通方式。 |
| data.scheduleDays.schedule.flightNumber | SQ437+SQ800 | string | 该参数记录行程安排中当天所乘坐航班的航班号码。 |
| data.scheduleDays.schedule.departureCity | 1337624 | string | 该参数以编号形式表示行程安排中当天出发的城市。 |
| data.scheduleDays.schedule.arrivalCity | 110000 | string | 该参数以编号形式表示行程安排中当天到达的城市。 |
| data.scheduleDays.schedule.stayType | 酒店 | string | 该参数表明行程安排中当天住宿的具体类型。 |
| data.scheduleDays.schedule.hotelName | 阿雅达岛酒店 | string | 该参数记录行程安排中当天所入住酒店的名称。 |
| data.scheduleDays.schedule.remark | 经新加坡转机返回北京，结束愉快的马尔代夫之旅。 | string | 该参数对行程安排中当天的特殊情况、注意事项等进行补充说明。 |
| data.scheduleDays.schedule.mealDuration | {} | object | 该参数用于记录行程安排中当天餐饮持续的时间信息。 |
| data.scheduleDays.schedule.mealDuration.hours | 0 | number | 该参数表示行程安排中当天餐饮持续时间的小时数。 |
| data.scheduleDays.schedule.mealDuration.minutes | 0 | number | 该参数代表行程安排中当天餐饮持续时间的分钟数。 |
| data.scheduleDays.schedule.estimatedDuration | {} | object | 该参数用于记录行程安排中当天某项活动或行程预计持续的时间。 |
| data.scheduleDays.schedule.estimatedDuration.hours | 14 | number | 该参数表示行程安排中当天某项活动或行程预计持续时间的小时数。 |
| data.scheduleDays.schedule.estimatedDuration.minutes | 0 | number | 该参数代表行程安排中当天某项活动或行程预计持续时间的分钟数。 |
| data.scheduleDays.schedule.activityDuration | {} | object | 该参数用于记录行程安排中当天某项活动持续的时间信息。 |
| data.scheduleDays.schedule.activityDuration.hours | 14 | number | 该参数表示行程安排中当天某项活动持续时间的小时数。 |
| data.scheduleDays.schedule.activityDuration.minutes | 0 | number | 该参数代表行程安排中当天某项活动持续时间的分钟数。 |
| data.id | 16 | number | 该参数是产品的唯一标识符，用于区分不同的产品。 |
| data.originalPrice | 29999 | number | 该参数表示产品在未进行促销等活动时的原始价格。 |
| data.departureCity | 440100,441900,441300,440300 | string | 该参数以编号集合形式表示产品出发的多个城市。 |
| data.productType | 自由行 | string | 该参数表明产品的类型，如跟团游、自由行等。 |
| data.coverImage | https://images.unsplash.com/photo-1573843981267-be1999ff37cd?q=80&w=1000 | string | 该参数为产品的封面图片链接，用于展示产品形象。 |
| data.imageDescription | 马尔代夫阿雅达岛美景 | array | 该参数对产品相关图片进行文字描述，辅助理解图片内容。 |
| data.travelMode | 休闲度假 | string | 该参数表示产品所提供的旅行方式或主题。 |
| data.groupType | 自助 | string | 该参数说明产品组团的类型，如自助、团队等。 |
| data.minGroupSize | 2 | number | 该参数代表产品组团时的最小人数限制。 |
| data.maxGroupSize | 6 | number | 该参数表示产品组团时的最大人数限制。 |
| data.collectType | capitation | string | 该参数表明产品收费的类型，如按人头收费等方式。 |
| data.promotionPrice | 26999 | number | 该参数代表产品在促销活动期间的价格。 |
| data.categoryId | 32002001 | string | 该参数是产品所属分类的唯一标识符，用于产品分类管理。 |
| data.categoryPath | 旅游度假/海外游/马尔代夫 | array | 该参数表示产品在分类体系中的具体路径，便于定位产品类别。 |
| data.validFrom | 2024-06-01 | string | 该参数代表产品价格、活动等信息的有效开始日期。 |
| data.validTo | 2026-05-31 | string | 该参数表示产品价格、活动等信息的有效结束日期。 |
| data.isLongTerm | false | boolean | 该参数用于判断产品是否属于长期有效的产品类型。 |
| data.singleRoomSurcharge | 400.00 | string | 该参数表示单人入住时需要额外支付的单房差价金额。 |
| data.roomUpgradeSurcharge | 600.00 | string | 该参数代表将房间升级时需要额外支付的差价金额。 |
| data.themesIds | [] | array | 该参数以集合形式存储产品相关的主题ID，用于产品主题分类。 |
| data.consumableDateType | specificDays | string | 该参数表明产品消费日期的类型，如特定日期、时间段等。 |
| data.consumableDays | 120 | number | 该参数代表产品从购买后可进行消费的天数。 |
| data.consumableDates | [] | null | 该参数以集合形式存储产品具体可消费的日期信息。 |
| data.purchaseLimitSingle | 1 | number | 该参数表示每个用户单次购买产品的最大数量限制。 |
| data.purchaseLimitPerOrder | 5 | number | 该参数代表每个订单中可购买产品的最大数量限制。 |
| data.purchaseLimitRules | 1 | number | 该参数是购买限制规则的编号，用于关联具体的限购规则。 |
| data.purchaseLimitDetails | 需持有效护照，单人限购2个名额 | string | 该参数详细说明产品购买限制的具体内容和要求。 |
| data.earliestBookingDays | 30 | number | 该参数表示在出行日期前最早可以预订产品的天数。 |
| data.advanceBookingRequired | 1 | number | 该参数用于判断是否需要提前预订产品，1表示需要。 |
| data.advanceBookingRange | oneDayBefore | string | 该参数表明提前预订产品的时间范围类型，如提前一天等。 |
| data.advanceBookingTime | 48 | number | 该参数代表提前预订产品所需的具体时间数值。 |
| data.advanceBookingTimeUnit | hours | string | 该参数说明提前预订时间的单位，如小时、天等。 |
| data.hasNoCancellationAfterAppointment | 1 | number | 该参数用于判断产品在预约后是否不可以取消，1表示不可取消。 |
| data.refundBeforeAppointment | 1 | number | 该参数用于判断产品在预约前是否可以退款，1表示可以退款。 |
| data.autoRefundAfterExpiration | 0 | number | 该参数用于判断产品过期后是否自动退款，0表示不自动退款。 |
| data.hasBreachOfContractTerms | 1 | number | 该参数用于判断产品是否存在违约相关条款，1表示存在。 |
| data.breachOfContractTerms | {} | array | 该参数用于存储产品违约相关条款的详细内容。 |
| data.breachOfContractTerms.days | 30 | number | 该参数表示违约条款中规定的违约期限天数。 |
| data.breachOfContractTerms.buyerPenalty | 20 | number | 该参数代表买家违约时需支付的违约金比例。 |
| data.breachOfContractTerms.sellerPenalty | 20 | number | 该参数表示卖家违约时需支付的违约金比例。 |
| data.breachOfContractBase | amountTotal | string | 该参数表明违约金计算所依据的基础，如总金额等。 |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取配置类型

> 创建人: 江汉波

> 更新人: 江汉波

> 创建时间: 2025-08-07 15:39:23

> 更新时间: 2025-08-11 15:00:50

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system-config/types

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{"code":200,"message":"success","data":{"types":[{"value":"string","label":"字符串"},{"value":"integer","label":"整数"},{"value":"float","label":"浮点数"},{"value":"boolean","label":"布尔值"},{"value":"json","label":"JSON"},{"value":"array","label":"数组"},{"value":"email","label":"邮箱"},{"value":"url","label":"URL"},{"value":"password","label":"密码"},{"value":"text","label":"文本"},{"value":"select","label":"下拉选择"},{"value":"multiselect","label":"多选"},{"value":"radio","label":"单选"},{"value":"checkbox","label":"复选框"},{"value":"date","label":"日期"},{"value":"datetime","label":"日期时间"},{"value":"time","label":"时间"},{"value":"file","label":"文件"},{"value":"image","label":"图片"}],"total":19}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | 该参数用于表示API请求的状态码，200通常表示请求成功。 |
| message | success | string | 该参数用于返回API请求相关的消息，描述请求执行的结果。 |
| data | - | object | 该参数用于承载API请求返回的具体数据内容，是请求结果的主体部分。 |
| data.types | - | array | - |
| data.types.value | string | string | - |
| data.types.label | 字符串 | string | - |
| data.total | 19 | number | - |

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 获取公开配置

> 创建人: 江汉波

> 更新人: 江汉波

> 创建时间: 2025-08-07 15:39:51

> 更新时间: 2025-08-11 15:01:01

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /system-config/public

**请求方式**

> GET

**Content-Type**

> none

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
	"code": 200,
	"message": "success",
	"data": {
		"title": "马尔代夫阿雅达岛7日5晚奢华度假之旅",
		"price": 17999,
		"days": 7,
		"nights": 5,
		"destination": "50080018,50080017,50080019,1733684",
		"images": [
			"https://images.unsplash.com/photo-1573843981267-be1999ff37cd?q=80&w=1000",
			"https://images.unsplash.com/photo-1514282401047-d79a71a590e8?q=80&w=1000",
			"https://images.unsplash.com/photo-1540202404-a2f29016b523?q=80&w=1000",
			"https://images.unsplash.com/photo-1540202403-b7abd6747a18?q=80&w=1000"
		],
		"description": "奢享印度洋上的世外桃源，入住五星级水上别墅，畅游纯净海域，体验浪漫的海底餐厅和SPA。行程包含浮潜、深潜、海钓、海豚巡游等丰富水上活动。",
		"features": [
			"豪华水上别墅",
			"全程半餐或全餐",
			"内飞+快艇接送",
			"浮潜装备免费使用",
			"赠送一次海底餐厅体验",
			"赠送一次SPA体验"
		],
		"status": "online",
		"deposit": 8000,
		"scheduleDays": [
			{
				"day": 7,
				"title": "阿雅达岛-马累-北京",
				"description": "乘坐水上飞机返回马累，转机返回北京。",
				"meetingPoint": "酒店大堂",
				"meetingTime": "09:00:00",
				"schedule": [
					{
						"day": 7,
						"title": "返程航班",
						"description": "乘坐国际航班返回北京。",
						"activities": [
							"办理登机",
							"登机"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "19:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "飞机",
						"flightNumber": "SQ437+SQ800",
						"departureCity": "1337624",
						"arrivalCity": "110000",
						"stayType": "",
						"hotelName": "",
						"remark": "经新加坡转机返回北京，结束愉快的马尔代夫之旅。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 14,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 14,
							"minutes": 0
						}
					},
					{
						"day": 7,
						"title": "前往机场",
						"description": "前往马累国际机场。",
						"activities": [],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "16:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "专车",
						"flightNumber": "",
						"departureCity": "马累市区",
						"arrivalCity": "马累机场",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 30
						}
					},
					{
						"day": 7,
						"title": "午餐",
						"description": "在马累市区享用午餐。",
						"activities": [],
						"meals": [
							"马尔代夫特色海鲜"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "13:00:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 80,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 7,
						"title": "马累自由活动",
						"description": "在马累自由活动，购买纪念品。",
						"activities": [
							"购物",
							"市区游览"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "12:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "可在当地市场购买纪念品。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 3,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 3,
							"minutes": 0
						}
					},
					{
						"day": 7,
						"title": "前往马累",
						"description": "乘坐水上飞机返回马累。",
						"activities": [],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "11:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "水上飞机",
						"flightNumber": "",
						"departureCity": "阿雅达岛",
						"arrivalCity": "1337624",
						"stayType": "",
						"hotelName": "",
						"remark": "欣赏马尔代夫群岛鸟瞰美景。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 1,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 7,
						"title": "退房",
						"description": "办理退房手续，行李寄存。",
						"activities": [
							"退房",
							"行李寄存"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "accommodation",
						"startTime": "09:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 30
						}
					},
					{
						"day": 7,
						"title": "早餐",
						"description": "享用最后一顿岛上早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "07:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					}
				]
			},
			{
				"day": 6,
				"title": "阿雅达岛最后一天",
				"description": "自由活动，尽情享受马尔代夫的阳光和海滩。",
				"meetingPoint": "岛上接待中心",
				"meetingTime": "08:00:00",
				"schedule": [
					{
						"day": 6,
						"title": "告别晚宴",
						"description": "享用特别安排的私人沙滩烛光晚宴。",
						"activities": [],
						"meals": [
							"龙虾",
							"高级海鲜",
							"香槟"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:00:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "在星空下的沙滩享用浪漫的告别晚餐。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 30
						}
					},
					{
						"day": 6,
						"title": "继续自由活动",
						"description": "下午继续自由活动。",
						"activities": [
							"水上活动",
							"沙滩活动"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "14:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 4,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 4,
							"minutes": 0
						}
					},
					{
						"day": 6,
						"title": "午餐",
						"description": "在岛上餐厅享用午餐。",
						"activities": [],
						"meals": [
							"国际自助餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "12:30:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 6,
						"title": "自由活动",
						"description": "全天自由活动，尽情享受岛上设施。",
						"activities": [
							"游泳",
							"潜水",
							"日光浴",
							"沙滩活动"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "09:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "最后一天充分享受马尔代夫的阳光、沙滩和海水。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 5,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 5,
							"minutes": 0
						}
					},
					{
						"day": 6,
						"title": "早餐",
						"description": "在别墅内或餐厅享用早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "08:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					}
				]
			},
			{
				"day": 5,
				"title": "SPA及海钓体验",
				"description": "上午SPA护理，下午海钓体验。",
				"meetingPoint": "岛上接待中心",
				"meetingTime": "09:00:00",
				"schedule": [
					{
						"day": 5,
						"title": "晚餐",
						"description": "享用自己钓到的海鲜晚餐。",
						"activities": [],
						"meals": [
							"新鲜海鲜",
							"本地特色烹饪"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:00:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "厨师将根据您的喜好烹饪您钓到的海鱼。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 5,
						"title": "海钓体验",
						"description": "参加专业向导带领的海钓活动。",
						"activities": [
							"海钓",
							"日落观赏"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "15:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "体验马尔代夫传统海钓，可能钓到金枪鱼、旗鱼等。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 3,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 3,
							"minutes": 0
						}
					},
					{
						"day": 5,
						"title": "午餐",
						"description": "在岛上餐厅享用午餐。",
						"activities": [],
						"meals": [
							"轻食午餐",
							"热带水果"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "13:00:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					},
					{
						"day": 5,
						"title": "SPA体验",
						"description": "享受特色马尔代夫SPA理疗。",
						"activities": [
							"精油按摩",
							"面部护理",
							"身体护理"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "10:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "在海边的SPA亭享受专业理疗服务。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 2,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 5,
						"title": "早餐",
						"description": "在别墅内或餐厅享用早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "08:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					}
				]
			},
			{
				"day": 4,
				"title": "海底餐厅体验",
				"description": "早晚自由活动，中午特别安排海底餐厅用餐体验。",
				"meetingPoint": "岛上接待中心",
				"meetingTime": "08:00:00",
				"schedule": [
					{
						"day": 4,
						"title": "晚餐",
						"description": "在主餐厅享用晚餐。",
						"activities": [],
						"meals": [
							"国际美食",
							"特色甜点"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:30:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 4,
						"title": "自由活动",
						"description": "下午自由活动时间。",
						"activities": [
							"深潜",
							"海钓",
							"水上摩托"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "15:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "可选择参加度假村提供的各种活动，部分活动需额外付费。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 4,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 4,
							"minutes": 0
						}
					},
					{
						"day": 4,
						"title": "海底餐厅体验",
						"description": "在世界著名的海底餐厅享用午餐。",
						"activities": [],
						"meals": [
							"高级海鲜",
							"精选葡萄酒"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "12:30:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "在水下5米处的餐厅用餐，欣赏海底景观。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 4,
						"title": "自由活动",
						"description": "上午自由活动时间。",
						"activities": [
							"游泳",
							"日光浴",
							"按摩"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "09:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 2,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 30
						}
					},
					{
						"day": 4,
						"title": "早餐",
						"description": "在别墅内或餐厅享用早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "08:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					}
				]
			},
			{
				"day": 3,
				"title": "阿雅达岛休闲日",
				"description": "自由活动，可选择浮潜、海滩休闲等。",
				"meetingPoint": "岛上接待中心",
				"meetingTime": "08:00:00",
				"schedule": [
					{
						"day": 3,
						"title": "晚餐",
						"description": "在岛上餐厅享用晚餐。",
						"activities": [],
						"meals": [
							"国际美食",
							"本地特色菜"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:00:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 3,
						"title": "自由活动",
						"description": "自由时间，可选择各种水上活动。",
						"activities": [
							"皮划艇",
							"帆板",
							"沙滩排球"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "14:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "可选择参加度假村提供的各种活动，部分活动需额外付费。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 5,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 5,
							"minutes": 0
						}
					},
					{
						"day": 3,
						"title": "午餐",
						"description": "回到岛上享用午餐。",
						"activities": [],
						"meals": [
							"国际自助餐",
							"本地特色菜"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "12:30:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 3,
						"title": "浮潜体验",
						"description": "参加专业教练指导的浮潜活动。",
						"activities": [
							"浮潜装备",
							"珊瑚观赏",
							"热带鱼观赏"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "10:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "探索马尔代夫丰富的海底世界。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 2,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 3,
						"title": "早餐",
						"description": "在别墅内或餐厅享用早餐。",
						"activities": [],
						"meals": [
							"热带水果",
							"国际自助早餐"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "07:30:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "可选择在别墅内享用早餐服务。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					}
				]
			},
			{
				"day": 2,
				"title": "马累-阿雅达岛",
				"description": "乘坐水上飞机或快艇前往阿雅达岛，入住水上别墅。",
				"meetingPoint": "马累机场接待大厅",
				"meetingTime": "12:00:00",
				"schedule": [
					{
						"day": 2,
						"title": "欢迎晚宴",
						"description": "在沙滩餐厅享用特色晚餐。",
						"activities": [],
						"meals": [
							"烧烤海鲜",
							"热带饮品"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "19:00:00",
						"mealType": [
							"dinner"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "在星空下享用浪漫晚餐。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "自由活动",
						"description": "自由时间，可在私人泳池或海滩放松。",
						"activities": [
							"游泳",
							"日光浴"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "freeActivity",
						"startTime": "16:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "享受马尔代夫的阳光与海滩。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 2,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 2,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "度假村介绍",
						"description": "参加度假村介绍会，了解各项设施与活动。",
						"activities": [
							"设施介绍",
							"活动预订"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "14:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "了解度假村提供的各项娱乐活动与服务。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 1,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "午餐",
						"description": "在岛上餐厅享用午餐。",
						"activities": [],
						"meals": [
							"海鲜自助餐",
							"热带水果"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "12:30:00",
						"mealType": [
							"lunch"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "品尝新鲜的马尔代夫特色美食。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 30
						}
					},
					{
						"day": 2,
						"title": "入住水上别墅",
						"description": "入住豪华水上别墅，办理入住手续。",
						"activities": [
							"办理入住",
							"行李服务"
						],
						"meals": [],
						"accommodation": "阿雅达岛水上别墅",
						"images": [],
						"type": "accommodation",
						"startTime": "11:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "水上别墅",
						"hotelName": "Ayada Maldives Water Villa",
						"remark": "豪华水上别墅，配有私人露台和直接入海通道。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "抵达阿雅达岛",
						"description": "抵达阿雅达岛，工作人员迎接。",
						"activities": [
							"迎宾仪式",
							"椰子欢迎饮料"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "",
						"startTime": "10:30:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "体验马尔代夫传统迎宾仪式。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 30
						}
					},
					{
						"day": 2,
						"title": "前往阿雅达岛",
						"description": "乘坐水上飞机前往阿雅达岛。",
						"activities": [
							"退房",
							"前往码头",
							"水上飞机"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "09:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "水上飞机",
						"flightNumber": "",
						"departureCity": "1337624",
						"arrivalCity": "阿雅达岛",
						"stayType": "",
						"hotelName": "",
						"remark": "欣赏马尔代夫环礁鸟瞰美景。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 1,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					},
					{
						"day": 2,
						"title": "早餐",
						"description": "酒店内享用早餐。",
						"activities": [],
						"meals": [
							"马尔代夫早餐",
							"热带水果"
						],
						"accommodation": "",
						"images": [],
						"type": "meal",
						"startTime": "07:00:00",
						"mealType": [
							"breakfast"
						],
						"adultMealIncluded": "included",
						"childMealIncluded": "included",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					}
				]
			},
			{
				"day": 1,
				"title": "北京-马累",
				"description": "国际航班飞往马尔代夫首都马累。",
				"meetingPoint": "北京首都国际机场T3航站楼",
				"meetingTime": "10:00:00",
				"schedule": [
					{
						"day": 1,
						"title": "入住马累酒店",
						"description": "入住马累市区酒店，短暂休息。",
						"activities": [
							"办理入住",
							"休息"
						],
						"meals": [],
						"accommodation": "马累市区酒店",
						"images": [],
						"type": "accommodation",
						"startTime": "23:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "酒店",
						"hotelName": "Hotel Jen Malé",
						"remark": "马累市区豪华酒店，为第二天前往度假岛做准备。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 0,
							"minutes": 30
						},
						"activityDuration": {
							"hours": 0,
							"minutes": 0
						}
					},
					{
						"day": 1,
						"title": "抵达马累",
						"description": "抵达马累国际机场，专人接机。",
						"activities": [
							"接机",
							"办理入境"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "22:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "",
						"flightNumber": "",
						"departureCity": "",
						"arrivalCity": "",
						"stayType": "",
						"hotelName": "",
						"remark": "导游将在机场出口处举\"马尔代夫度假\"接机牌。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 1,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 1,
							"minutes": 0
						}
					},
					{
						"day": 1,
						"title": "北京-马累",
						"description": "乘坐国际航班前往马尔代夫首都马累。",
						"activities": [
							"办理登机手续",
							"登机"
						],
						"meals": [],
						"accommodation": "",
						"images": [],
						"type": "transport",
						"startTime": "10:00:00",
						"mealType": [],
						"adultMealIncluded": "",
						"childMealIncluded": "",
						"mealPrice": 0,
						"transportType": "飞机",
						"flightNumber": "SQ801+SQ436",
						"departureCity": "110000",
						"arrivalCity": "1337624",
						"stayType": "",
						"hotelName": "",
						"remark": "请提前3小时到达机场办理登机手续，经新加坡转机。",
						"mealDuration": {
							"hours": 0,
							"minutes": 0
						},
						"estimatedDuration": {
							"hours": 12,
							"minutes": 0
						},
						"activityDuration": {
							"hours": 12,
							"minutes": 0
						}
					}
				]
			}
		],
		"id": 16,
		"originalPrice": 29999,
		"departureCity": "440100,441900,441300,440300",
		"productType": "自由行",
		"coverImage": "https://images.unsplash.com/photo-1573843981267-be1999ff37cd?q=80&w=1000",
		"imageDescription": [
			"https://images.unsplash.com/photo-1512100356356-de1b84283e18?q=80&w=2000&auto=format",
			"https://images.unsplash.com/photo-1586861256632-42e108743990?q=80&w=2000&auto=format",
			"https://images.unsplash.com/photo-1514282401047-d79a71a590e8?q=80&w=2000&auto=format",
			"https://images.unsplash.com/photo-1602157947535-51a09392fcf0?q=80&w=2000&auto=format"
		],
		"travelMode": "休闲度假",
		"groupType": "自助",
		"minGroupSize": 2,
		"maxGroupSize": 6,
		"collectType": "capitation",
		"promotionPrice": 26999,
		"categoryId": "32002001",
		"categoryPath": [
			"32000000",
			"32002000",
			"32002001"
		],
		"validFrom": "2024-06-01",
		"validTo": "2026-05-31",
		"isLongTerm": false,
		"singleRoomSurcharge": "400.00",
		"roomUpgradeSurcharge": "600.00",
		"themesIds": [
			"13",
			"12",
			"11",
			"14",
			"10",
			"9",
			"8",
			"7"
		],
		"consumableDateType": "specificDays",
		"consumableDays": 120,
		"consumableDates": null,
		"purchaseLimitSingle": 1,
		"purchaseLimitPerOrder": 5,
		"purchaseLimitRules": 1,
		"purchaseLimitDetails": "需持有效护照，单人限购2个名额",
		"earliestBookingDays": 30,
		"advanceBookingRequired": 1,
		"advanceBookingRange": "oneDayBefore",
		"advanceBookingTime": 48,
		"advanceBookingTimeUnit": "hours",
		"hasNoCancellationAfterAppointment": 1,
		"refundBeforeAppointment": 1,
		"autoRefundAfterExpiration": 0,
		"hasBreachOfContractTerms": 1,
		"breachOfContractTerms": [
			{
				"days": 30,
				"buyerPenalty": 20,
				"sellerPenalty": 20
			},
			{
				"days": 15,
				"buyerPenalty": 50,
				"sellerPenalty": 50
			},
			{
				"days": 7,
				"buyerPenalty": 80,
				"sellerPenalty": 80
			},
			{
				"days": 3,
				"buyerPenalty": 100,
				"sellerPenalty": 100
			}
		],
		"breachOfContractBase": "amountTotal"
	}
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | 该参数用于表示API请求的状态码，200通常表示请求成功。 |
| message | success | string | 该参数用于返回API请求相关的消息，描述请求执行的结果。 |
| data | {} | object | 该参数用于承载API请求返回的具体数据内容，是请求结果的主体部分。 |
| data.title | 马尔代夫阿雅达岛7日5晚奢华度假之旅 | string | 该参数表示产品的标题，用于简洁概括产品的主要内容。 |
| data.price | 17999 | number | 该参数代表产品的价格，反映购买该产品所需支付的金额。 |
| data.days | 7 | number | 该参数表示整个旅行行程所持续的天数。 |
| data.nights | 5 | number | 该参数代表在旅行期间需要住宿的夜晚数量。 |
| data.destination | 50080018,50080017,50080019,1733684 | string | 该参数以编号形式表示旅行目的地，可包含多个目的地编号。 |
| data.images | [] | array | 该参数用于记录与产品相关的图片信息，可能是图片链接等内容。 |
| data.description | 奢享印度洋上的世外桃源，入住五星级水上别墅，畅游纯净海域，体验浪漫的海底餐厅和SPA。行程包含浮潜、深潜、海钓、海豚巡游等丰富水上活动。 | string | 该参数对产品进行详细描述，介绍产品特色、行程安排等内容。 |
| data.features | 奢华度假、丰富水上活动、优质住宿 | array | 该参数用于突出产品区别于其他产品的独特特点和优势。 |
| data.status | online | string | 该参数表示产品当前所处的状态，如上线、下线等。 |
| data.deposit | 8000 | number | 该参数代表预订产品时需要支付的定金数额。 |
| data.scheduleDays | [] | array | 该参数用于存储整个旅行行程中每天的详细安排信息。 |
| data.scheduleDays.day | 7 | number | 该参数表示行程安排中具体某一天的编号或顺序。 |
| data.scheduleDays.title | 阿雅达岛-马累-北京 | string | 该参数用于概括行程安排中某一天的主要内容或地点信息。 |
| data.scheduleDays.description | 乘坐水上飞机返回马累，转机返回北京。 | string | 该参数对行程安排中某一天的活动、交通等情况进行详细说明。 |
| data.scheduleDays.meetingPoint | 酒店大堂 | string | 该参数表示行程中当天活动的集合地点。 |
| data.scheduleDays.meetingTime | 09:00:00 | string | 该参数代表行程中当天活动的集合具体时间。 |
| data.scheduleDays.schedule | [] | array | 该参数用于存储行程安排中某一天具体的行程细节，如活动、餐饮、住宿等。 |
| data.scheduleDays.schedule.day | 7 | number | 在当日行程详情中，再次明确该行程对应的日数。 |
| data.scheduleDays.schedule.title | 返程航班 | string | 该参数概括当日行程详情的主要内容，如航班、活动等主题。 |
| data.scheduleDays.schedule.description | 乘坐国际航班返回北京。 | string | 该参数对当日行程详情进行详细的文字描述。 |
| data.scheduleDays.schedule.activities | [] | array | 该参数记录行程安排中当天具体的活动项目。 |
| data.scheduleDays.schedule.meals | [] | array | 该参数用于说明行程安排中当天提供的餐饮信息。 |
| data.scheduleDays.schedule.accommodation | [] | string | 该参数表示行程安排中当天的住宿相关信息。 |
| data.scheduleDays.schedule.images | [] | array | 该参数存储行程安排中当天相关的图片信息。 |
| data.scheduleDays.schedule.type | transport | string | 该参数表明行程安排中当天活动的类型，如交通、住宿等。 |
| data.scheduleDays.schedule.startTime | 19:00:00 | string | 该参数代表行程安排中当天某项活动或行程的开始时间。 |
| data.scheduleDays.schedule.mealType | 正餐 | array | 该参数用于明确行程安排中当天餐饮的具体类型。 |
| data.scheduleDays.schedule.adultMealIncluded | 是 | string | 该参数表示行程安排中当天成人餐饮是否包含在费用内。 |
| data.scheduleDays.schedule.childMealIncluded | 是 | string | 该参数表明行程安排中当天儿童餐饮是否包含在费用内。 |
| data.scheduleDays.schedule.mealPrice | 0 | number | 该参数代表行程安排中当天餐饮的价格。 |
| data.scheduleDays.schedule.transportType | 飞机 | string | 该参数说明行程安排中当天所使用的交通方式。 |
| data.scheduleDays.schedule.flightNumber | SQ437+SQ800 | string | 该参数记录行程安排中当天所乘坐航班的航班号码。 |
| data.scheduleDays.schedule.departureCity | 1337624 | string | 该参数以编号形式表示行程安排中当天出发的城市。 |
| data.scheduleDays.schedule.arrivalCity | 110000 | string | 该参数以编号形式表示行程安排中当天到达的城市。 |
| data.scheduleDays.schedule.stayType | 酒店 | string | 该参数表明行程安排中当天住宿的具体类型。 |
| data.scheduleDays.schedule.hotelName | 阿雅达岛酒店 | string | 该参数记录行程安排中当天所入住酒店的名称。 |
| data.scheduleDays.schedule.remark | 经新加坡转机返回北京，结束愉快的马尔代夫之旅。 | string | 该参数对行程安排中当天的特殊情况、注意事项等进行补充说明。 |
| data.scheduleDays.schedule.mealDuration | {} | object | 该参数用于记录行程安排中当天餐饮持续的时间信息。 |
| data.scheduleDays.schedule.mealDuration.hours | 0 | number | 该参数表示行程安排中当天餐饮持续时间的小时数。 |
| data.scheduleDays.schedule.mealDuration.minutes | 0 | number | 该参数代表行程安排中当天餐饮持续时间的分钟数。 |
| data.scheduleDays.schedule.estimatedDuration | {} | object | 该参数用于记录行程安排中当天某项活动或行程预计持续的时间。 |
| data.scheduleDays.schedule.estimatedDuration.hours | 14 | number | 该参数表示行程安排中当天某项活动或行程预计持续时间的小时数。 |
| data.scheduleDays.schedule.estimatedDuration.minutes | 0 | number | 该参数代表行程安排中当天某项活动或行程预计持续时间的分钟数。 |
| data.scheduleDays.schedule.activityDuration | {} | object | 该参数用于记录行程安排中当天某项活动持续的时间信息。 |
| data.scheduleDays.schedule.activityDuration.hours | 14 | number | 该参数表示行程安排中当天某项活动持续时间的小时数。 |
| data.scheduleDays.schedule.activityDuration.minutes | 0 | number | 该参数代表行程安排中当天某项活动持续时间的分钟数。 |
| data.id | 16 | number | 该参数是产品的唯一标识符，用于区分不同的产品。 |
| data.originalPrice | 29999 | number | 该参数表示产品在未进行促销等活动时的原始价格。 |
| data.departureCity | 440100,441900,441300,440300 | string | 该参数以编号集合形式表示产品出发的多个城市。 |
| data.productType | 自由行 | string | 该参数表明产品的类型，如跟团游、自由行等。 |
| data.coverImage | https://images.unsplash.com/photo-1573843981267-be1999ff37cd?q=80&w=1000 | string | 该参数为产品的封面图片链接，用于展示产品形象。 |
| data.imageDescription | 马尔代夫阿雅达岛美景 | array | 该参数对产品相关图片进行文字描述，辅助理解图片内容。 |
| data.travelMode | 休闲度假 | string | 该参数表示产品所提供的旅行方式或主题。 |
| data.groupType | 自助 | string | 该参数说明产品组团的类型，如自助、团队等。 |
| data.minGroupSize | 2 | number | 该参数代表产品组团时的最小人数限制。 |
| data.maxGroupSize | 6 | number | 该参数表示产品组团时的最大人数限制。 |
| data.collectType | capitation | string | 该参数表明产品收费的类型，如按人头收费等方式。 |
| data.promotionPrice | 26999 | number | 该参数代表产品在促销活动期间的价格。 |
| data.categoryId | 32002001 | string | 该参数是产品所属分类的唯一标识符，用于产品分类管理。 |
| data.categoryPath | 旅游度假/海外游/马尔代夫 | array | 该参数表示产品在分类体系中的具体路径，便于定位产品类别。 |
| data.validFrom | 2024-06-01 | string | 该参数代表产品价格、活动等信息的有效开始日期。 |
| data.validTo | 2026-05-31 | string | 该参数表示产品价格、活动等信息的有效结束日期。 |
| data.isLongTerm | false | boolean | 该参数用于判断产品是否属于长期有效的产品类型。 |
| data.singleRoomSurcharge | 400.00 | string | 该参数表示单人入住时需要额外支付的单房差价金额。 |
| data.roomUpgradeSurcharge | 600.00 | string | 该参数代表将房间升级时需要额外支付的差价金额。 |
| data.themesIds | [] | array | 该参数以集合形式存储产品相关的主题ID，用于产品主题分类。 |
| data.consumableDateType | specificDays | string | 该参数表明产品消费日期的类型，如特定日期、时间段等。 |
| data.consumableDays | 120 | number | 该参数代表产品从购买后可进行消费的天数。 |
| data.consumableDates | [] | null | 该参数以集合形式存储产品具体可消费的日期信息。 |
| data.purchaseLimitSingle | 1 | number | 该参数表示每个用户单次购买产品的最大数量限制。 |
| data.purchaseLimitPerOrder | 5 | number | 该参数代表每个订单中可购买产品的最大数量限制。 |
| data.purchaseLimitRules | 1 | number | 该参数是购买限制规则的编号，用于关联具体的限购规则。 |
| data.purchaseLimitDetails | 需持有效护照，单人限购2个名额 | string | 该参数详细说明产品购买限制的具体内容和要求。 |
| data.earliestBookingDays | 30 | number | 该参数表示在出行日期前最早可以预订产品的天数。 |
| data.advanceBookingRequired | 1 | number | 该参数用于判断是否需要提前预订产品，1表示需要。 |
| data.advanceBookingRange | oneDayBefore | string | 该参数表明提前预订产品的时间范围类型，如提前一天等。 |
| data.advanceBookingTime | 48 | number | 该参数代表提前预订产品所需的具体时间数值。 |
| data.advanceBookingTimeUnit | hours | string | 该参数说明提前预订时间的单位，如小时、天等。 |
| data.hasNoCancellationAfterAppointment | 1 | number | 该参数用于判断产品在预约后是否不可以取消，1表示不可取消。 |
| data.refundBeforeAppointment | 1 | number | 该参数用于判断产品在预约前是否可以退款，1表示可以退款。 |
| data.autoRefundAfterExpiration | 0 | number | 该参数用于判断产品过期后是否自动退款，0表示不自动退款。 |
| data.hasBreachOfContractTerms | 1 | number | 该参数用于判断产品是否存在违约相关条款，1表示存在。 |
| data.breachOfContractTerms | {} | array | 该参数用于存储产品违约相关条款的详细内容。 |
| data.breachOfContractTerms.days | 30 | number | 该参数表示违约条款中规定的违约期限天数。 |
| data.breachOfContractTerms.buyerPenalty | 20 | number | 该参数代表买家违约时需支付的违约金比例。 |
| data.breachOfContractTerms.sellerPenalty | 20 | number | 该参数表示卖家违约时需支付的违约金比例。 |
| data.breachOfContractBase | amountTotal | string | 该参数表明违约金计算所依据的基础，如总金额等。 |

* 失败(404)

```javascript
暂无数据
```

**Query**
