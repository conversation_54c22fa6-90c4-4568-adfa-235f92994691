import { requestClient } from '#/api/request';

export interface UserItem {
  id: string | number;
  username?: string;
  nickname?: string | null;
  avatar?: string | null;
  gender?: number; // 0 女 1 男 等
  email?: string | null;
  phone?: string;
  status?: number; // 0禁用 1正常
  lastLoginTime?: string;
  lastLoginIp?: string;
}

export interface UserListResp {
  list: UserItem[];
  total: number;
}

export function getUserList(params: { nickname?: string; phone?: string; page: number|string; limit: number|string; }) {
  return requestClient.get<UserListResp>('/admin/user', { params });
}

export function setUserDisabled(id: string|number, status: 0|1) {
  return requestClient.post(`/admin/user/disabled/${id}`, { status });
}

