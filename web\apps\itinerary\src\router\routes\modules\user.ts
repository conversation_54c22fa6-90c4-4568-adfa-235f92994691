import type { RouteRecordRaw } from 'vue-router';

const userRoutes: RouteRecordRaw[] = [
  {
    path: '/user',
    name: 'User',
    meta: {
      orderNo: 2150,
      icon: 'ion:people-outline',
      title: '用户管理',
    },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('#/views/user/list/index.vue'),
        meta: { title: '用户列表' },
      },
    ],
  },
];

export default userRoutes;

