import type { RouteRecordRaw } from 'vue-router';

const systemRoutes: RouteRecordRaw[] = [
  {
    path: '/system',
    name: 'System',
    redirect: '/system/config',
    meta: {
      orderNo: 9000,
      icon: 'ion:settings-outline',
      title: '系统管理',
    },
    children: [
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('#/views/system/config/index.vue'),
        meta: {
          title: '配置管理',
          icon: 'ion:cog-outline',
        },
      },
      {
        path: 'config/group/:groupKey',
        name: 'SystemConfigGroup',
        component: () => import('#/views/system/config/group.vue'),
        meta: {
          title: '分组配置',
          hideInMenu: true,
          currentActiveMenu: '/system/config',
        },
        props: true,
      },
    ],
  },
];

export default systemRoutes;
