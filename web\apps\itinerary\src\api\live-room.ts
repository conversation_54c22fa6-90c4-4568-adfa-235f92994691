import { requestClient } from '#/api/request';

export interface LiveRoom {
  id: string | number;
  title: string;
  description?: string;
  status?: string;
  createTime?: string;
  updateTime?: string;
}

export interface LiveRoomProduct {
  id?: string | number;
  productId: string;
  productType: 'itinerary' | string;
  specialPrice?: number | '';
  discountRate?: number | '';
  stockLimit?: number;
  isFeatured?: number | boolean;
  sortOrder?: number;
  itinerary?: {
    id: string;
    title: string;
    price: number;
  };
}

// 列表
export function listLiveRooms(params?: { page?: number; pageSize?: number; keyword?: string }) {
  return requestClient.get<{ list: LiveRoom[]; total: number }>('/admin/live-room', {
    params,
  });
}

// 详情（用于编辑回显）
export function getLiveRoomDetail(id: string | number) {
  return requestClient.get<LiveRoom>(`/admin/live-room/${id}`);
}

// 创建
export function createLiveRoom(data: { title: string; description?: string }) {
  return requestClient.post<LiveRoom>('/admin/live-room', data);
}

// 更新
export function updateLiveRoom(id: string | number, data: { title?: string; description?: string }) {
  return requestClient.put<LiveRoom>(`/admin/live-room/${id}`, data);
}

// 删除
export function deleteLiveRoom(id: string | number) {
  return requestClient.delete(`/admin/live-room/${id}`);
}

// 直播间商品列表
export function getLiveRoomProducts(id: string | number) {
  return requestClient.get<{ list: LiveRoomProduct[]; total?: number }>(`/admin/live-room/${id}/products`);
}

// 添加直播间商品（支持批量）
export function addLiveRoomProducts(id: string | number, products: LiveRoomProduct) {
  return requestClient.post(`/admin/live-room/${id}/products`, products);
}

// 置顶
export function TopLiveRoomProducts(id: string | number, product_id: string | number) {
  return requestClient.post(`/admin/live-room/${id}/products/${product_id}/top`);
}

// 排序
export function SortLiveRoomProducts(id: string | number, product_id: string | number, sort:string | number) {
  return requestClient.post(`/admin/live-room/${id}/products/${product_id}/sort`, { sort_order : sort });
}

// 移除直播间商品
export function deleteLiveRoomProduct(id: string | number, product_id: string | number) {
  return requestClient.delete(`/admin/live-room/${id}/products/${product_id}`);
}

