import {
  selectProps,
  select_default
} from "./chunk-KGTR5MFQ.js";
import {
  omit_default
} from "./chunk-HN4DNUMZ.js";
import {
  _objectSpread2,
  flattenChildren,
  isValidElement,
  useConfigInject_default,
  warning_default2 as warning_default
} from "./chunk-7CXY6ZY4.js";
import {
  _extends
} from "./chunk-YTNASAWS.js";
import {
  createVNode,
  defineComponent,
  ref
} from "./chunk-3IESGBRV.js";

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/auto-complete/Option.js
var Option = () => null;
Option.isSelectOption = true;
Option.displayName = "AAutoCompleteOption";
var Option_default = Option;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/auto-complete/OptGroup.js
var OptGroup = () => null;
OptGroup.isSelectOptGroup = true;
OptGroup.displayName = "AAutoCompleteOptGroup";
var OptGroup_default = OptGroup;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/auto-complete/index.js
function isSelectOptionOrSelectOptGroup(child) {
  var _a, _b;
  return ((_a = child === null || child === void 0 ? void 0 : child.type) === null || _a === void 0 ? void 0 : _a.isSelectOption) || ((_b = child === null || child === void 0 ? void 0 : child.type) === null || _b === void 0 ? void 0 : _b.isSelectOptGroup);
}
var autoCompleteProps = () => _extends(_extends({}, omit_default(selectProps(), ["loading", "mode", "optionLabelProp", "labelInValue"])), {
  dataSource: Array,
  dropdownMenuStyle: {
    type: Object,
    default: void 0
  },
  // optionLabelProp: String,
  dropdownMatchSelectWidth: {
    type: [Number, Boolean],
    default: true
  },
  prefixCls: String,
  showSearch: {
    type: Boolean,
    default: void 0
  },
  transitionName: String,
  choiceTransitionName: {
    type: String,
    default: "zoom"
  },
  autofocus: {
    type: Boolean,
    default: void 0
  },
  backfill: {
    type: Boolean,
    default: void 0
  },
  // optionLabelProp: PropTypes.string.def('children'),
  filterOption: {
    type: [Boolean, Function],
    default: false
  },
  defaultActiveFirstOption: {
    type: Boolean,
    default: true
  },
  status: String
});
var AutoCompleteOption = Option_default;
var AutoCompleteOptGroup = OptGroup_default;
var AutoComplete = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "AAutoComplete",
  inheritAttrs: false,
  props: autoCompleteProps(),
  // emits: ['change', 'select', 'focus', 'blur'],
  slots: Object,
  setup(props, _ref) {
    let {
      slots,
      attrs,
      expose
    } = _ref;
    warning_default(!("dataSource" in slots), "AutoComplete", "`dataSource` slot is deprecated, please use props `options` instead.");
    warning_default(!("options" in slots), "AutoComplete", "`options` slot is deprecated, please use props `options` instead.");
    warning_default(!props.dropdownClassName, "AutoComplete", "`dropdownClassName` is deprecated, please use `popupClassName` instead.");
    const selectRef = ref();
    const getInputElement = () => {
      var _a;
      const children = flattenChildren((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots));
      const element = children.length ? children[0] : void 0;
      return element;
    };
    const focus = () => {
      var _a;
      (_a = selectRef.value) === null || _a === void 0 ? void 0 : _a.focus();
    };
    const blur = () => {
      var _a;
      (_a = selectRef.value) === null || _a === void 0 ? void 0 : _a.blur();
    };
    expose({
      focus,
      blur
    });
    const {
      prefixCls
    } = useConfigInject_default("select", props);
    return () => {
      var _a, _b, _c;
      const {
        size,
        dataSource,
        notFoundContent = (_a = slots.notFoundContent) === null || _a === void 0 ? void 0 : _a.call(slots)
      } = props;
      let optionChildren;
      const {
        class: className
      } = attrs;
      const cls = {
        [className]: !!className,
        [`${prefixCls.value}-lg`]: size === "large",
        [`${prefixCls.value}-sm`]: size === "small",
        [`${prefixCls.value}-show-search`]: true,
        [`${prefixCls.value}-auto-complete`]: true
      };
      if (props.options === void 0) {
        const childArray = ((_b = slots.dataSource) === null || _b === void 0 ? void 0 : _b.call(slots)) || ((_c = slots.options) === null || _c === void 0 ? void 0 : _c.call(slots)) || [];
        if (childArray.length && isSelectOptionOrSelectOptGroup(childArray[0])) {
          optionChildren = childArray;
        } else {
          optionChildren = dataSource ? dataSource.map((item) => {
            if (isValidElement(item)) {
              return item;
            }
            switch (typeof item) {
              case "string":
                return createVNode(Option_default, {
                  "key": item,
                  "value": item
                }, {
                  default: () => [item]
                });
              case "object":
                return createVNode(Option_default, {
                  "key": item.value,
                  "value": item.value
                }, {
                  default: () => [item.text]
                });
              default:
                throw new Error("AutoComplete[dataSource] only supports type `string[] | Object[]`.");
            }
          }) : [];
        }
      }
      const selectProps2 = omit_default(_extends(_extends(_extends({}, props), attrs), {
        mode: select_default.SECRET_COMBOBOX_MODE_DO_NOT_USE,
        // optionLabelProp,
        getInputElement,
        notFoundContent,
        // placeholder: '',
        class: cls,
        popupClassName: props.popupClassName || props.dropdownClassName,
        ref: selectRef
      }), ["dataSource", "loading"]);
      return createVNode(select_default, selectProps2, _objectSpread2({
        default: () => [optionChildren]
      }, omit_default(slots, ["default", "dataSource", "options"])));
    };
  }
});
var auto_complete_default = _extends(AutoComplete, {
  Option: Option_default,
  OptGroup: OptGroup_default,
  install(app) {
    app.component(AutoComplete.name, AutoComplete);
    app.component(Option_default.displayName, Option_default);
    app.component(OptGroup_default.displayName, OptGroup_default);
    return app;
  }
});

export {
  autoCompleteProps,
  AutoCompleteOption,
  AutoCompleteOptGroup,
  auto_complete_default
};
//# sourceMappingURL=chunk-7FY6HXLI.js.map
