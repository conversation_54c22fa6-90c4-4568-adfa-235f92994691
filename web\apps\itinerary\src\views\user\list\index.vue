<script setup lang="ts">
import 'viewerjs/dist/viewer.css';
import Viewer from 'viewerjs';
import { h, onMounted, reactive, ref } from 'vue';
import { Page } from '@vben/common-ui';
import { Button, Card, Form, Input, Space, Table, Tag, message, Popconfirm } from 'ant-design-vue';
import { getUserList, setUserDisabled, type UserItem } from '#/api/user';

const query = reactive({ nickname: '', phone: '', page: 1, limit: 10 });
const loading = ref(false);
const dataSource = ref<UserItem[]>([]);

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (t: number) => `共 ${t} 条`,
});

const columns = [
  { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
  { title: '用户名', dataIndex: 'username', key: 'username', width: 160 },
  { title: '昵称', dataIndex: 'nickname', key: 'nickname', width: 160 },
  // 头像点击预览
  { title: '头像', dataIndex: 'avatar', key: 'avatar', width: 160, customRender: ({ text }: any) => text ? h(
    'img',
    { src: text, style: { width: '50px', height: '50px', objectFit: 'cover', cursor: 'pointer' },
    onClick: (e: Event) => {
          const target = e.target as HTMLImageElement;
          if (target) {
            const viewer = new Viewer(target, {
              inline: false,
              toolbar: {
                zoomIn: 1,
                zoomOut: 1,
                oneToOne: 1,
                reset: 1,
                prev: 1,
                play: 0,
                next: 1,
                rotateLeft: 1,
                rotateRight: 1,
                flipHorizontal: 1,
                flipVertical: 1,
              },
            });
            viewer.show();
          }
        } },
  ) : '-' },
  { title: '手机号', dataIndex: 'phone', key: 'phone', width: 140 },
  { title: '状态', dataIndex: 'status', key: 'status', width: 100, customRender: ({ text }: any) => text === 1 ? h(Tag, { color: 'green' }, () => '正常') : h(Tag, { color: 'red' }, () => '禁用') },
  { title: '上次登录时间', dataIndex: 'lastLoginTime', key: 'lastLoginTime', width: 180 },
  { title: '上次登录IP', dataIndex: 'lastLoginIp', key: 'lastLoginIp', width: 160 },
  {
    title: '操作', key: 'action', width: 160, fixed: 'right' as const,
    customRender: ({ record }: { record: UserItem }) => {
      const isDisabled = record.status === 0;
      const toggleText = isDisabled ? '启用' : '禁用';
      const nextStatus = isDisabled ? 1 : 0;
      return h(Popconfirm, {
        title: `确认${toggleText}该用户？`,
        onConfirm: async () => {
          try {
            await setUserDisabled(record.id, nextStatus as 0|1);
            message.success(`${toggleText}成功`);
            fetchData();
          } catch (e) {
            message.error(`${toggleText}失败`);
          }
        },
      }, {
        default: () => h(Button, { type: isDisabled ? 'primary' : 'default', danger: !isDisabled, size: 'small' }, () => toggleText),
      });
    },
  },
];

async function fetchData() {
  loading.value = true;
  try {
    const res = await getUserList({ nickname: query.nickname || undefined, phone: query.phone || undefined, page: pagination.current, limit: pagination.pageSize });
    dataSource.value = res.list || [];
    pagination.total = res.total || 0;
  } catch (e) {
    message.error('获取用户列表失败');
  } finally {
    loading.value = false;
  }
}

function onSearch() {
  pagination.current = 1;
  fetchData();
}

function onReset() {
  query.nickname = '';
  query.phone = '';
  pagination.current = 1;
  fetchData();
}

function onTableChange(pag: any) {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
}

onMounted(fetchData);
</script>

<template>
  <div>
    <Page title="用户管理">
      <Card class="mb-4">
        <Form layout="inline" :model="query">
          <Form.Item label="昵称">
            <Input v-model:value="query.nickname" placeholder="昵称" allow-clear />
          </Form.Item>
          <Form.Item label="手机号">
            <Input v-model:value="query.phone" placeholder="手机号" allow-clear />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" @click="onSearch">搜索</Button>
              <Button @click="onReset">重置</Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Card>
        <Table
          :columns="columns"
          :data-source="dataSource"
          :pagination="pagination"
          :loading="loading"
          :row-key="(r: UserItem) => r.id"
          :scroll="{ x: 1200 }"
          @change="onTableChange"
        />
      </Card>
    </Page>
  </div>
</template>

