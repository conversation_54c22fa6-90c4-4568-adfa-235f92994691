import { requestClient } from '#/api/request';
import type {
  ConfigItem,
  ConfigListParams,
  ConfigListResult,
  CreateConfigParams,
  UpdateConfigParams,
  PublicConfigResult,
  ConfigTypeOption,
  ConfigGroupOption,
  ConfigGroupDetail,
  ConfigGroupListResult,
  CreateConfigGroupParams,
  UpdateConfigGroupParams,
} from './types';

/**
 * 获取配置列表
 * @param params 查询参数
 * @returns 配置列表数据
 */
export function getConfigList(params?: ConfigListParams) {
  return requestClient.get<ConfigListResult>('/system-config', { params });
}

/**
 * 获取配置详情
 * @param id 配置ID
 * @returns 配置详情
 */
export function getConfigDetail(id: string | number) {
  return requestClient.get<ConfigItem>(`/system-config/${id}`);
}

/**
 * 创建配置
 * @param data 配置数据
 * @returns 创建结果
 */
export function createConfig(data: CreateConfigParams) {
  return requestClient.post<ConfigItem>('/system-config', data);
}

/**
 * 更新配置
 * @param id 配置ID
 * @param data 更新数据
 * @returns 更新结果
 */
export function updateConfig(id: string | number, data: UpdateConfigParams) {
  return requestClient.put<ConfigItem>(`/system-config/${id}`, data);
}

/**
 * 删除配置
 * @param id 配置ID
 * @returns 删除结果
 */
export function deleteConfig(id: string | number) {
  return requestClient.delete(`/system-config/${id}`);
}

/**
 * 获取公开配置
 * @returns 公开配置数据
 */
export function getPublicConfig() {
  return requestClient.get<PublicConfigResult>('/system-config/public');
}

/**
 * 获取配置类型选项
 * @returns 配置类型选项列表
 */
export function getConfigTypes() {
  return requestClient.get<{ types: ConfigTypeOption[]; total: number }>('/system-config/types');
}

/**
 * 获取配置分组选项
 * @returns 配置分组选项列表
 */
export function getConfigGroupOptions() {
  return requestClient.get<{ groups: ConfigGroupOption[]; total: number }>('/system-config/group-options');
}

/**
 * 批量删除配置
 * @param ids 配置ID数组
 * @returns 删除结果
 */
export function batchDeleteConfig(ids: (string | number)[]) {
  return requestClient.delete('/system-config/batch', { data: { ids } });
}

/**
 * 导出配置
 * @param params 导出参数
 * @returns 导出结果
 */
export function exportConfig(params?: ConfigListParams) {
  return requestClient.get('/system-config/export', { 
    params,
    responseType: 'blob'
  });
}

/**
 * 导入配置
 * @param file 配置文件
 * @returns 导入结果
 */
export function importConfig(file: File) {
  const formData = new FormData();
  formData.append('file', file);
  return requestClient.post('/system-config/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// ==================== 配置分组管理 API ====================

/**
 * 获取配置分组列表
 * @returns 配置分组列表数据
 */
export function getConfigGroups() {
  return requestClient.get<ConfigGroupListResult>('/system-config/groups');
}

/**
 * 获取配置分组详情
 * @param groupKey 分组键
 * @returns 配置分组详情
 */
export function getConfigGroupDetail(groupKey: string) {
  return requestClient.get<ConfigGroupDetail>(`/system-config/groups/${groupKey}`);
}

/**
 * 创建配置分组
 * @param data 分组数据
 * @returns 创建结果
 */
export function createConfigGroup(data: CreateConfigGroupParams) {
  return requestClient.post<ConfigGroupDetail>('/system-config/groups', data);
}

/**
 * 更新配置分组
 * @param groupKey 分组键
 * @param data 更新数据
 * @returns 更新结果
 */
export function updateConfigGroup(groupKey: string, data: UpdateConfigGroupParams) {
  return requestClient.put<ConfigGroupDetail>(`/system-config/groups/${groupKey}`, data);
}

/**
 * 删除配置分组
 * @param groupKey 分组键
 * @returns 删除结果
 */
export function deleteConfigGroup(groupKey: string) {
  return requestClient.delete(`/system-config/groups/${groupKey}`);
}

/**
 * 获取指定分组的配置列表
 * @param groupKey 分组键
 * @param params 查询参数
 * @returns 配置列表数据
 */
export function getConfigsByGroup(groupKey: string, params?: Omit<ConfigListParams, 'config_group'>) {
  return requestClient.get<ConfigListResult>(`/system-config/groups/${groupKey}/items`, { params });
}
