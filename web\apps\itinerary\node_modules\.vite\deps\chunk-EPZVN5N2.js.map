{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-overflow/context.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-overflow/Item.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-overflow/RawItem.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-overflow/Overflow.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-overflow/index.js"], "sourcesContent": ["import { computed, defineComponent, inject, provide } from 'vue';\nconst OverflowContextProviderKey = Symbol('OverflowContextProviderKey');\nexport const OverflowContextProvider = defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'OverflowContextProvider',\n  inheritAttrs: false,\n  props: {\n    value: {\n      type: Object\n    }\n  },\n  setup(props, _ref) {\n    let {\n      slots\n    } = _ref;\n    provide(OverflowContextProviderKey, computed(() => props.value));\n    return () => {\n      var _a;\n      return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n    };\n  }\n});\nexport const useInjectOverflowContext = () => {\n  return inject(OverflowContextProviderKey, computed(() => null));\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { resolveDirective as _resolveDirective, createVNode as _createVNode } from \"vue\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { computed, defineComponent, onUnmounted, ref } from 'vue';\nimport ResizeObserver from '../vc-resize-observer';\nimport classNames from '../_util/classNames';\nimport PropTypes from '../_util/vue-types';\nconst UNDEFINED = undefined;\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'Item',\n  props: {\n    prefixCls: String,\n    item: PropTypes.any,\n    renderItem: Function,\n    responsive: Boolean,\n    itemKey: {\n      type: [String, Number]\n    },\n    registerSize: Function,\n    display: Boolean,\n    order: Number,\n    component: PropTypes.any,\n    invalidate: Boolean\n  },\n  setup(props, _ref) {\n    let {\n      slots,\n      expose\n    } = _ref;\n    const mergedHidden = computed(() => props.responsive && !props.display);\n    const itemNodeRef = ref();\n    expose({\n      itemNodeRef\n    });\n    // ================================ Effect ================================\n    function internalRegisterSize(width) {\n      props.registerSize(props.itemKey, width);\n    }\n    onUnmounted(() => {\n      internalRegisterSize(null);\n    });\n    return () => {\n      var _a;\n      const {\n          prefixCls,\n          invalidate,\n          item,\n          renderItem,\n          responsive,\n          registerSize,\n          itemKey,\n          display,\n          order,\n          component: Component = 'div'\n        } = props,\n        restProps = __rest(props, [\"prefixCls\", \"invalidate\", \"item\", \"renderItem\", \"responsive\", \"registerSize\", \"itemKey\", \"display\", \"order\", \"component\"]);\n      const children = (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n      // ================================ Render ================================\n      const childNode = renderItem && item !== UNDEFINED ? renderItem(item) : children;\n      let overflowStyle;\n      if (!invalidate) {\n        overflowStyle = {\n          opacity: mergedHidden.value ? 0 : 1,\n          height: mergedHidden.value ? 0 : UNDEFINED,\n          overflowY: mergedHidden.value ? 'hidden' : UNDEFINED,\n          order: responsive ? order : UNDEFINED,\n          pointerEvents: mergedHidden.value ? 'none' : UNDEFINED,\n          position: mergedHidden.value ? 'absolute' : UNDEFINED\n        };\n      }\n      const overflowProps = {};\n      if (mergedHidden.value) {\n        overflowProps['aria-hidden'] = true;\n      }\n      // 使用 disabled  避免结构不一致 导致子组件 rerender\n      return _createVNode(ResizeObserver, {\n        \"disabled\": !responsive,\n        \"onResize\": _ref2 => {\n          let {\n            offsetWidth\n          } = _ref2;\n          internalRegisterSize(offsetWidth);\n        }\n      }, {\n        default: () => _createVNode(Component, _objectSpread(_objectSpread(_objectSpread({\n          \"class\": classNames(!invalidate && prefixCls),\n          \"style\": overflowStyle\n        }, overflowProps), restProps), {}, {\n          \"ref\": itemNodeRef\n        }), {\n          default: () => [childNode]\n        })\n      });\n    };\n  }\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { resolveDirective as _resolveDirective, createVNode as _createVNode } from \"vue\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { defineComponent } from 'vue';\nimport classNames from '../_util/classNames';\nimport PropTypes from '../_util/vue-types';\nimport { OverflowContextProvider, useInjectOverflowContext } from './context';\nimport Item from './Item';\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'RawItem',\n  inheritAttrs: false,\n  props: {\n    component: PropTypes.any,\n    title: PropTypes.any,\n    id: String,\n    onMouseenter: {\n      type: Function\n    },\n    onMouseleave: {\n      type: Function\n    },\n    onClick: {\n      type: Function\n    },\n    onKeydown: {\n      type: Function\n    },\n    onFocus: {\n      type: Function\n    },\n    role: String,\n    tabindex: Number\n  },\n  setup(props, _ref) {\n    let {\n      slots,\n      attrs\n    } = _ref;\n    const context = useInjectOverflowContext();\n    return () => {\n      var _a;\n      // Render directly when context not provided\n      if (!context.value) {\n        const {\n            component: Component = 'div'\n          } = props,\n          restProps = __rest(props, [\"component\"]);\n        return _createVNode(Component, _objectSpread(_objectSpread({}, restProps), attrs), {\n          default: () => [(_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)]\n        });\n      }\n      const _b = context.value,\n        {\n          className: contextClassName\n        } = _b,\n        restContext = __rest(_b, [\"className\"]);\n      const {\n          class: className\n        } = attrs,\n        restProps = __rest(attrs, [\"class\"]);\n      // Do not pass context to sub item to avoid multiple measure\n      return _createVNode(OverflowContextProvider, {\n        \"value\": null\n      }, {\n        default: () => [_createVNode(Item, _objectSpread(_objectSpread(_objectSpread({\n          \"class\": classNames(contextClassName, className)\n        }, restContext), restProps), props), slots)]\n      });\n    };\n  }\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { resolveDirective as _resolveDirective, createVNode as _createVNode } from \"vue\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { computed, defineComponent, shallowRef, watch } from 'vue';\nimport ResizeObserver from '../vc-resize-observer';\nimport classNames from '../_util/classNames';\nimport PropTypes from '../_util/vue-types';\nimport { OverflowContextProvider } from './context';\nimport Item from './Item';\nimport RawItem from './RawItem';\nconst RESPONSIVE = 'responsive';\nconst INVALIDATE = 'invalidate';\nfunction defaultRenderRest(omittedItems) {\n  return `+ ${omittedItems.length} ...`;\n}\nconst overflowProps = () => {\n  return {\n    id: String,\n    prefixCls: String,\n    data: Array,\n    itemKey: [String, Number, Function],\n    /** Used for `responsive`. It will limit render node to avoid perf issue */\n    itemWidth: {\n      type: Number,\n      default: 10\n    },\n    renderItem: Function,\n    /** @private Do not use in your production. Render raw node that need wrap Item by developer self */\n    renderRawItem: Function,\n    maxCount: [Number, String],\n    renderRest: Function,\n    /** @private Do not use in your production. Render raw node that need wrap Item by developer self */\n    renderRawRest: Function,\n    suffix: PropTypes.any,\n    component: String,\n    itemComponent: PropTypes.any,\n    /** @private This API may be refactor since not well design */\n    onVisibleChange: Function,\n    /** When set to `full`, ssr will render full items by default and remove at client side */\n    ssr: String,\n    onMousedown: Function,\n    role: String\n  };\n};\nconst Overflow = defineComponent({\n  name: 'Overflow',\n  inheritAttrs: false,\n  props: overflowProps(),\n  emits: ['visibleChange'],\n  setup(props, _ref) {\n    let {\n      attrs,\n      emit,\n      slots\n    } = _ref;\n    const fullySSR = computed(() => props.ssr === 'full');\n    const containerWidth = shallowRef(null);\n    const mergedContainerWidth = computed(() => containerWidth.value || 0);\n    const itemWidths = shallowRef(new Map());\n    const prevRestWidth = shallowRef(0);\n    const restWidth = shallowRef(0);\n    const suffixWidth = shallowRef(0);\n    const suffixFixedStart = shallowRef(null);\n    const displayCount = shallowRef(null);\n    const mergedDisplayCount = computed(() => {\n      if (displayCount.value === null && fullySSR.value) {\n        return Number.MAX_SAFE_INTEGER;\n      }\n      return displayCount.value || 0;\n    });\n    const restReady = shallowRef(false);\n    const itemPrefixCls = computed(() => `${props.prefixCls}-item`);\n    // Always use the max width to avoid blink\n    const mergedRestWidth = computed(() => Math.max(prevRestWidth.value, restWidth.value));\n    // ================================= Data =================================\n    const isResponsive = computed(() => !!(props.data.length && props.maxCount === RESPONSIVE));\n    const invalidate = computed(() => props.maxCount === INVALIDATE);\n    /**\n     * When is `responsive`, we will always render rest node to get the real width of it for calculation\n     */\n    const showRest = computed(() => isResponsive.value || typeof props.maxCount === 'number' && props.data.length > props.maxCount);\n    const mergedData = computed(() => {\n      let items = props.data;\n      if (isResponsive.value) {\n        if (containerWidth.value === null && fullySSR.value) {\n          items = props.data;\n        } else {\n          items = props.data.slice(0, Math.min(props.data.length, mergedContainerWidth.value / props.itemWidth));\n        }\n      } else if (typeof props.maxCount === 'number') {\n        items = props.data.slice(0, props.maxCount);\n      }\n      return items;\n    });\n    const omittedItems = computed(() => {\n      if (isResponsive.value) {\n        return props.data.slice(mergedDisplayCount.value + 1);\n      }\n      return props.data.slice(mergedData.value.length);\n    });\n    // ================================= Item =================================\n    const getKey = (item, index) => {\n      var _a;\n      if (typeof props.itemKey === 'function') {\n        return props.itemKey(item);\n      }\n      return (_a = props.itemKey && (item === null || item === void 0 ? void 0 : item[props.itemKey])) !== null && _a !== void 0 ? _a : index;\n    };\n    const mergedRenderItem = computed(() => props.renderItem || (item => item));\n    const updateDisplayCount = (count, notReady) => {\n      displayCount.value = count;\n      if (!notReady) {\n        restReady.value = count < props.data.length - 1;\n        emit('visibleChange', count);\n      }\n    };\n    // ================================= Size =================================\n    const onOverflowResize = (_, element) => {\n      containerWidth.value = element.clientWidth;\n    };\n    const registerSize = (key, width) => {\n      const clone = new Map(itemWidths.value);\n      if (width === null) {\n        clone.delete(key);\n      } else {\n        clone.set(key, width);\n      }\n      itemWidths.value = clone;\n    };\n    const registerOverflowSize = (_, width) => {\n      prevRestWidth.value = restWidth.value;\n      restWidth.value = width;\n    };\n    const registerSuffixSize = (_, width) => {\n      suffixWidth.value = width;\n    };\n    // ================================ Effect ================================\n    const getItemWidth = index => {\n      return itemWidths.value.get(getKey(mergedData.value[index], index));\n    };\n    watch([mergedContainerWidth, itemWidths, restWidth, suffixWidth, () => props.itemKey, mergedData], () => {\n      if (mergedContainerWidth.value && mergedRestWidth.value && mergedData.value) {\n        let totalWidth = suffixWidth.value;\n        const len = mergedData.value.length;\n        const lastIndex = len - 1;\n        // When data count change to 0, reset this since not loop will reach\n        if (!len) {\n          updateDisplayCount(0);\n          suffixFixedStart.value = null;\n          return;\n        }\n        for (let i = 0; i < len; i += 1) {\n          const currentItemWidth = getItemWidth(i);\n          // Break since data not ready\n          if (currentItemWidth === undefined) {\n            updateDisplayCount(i - 1, true);\n            break;\n          }\n          // Find best match\n          totalWidth += currentItemWidth;\n          if (\n          // Only one means `totalWidth` is the final width\n          lastIndex === 0 && totalWidth <= mergedContainerWidth.value ||\n          // Last two width will be the final width\n          i === lastIndex - 1 && totalWidth + getItemWidth(lastIndex) <= mergedContainerWidth.value) {\n            // Additional check if match the end\n            updateDisplayCount(lastIndex);\n            suffixFixedStart.value = null;\n            break;\n          } else if (totalWidth + mergedRestWidth.value > mergedContainerWidth.value) {\n            // Can not hold all the content to show rest\n            updateDisplayCount(i - 1);\n            suffixFixedStart.value = totalWidth - currentItemWidth - suffixWidth.value + restWidth.value;\n            break;\n          }\n        }\n        if (props.suffix && getItemWidth(0) + suffixWidth.value > mergedContainerWidth.value) {\n          suffixFixedStart.value = null;\n        }\n      }\n    });\n    return () => {\n      // ================================ Render ================================\n      const displayRest = restReady.value && !!omittedItems.value.length;\n      const {\n        itemComponent,\n        renderRawItem,\n        renderRawRest,\n        renderRest,\n        prefixCls = 'rc-overflow',\n        suffix,\n        component: Component = 'div',\n        id,\n        onMousedown\n      } = props;\n      const {\n          class: className,\n          style\n        } = attrs,\n        restAttrs = __rest(attrs, [\"class\", \"style\"]);\n      let suffixStyle = {};\n      if (suffixFixedStart.value !== null && isResponsive.value) {\n        suffixStyle = {\n          position: 'absolute',\n          left: `${suffixFixedStart.value}px`,\n          top: 0\n        };\n      }\n      const itemSharedProps = {\n        prefixCls: itemPrefixCls.value,\n        responsive: isResponsive.value,\n        component: itemComponent,\n        invalidate: invalidate.value\n      };\n      // >>>>> Choice render fun by `renderRawItem`\n      const internalRenderItemNode = renderRawItem ? (item, index) => {\n        const key = getKey(item, index);\n        return _createVNode(OverflowContextProvider, {\n          \"key\": key,\n          \"value\": _extends(_extends({}, itemSharedProps), {\n            order: index,\n            item,\n            itemKey: key,\n            registerSize,\n            display: index <= mergedDisplayCount.value\n          })\n        }, {\n          default: () => [renderRawItem(item, index)]\n        });\n      } : (item, index) => {\n        const key = getKey(item, index);\n        return _createVNode(Item, _objectSpread(_objectSpread({}, itemSharedProps), {}, {\n          \"order\": index,\n          \"key\": key,\n          \"item\": item,\n          \"renderItem\": mergedRenderItem.value,\n          \"itemKey\": key,\n          \"registerSize\": registerSize,\n          \"display\": index <= mergedDisplayCount.value\n        }), null);\n      };\n      // >>>>> Rest node\n      let restNode = () => null;\n      const restContextProps = {\n        order: displayRest ? mergedDisplayCount.value : Number.MAX_SAFE_INTEGER,\n        className: `${itemPrefixCls.value} ${itemPrefixCls.value}-rest`,\n        registerSize: registerOverflowSize,\n        display: displayRest\n      };\n      if (!renderRawRest) {\n        const mergedRenderRest = renderRest || defaultRenderRest;\n        restNode = () => _createVNode(Item, _objectSpread(_objectSpread({}, itemSharedProps), restContextProps), {\n          default: () => typeof mergedRenderRest === 'function' ? mergedRenderRest(omittedItems.value) : mergedRenderRest\n        });\n      } else if (renderRawRest) {\n        restNode = () => _createVNode(OverflowContextProvider, {\n          \"value\": _extends(_extends({}, itemSharedProps), restContextProps)\n        }, {\n          default: () => [renderRawRest(omittedItems.value)]\n        });\n      }\n      const overflowNode = () => {\n        var _a;\n        return _createVNode(Component, _objectSpread({\n          \"id\": id,\n          \"class\": classNames(!invalidate.value && prefixCls, className),\n          \"style\": style,\n          \"onMousedown\": onMousedown,\n          \"role\": props.role\n        }, restAttrs), {\n          default: () => [mergedData.value.map(internalRenderItemNode), showRest.value ? restNode() : null, suffix && _createVNode(Item, _objectSpread(_objectSpread({}, itemSharedProps), {}, {\n            \"order\": mergedDisplayCount.value,\n            \"class\": `${itemPrefixCls.value}-suffix`,\n            \"registerSize\": registerSuffixSize,\n            \"display\": true,\n            \"style\": suffixStyle\n          }), {\n            default: () => suffix\n          }), (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)]\n        });\n      };\n      // 使用 disabled  避免结构不一致 导致子组件 rerender\n      return _createVNode(ResizeObserver, {\n        \"disabled\": !isResponsive.value,\n        \"onResize\": onOverflowResize\n      }, {\n        default: overflowNode\n      });\n    };\n  }\n});\nOverflow.Item = RawItem;\nOverflow.RESPONSIVE = RESPONSIVE;\nOverflow.INVALIDATE = INVALIDATE;\nexport default Overflow;", "import Overflow from './Overflow';\nexport default Overflow;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAM,6BAA6B,OAAO,4BAA4B;AAC/D,IAAM,0BAA0B,gBAAgB;AAAA,EACrD,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EACA,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,YAAQ,4BAA4B,SAAS,MAAM,MAAM,KAAK,CAAC;AAC/D,WAAO,MAAM;AACX,UAAI;AACJ,cAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,IAChF;AAAA,EACF;AACF,CAAC;AACM,IAAM,2BAA2B,MAAM;AAC5C,SAAO,OAAO,4BAA4B,SAAS,MAAM,IAAI,CAAC;AAChE;;;ACxBA,IAAI,SAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAKA,IAAM,YAAY;AAClB,IAAO,eAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,OAAO;AAAA,IACL,WAAW;AAAA,IACX,MAAM,kBAAU;AAAA,IAChB,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,IACvB;AAAA,IACA,cAAc;AAAA,IACd,SAAS;AAAA,IACT,OAAO;AAAA,IACP,WAAW,kBAAU;AAAA,IACrB,YAAY;AAAA,EACd;AAAA,EACA,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,SAAS,MAAM,MAAM,cAAc,CAAC,MAAM,OAAO;AACtE,UAAM,cAAc,IAAI;AACxB,WAAO;AAAA,MACL;AAAA,IACF,CAAC;AAED,aAAS,qBAAqB,OAAO;AACnC,YAAM,aAAa,MAAM,SAAS,KAAK;AAAA,IACzC;AACA,gBAAY,MAAM;AAChB,2BAAqB,IAAI;AAAA,IAC3B,CAAC;AACD,WAAO,MAAM;AACX,UAAI;AACJ,YAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW,YAAY;AAAA,MACzB,IAAI,OACJ,YAAY,OAAO,OAAO,CAAC,aAAa,cAAc,QAAQ,cAAc,cAAc,gBAAgB,WAAW,WAAW,SAAS,WAAW,CAAC;AACvJ,YAAM,YAAY,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAExF,YAAM,YAAY,cAAc,SAAS,YAAY,WAAW,IAAI,IAAI;AACxE,UAAI;AACJ,UAAI,CAAC,YAAY;AACf,wBAAgB;AAAA,UACd,SAAS,aAAa,QAAQ,IAAI;AAAA,UAClC,QAAQ,aAAa,QAAQ,IAAI;AAAA,UACjC,WAAW,aAAa,QAAQ,WAAW;AAAA,UAC3C,OAAO,aAAa,QAAQ;AAAA,UAC5B,eAAe,aAAa,QAAQ,SAAS;AAAA,UAC7C,UAAU,aAAa,QAAQ,aAAa;AAAA,QAC9C;AAAA,MACF;AACA,YAAMA,iBAAgB,CAAC;AACvB,UAAI,aAAa,OAAO;AACtB,QAAAA,eAAc,aAAa,IAAI;AAAA,MACjC;AAEA,aAAO,YAAa,4BAAgB;AAAA,QAClC,YAAY,CAAC;AAAA,QACb,YAAY,WAAS;AACnB,cAAI;AAAA,YACF;AAAA,UACF,IAAI;AACJ,+BAAqB,WAAW;AAAA,QAClC;AAAA,MACF,GAAG;AAAA,QACD,SAAS,MAAM,YAAa,WAAW,eAAc,eAAc,eAAc;AAAA,UAC/E,SAAS,mBAAW,CAAC,cAAc,SAAS;AAAA,UAC5C,SAAS;AAAA,QACX,GAAGA,cAAa,GAAG,SAAS,GAAG,CAAC,GAAG;AAAA,UACjC,OAAO;AAAA,QACT,CAAC,GAAG;AAAA,UACF,SAAS,MAAM,CAAC,SAAS;AAAA,QAC3B,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACvGD,IAAIC,UAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAMA,IAAO,kBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,WAAW,kBAAU;AAAA,IACrB,OAAO,kBAAU;AAAA,IACjB,IAAI;AAAA,IACJ,cAAc;AAAA,MACZ,MAAM;AAAA,IACR;AAAA,IACA,cAAc;AAAA,MACZ,MAAM;AAAA,IACR;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,WAAW;AAAA,MACT,MAAM;AAAA,IACR;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,IACR;AAAA,IACA,MAAM;AAAA,IACN,UAAU;AAAA,EACZ;AAAA,EACA,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,yBAAyB;AACzC,WAAO,MAAM;AACX,UAAI;AAEJ,UAAI,CAAC,QAAQ,OAAO;AAClB,cAAM;AAAA,UACF,WAAW,YAAY;AAAA,QACzB,IAAI,OACJC,aAAYD,QAAO,OAAO,CAAC,WAAW,CAAC;AACzC,eAAO,YAAa,WAAW,eAAc,eAAc,CAAC,GAAGC,UAAS,GAAG,KAAK,GAAG;AAAA,UACjF,SAAS,MAAM,EAAE,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC;AAAA,QAC1F,CAAC;AAAA,MACH;AACA,YAAM,KAAK,QAAQ,OACjB;AAAA,QACE,WAAW;AAAA,MACb,IAAI,IACJ,cAAcD,QAAO,IAAI,CAAC,WAAW,CAAC;AACxC,YAAM;AAAA,QACF,OAAO;AAAA,MACT,IAAI,OACJ,YAAYA,QAAO,OAAO,CAAC,OAAO,CAAC;AAErC,aAAO,YAAa,yBAAyB;AAAA,QAC3C,SAAS;AAAA,MACX,GAAG;AAAA,QACD,SAAS,MAAM,CAAC,YAAa,cAAM,eAAc,eAAc,eAAc;AAAA,UAC3E,SAAS,mBAAW,kBAAkB,SAAS;AAAA,QACjD,GAAG,WAAW,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,MAC7C,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AC7ED,IAAIE,UAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAQA,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,SAAS,kBAAkB,cAAc;AACvC,SAAO,KAAK,aAAa,MAAM;AACjC;AACA,IAAM,gBAAgB,MAAM;AAC1B,SAAO;AAAA,IACL,IAAI;AAAA,IACJ,WAAW;AAAA,IACX,MAAM;AAAA,IACN,SAAS,CAAC,QAAQ,QAAQ,QAAQ;AAAA;AAAA,IAElC,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA;AAAA,IAEZ,eAAe;AAAA,IACf,UAAU,CAAC,QAAQ,MAAM;AAAA,IACzB,YAAY;AAAA;AAAA,IAEZ,eAAe;AAAA,IACf,QAAQ,kBAAU;AAAA,IAClB,WAAW;AAAA,IACX,eAAe,kBAAU;AAAA;AAAA,IAEzB,iBAAiB;AAAA;AAAA,IAEjB,KAAK;AAAA,IACL,aAAa;AAAA,IACb,MAAM;AAAA,EACR;AACF;AACA,IAAM,WAAW,gBAAgB;AAAA,EAC/B,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,cAAc;AAAA,EACrB,OAAO,CAAC,eAAe;AAAA,EACvB,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,SAAS,MAAM,MAAM,QAAQ,MAAM;AACpD,UAAM,iBAAiB,WAAW,IAAI;AACtC,UAAM,uBAAuB,SAAS,MAAM,eAAe,SAAS,CAAC;AACrE,UAAM,aAAa,WAAW,oBAAI,IAAI,CAAC;AACvC,UAAM,gBAAgB,WAAW,CAAC;AAClC,UAAM,YAAY,WAAW,CAAC;AAC9B,UAAM,cAAc,WAAW,CAAC;AAChC,UAAM,mBAAmB,WAAW,IAAI;AACxC,UAAM,eAAe,WAAW,IAAI;AACpC,UAAM,qBAAqB,SAAS,MAAM;AACxC,UAAI,aAAa,UAAU,QAAQ,SAAS,OAAO;AACjD,eAAO,OAAO;AAAA,MAChB;AACA,aAAO,aAAa,SAAS;AAAA,IAC/B,CAAC;AACD,UAAM,YAAY,WAAW,KAAK;AAClC,UAAM,gBAAgB,SAAS,MAAM,GAAG,MAAM,SAAS,OAAO;AAE9D,UAAM,kBAAkB,SAAS,MAAM,KAAK,IAAI,cAAc,OAAO,UAAU,KAAK,CAAC;AAErF,UAAM,eAAe,SAAS,MAAM,CAAC,EAAE,MAAM,KAAK,UAAU,MAAM,aAAa,WAAW;AAC1F,UAAM,aAAa,SAAS,MAAM,MAAM,aAAa,UAAU;AAI/D,UAAM,WAAW,SAAS,MAAM,aAAa,SAAS,OAAO,MAAM,aAAa,YAAY,MAAM,KAAK,SAAS,MAAM,QAAQ;AAC9H,UAAM,aAAa,SAAS,MAAM;AAChC,UAAI,QAAQ,MAAM;AAClB,UAAI,aAAa,OAAO;AACtB,YAAI,eAAe,UAAU,QAAQ,SAAS,OAAO;AACnD,kBAAQ,MAAM;AAAA,QAChB,OAAO;AACL,kBAAQ,MAAM,KAAK,MAAM,GAAG,KAAK,IAAI,MAAM,KAAK,QAAQ,qBAAqB,QAAQ,MAAM,SAAS,CAAC;AAAA,QACvG;AAAA,MACF,WAAW,OAAO,MAAM,aAAa,UAAU;AAC7C,gBAAQ,MAAM,KAAK,MAAM,GAAG,MAAM,QAAQ;AAAA,MAC5C;AACA,aAAO;AAAA,IACT,CAAC;AACD,UAAM,eAAe,SAAS,MAAM;AAClC,UAAI,aAAa,OAAO;AACtB,eAAO,MAAM,KAAK,MAAM,mBAAmB,QAAQ,CAAC;AAAA,MACtD;AACA,aAAO,MAAM,KAAK,MAAM,WAAW,MAAM,MAAM;AAAA,IACjD,CAAC;AAED,UAAM,SAAS,CAAC,MAAM,UAAU;AAC9B,UAAI;AACJ,UAAI,OAAO,MAAM,YAAY,YAAY;AACvC,eAAO,MAAM,QAAQ,IAAI;AAAA,MAC3B;AACA,cAAQ,KAAK,MAAM,YAAY,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,MAAM,OAAO,QAAQ,QAAQ,OAAO,SAAS,KAAK;AAAA,IACpI;AACA,UAAM,mBAAmB,SAAS,MAAM,MAAM,eAAe,UAAQ,KAAK;AAC1E,UAAM,qBAAqB,CAAC,OAAO,aAAa;AAC9C,mBAAa,QAAQ;AACrB,UAAI,CAAC,UAAU;AACb,kBAAU,QAAQ,QAAQ,MAAM,KAAK,SAAS;AAC9C,aAAK,iBAAiB,KAAK;AAAA,MAC7B;AAAA,IACF;AAEA,UAAM,mBAAmB,CAAC,GAAG,YAAY;AACvC,qBAAe,QAAQ,QAAQ;AAAA,IACjC;AACA,UAAM,eAAe,CAAC,KAAK,UAAU;AACnC,YAAM,QAAQ,IAAI,IAAI,WAAW,KAAK;AACtC,UAAI,UAAU,MAAM;AAClB,cAAM,OAAO,GAAG;AAAA,MAClB,OAAO;AACL,cAAM,IAAI,KAAK,KAAK;AAAA,MACtB;AACA,iBAAW,QAAQ;AAAA,IACrB;AACA,UAAM,uBAAuB,CAAC,GAAG,UAAU;AACzC,oBAAc,QAAQ,UAAU;AAChC,gBAAU,QAAQ;AAAA,IACpB;AACA,UAAM,qBAAqB,CAAC,GAAG,UAAU;AACvC,kBAAY,QAAQ;AAAA,IACtB;AAEA,UAAM,eAAe,WAAS;AAC5B,aAAO,WAAW,MAAM,IAAI,OAAO,WAAW,MAAM,KAAK,GAAG,KAAK,CAAC;AAAA,IACpE;AACA,UAAM,CAAC,sBAAsB,YAAY,WAAW,aAAa,MAAM,MAAM,SAAS,UAAU,GAAG,MAAM;AACvG,UAAI,qBAAqB,SAAS,gBAAgB,SAAS,WAAW,OAAO;AAC3E,YAAI,aAAa,YAAY;AAC7B,cAAM,MAAM,WAAW,MAAM;AAC7B,cAAM,YAAY,MAAM;AAExB,YAAI,CAAC,KAAK;AACR,6BAAmB,CAAC;AACpB,2BAAiB,QAAQ;AACzB;AAAA,QACF;AACA,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,gBAAM,mBAAmB,aAAa,CAAC;AAEvC,cAAI,qBAAqB,QAAW;AAClC,+BAAmB,IAAI,GAAG,IAAI;AAC9B;AAAA,UACF;AAEA,wBAAc;AACd;AAAA;AAAA,YAEA,cAAc,KAAK,cAAc,qBAAqB;AAAA,YAEtD,MAAM,YAAY,KAAK,aAAa,aAAa,SAAS,KAAK,qBAAqB;AAAA,YAAO;AAEzF,+BAAmB,SAAS;AAC5B,6BAAiB,QAAQ;AACzB;AAAA,UACF,WAAW,aAAa,gBAAgB,QAAQ,qBAAqB,OAAO;AAE1E,+BAAmB,IAAI,CAAC;AACxB,6BAAiB,QAAQ,aAAa,mBAAmB,YAAY,QAAQ,UAAU;AACvF;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAM,UAAU,aAAa,CAAC,IAAI,YAAY,QAAQ,qBAAqB,OAAO;AACpF,2BAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO,MAAM;AAEX,YAAM,cAAc,UAAU,SAAS,CAAC,CAAC,aAAa,MAAM;AAC5D,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAY;AAAA,QACZ;AAAA,QACA,WAAW,YAAY;AAAA,QACvB;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACF,OAAO;AAAA,QACP;AAAA,MACF,IAAI,OACJ,YAAYA,QAAO,OAAO,CAAC,SAAS,OAAO,CAAC;AAC9C,UAAI,cAAc,CAAC;AACnB,UAAI,iBAAiB,UAAU,QAAQ,aAAa,OAAO;AACzD,sBAAc;AAAA,UACZ,UAAU;AAAA,UACV,MAAM,GAAG,iBAAiB,KAAK;AAAA,UAC/B,KAAK;AAAA,QACP;AAAA,MACF;AACA,YAAM,kBAAkB;AAAA,QACtB,WAAW,cAAc;AAAA,QACzB,YAAY,aAAa;AAAA,QACzB,WAAW;AAAA,QACX,YAAY,WAAW;AAAA,MACzB;AAEA,YAAM,yBAAyB,gBAAgB,CAAC,MAAM,UAAU;AAC9D,cAAM,MAAM,OAAO,MAAM,KAAK;AAC9B,eAAO,YAAa,yBAAyB;AAAA,UAC3C,OAAO;AAAA,UACP,SAAS,SAAS,SAAS,CAAC,GAAG,eAAe,GAAG;AAAA,YAC/C,OAAO;AAAA,YACP;AAAA,YACA,SAAS;AAAA,YACT;AAAA,YACA,SAAS,SAAS,mBAAmB;AAAA,UACvC,CAAC;AAAA,QACH,GAAG;AAAA,UACD,SAAS,MAAM,CAAC,cAAc,MAAM,KAAK,CAAC;AAAA,QAC5C,CAAC;AAAA,MACH,IAAI,CAAC,MAAM,UAAU;AACnB,cAAM,MAAM,OAAO,MAAM,KAAK;AAC9B,eAAO,YAAa,cAAM,eAAc,eAAc,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,UAC9E,SAAS;AAAA,UACT,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,cAAc,iBAAiB;AAAA,UAC/B,WAAW;AAAA,UACX,gBAAgB;AAAA,UAChB,WAAW,SAAS,mBAAmB;AAAA,QACzC,CAAC,GAAG,IAAI;AAAA,MACV;AAEA,UAAI,WAAW,MAAM;AACrB,YAAM,mBAAmB;AAAA,QACvB,OAAO,cAAc,mBAAmB,QAAQ,OAAO;AAAA,QACvD,WAAW,GAAG,cAAc,KAAK,IAAI,cAAc,KAAK;AAAA,QACxD,cAAc;AAAA,QACd,SAAS;AAAA,MACX;AACA,UAAI,CAAC,eAAe;AAClB,cAAM,mBAAmB,cAAc;AACvC,mBAAW,MAAM,YAAa,cAAM,eAAc,eAAc,CAAC,GAAG,eAAe,GAAG,gBAAgB,GAAG;AAAA,UACvG,SAAS,MAAM,OAAO,qBAAqB,aAAa,iBAAiB,aAAa,KAAK,IAAI;AAAA,QACjG,CAAC;AAAA,MACH,WAAW,eAAe;AACxB,mBAAW,MAAM,YAAa,yBAAyB;AAAA,UACrD,SAAS,SAAS,SAAS,CAAC,GAAG,eAAe,GAAG,gBAAgB;AAAA,QACnE,GAAG;AAAA,UACD,SAAS,MAAM,CAAC,cAAc,aAAa,KAAK,CAAC;AAAA,QACnD,CAAC;AAAA,MACH;AACA,YAAM,eAAe,MAAM;AACzB,YAAI;AACJ,eAAO,YAAa,WAAW,eAAc;AAAA,UAC3C,MAAM;AAAA,UACN,SAAS,mBAAW,CAAC,WAAW,SAAS,WAAW,SAAS;AAAA,UAC7D,SAAS;AAAA,UACT,eAAe;AAAA,UACf,QAAQ,MAAM;AAAA,QAChB,GAAG,SAAS,GAAG;AAAA,UACb,SAAS,MAAM,CAAC,WAAW,MAAM,IAAI,sBAAsB,GAAG,SAAS,QAAQ,SAAS,IAAI,MAAM,UAAU,YAAa,cAAM,eAAc,eAAc,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,YACnL,SAAS,mBAAmB;AAAA,YAC5B,SAAS,GAAG,cAAc,KAAK;AAAA,YAC/B,gBAAgB;AAAA,YAChB,WAAW;AAAA,YACX,SAAS;AAAA,UACX,CAAC,GAAG;AAAA,YACF,SAAS,MAAM;AAAA,UACjB,CAAC,IAAI,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC;AAAA,QAC9E,CAAC;AAAA,MACH;AAEA,aAAO,YAAa,4BAAgB;AAAA,QAClC,YAAY,CAAC,aAAa;AAAA,QAC1B,YAAY;AAAA,MACd,GAAG;AAAA,QACD,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;AACD,SAAS,OAAO;AAChB,SAAS,aAAa;AACtB,SAAS,aAAa;AACtB,IAAO,mBAAQ;;;AC7Sf,IAAO,sBAAQ;", "names": ["overflowProps", "__rest", "restProps", "__rest"]}