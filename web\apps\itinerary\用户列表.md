# 全局公共参数

**全局Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| X-Token | {{zy_token}} | string | 是 | - |
| version | 2 | number | 是 | - |

**全局Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局认证方式**

> 无需认证

# 状态码说明

| 状态码 | 中文描述 |
| --- | ---- |
| 暂无参数 |

# 小红书小程序

> 创建人: 江汉波

> 更新人: 江汉波

> 创建时间: 2025-08-06 14:53:30

> 更新时间: 2025-08-06 14:53:30

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

## 后台

> 创建人: 江汉波

> 更新人: 江汉波

> 创建时间: 2025-08-11 13:53:48

> 更新时间: 2025-08-11 13:53:48

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

### 用户

> 创建人: 江汉波

> 更新人: 江汉波

> 创建时间: 2025-08-11 13:54:13

> 更新时间: 2025-08-11 13:54:13

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

#### 用户列表

> 创建人: 江汉波

> 更新人: 江汉波

> 创建时间: 2025-08-11 13:59:16

> 更新时间: 2025-08-11 14:12:43

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /admin/user?nickname=&phone=&page=&limit=

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| nickname | - | string | 是 | 昵称 |
| phone | - | string | 是 | 手机 |
| page | - | string | 是 | 当前页 |
| limit | - | string | 是 | 显示数量 |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
暂无数据
```

* 失败(404)

```javascript
暂无数据
```

**Query**

#### 禁用用户

> 创建人: 江汉波

> 更新人: 江汉波

> 创建时间: 2025-08-11 14:11:51

> 更新时间: 2025-08-11 14:11:51

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /admin/user/disabled/2

**请求方式**

> POST

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "status":0
}
```

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| status | 1 | number | 是 | 状态：0禁用 1正常 |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{"code":200,"message":"success","data":{"username":"江小白","nickname":null,"avatar":null,"gender":0,"email":null,"phone":"15711970154","status":0,"lastLoginTime":"2025-08-08 14:06:43","lastLoginIp":"*************"}}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | number | 表示API请求的状态码，用于标识请求是否成功或失败等状态。 |
| message | success | string | 用于返回关于API请求结果的简要描述信息。 |
| data | {} | object | 包含API请求相关的数据，具体结构和内容取决于API的功能。 |
| data.username | 江小白 | string | 代表用户的登录名或标识。 |
| data.nickname | 小白 | null | 用户的昵称，用于个性化展示。 |
| data.avatar | http://example.com/avatar.jpg | null | 用户头像的网络地址。 |
| data.gender | 0 | number | 表示用户性别的标识，0可能代表女性等。 |
| data.email | <EMAIL> | null | 用户的电子邮箱地址。 |
| data.phone | 15711970154 | string | 用户的联系手机号码。 |
| data.status | 0 | number | 状态：0禁用 1正常 |
| data.lastLoginTime | 2025-08-08 14:06:43 | string | 用户上次登录系统的时间。 |
| data.lastLoginIp | ************* | string | 用户上次登录时使用的IP地址。 |

* 失败(404)

```javascript
暂无数据
```

**Query**
