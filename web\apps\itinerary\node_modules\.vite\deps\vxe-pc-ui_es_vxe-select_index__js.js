import {
  select_default
} from "./chunk-XOASDV7L.js";
import "./chunk-ZMV4OEXL.js";
import "./chunk-U2MEA2VD.js";
import "./chunk-VZZZLHGO.js";
import "./chunk-AXQGUP5Q.js";
import "./chunk-XEMUQ5BX.js";
import {
  dynamicApp
} from "./chunk-2D57JIXU.js";
import "./chunk-ZOO3QRQU.js";
import {
  VxeUI
} from "./chunk-NQMB7AVD.js";
import "./chunk-5AGCXRTH.js";
import "./chunk-3IESGBRV.js";
import "./chunk-6RFCLEIL.js";
import "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.6.42_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/select/index.js
var VxeSelect = Object.assign(select_default, {
  install: function(app) {
    app.component(select_default.name, select_default);
  }
});
dynamicApp.use(VxeSelect);
VxeUI.component(select_default);
var Select = VxeSelect;
var select_default2 = VxeSelect;

// ../../node_modules/.pnpm/vxe-pc-ui@4.6.42_vue@3.5.17_typescript@5.8.3_/node_modules/vxe-pc-ui/es/vxe-select/index.js
var vxe_select_default = select_default2;
export {
  Select,
  VxeSelect,
  vxe_select_default as default
};
//# sourceMappingURL=vxe-pc-ui_es_vxe-select_index__js.js.map
