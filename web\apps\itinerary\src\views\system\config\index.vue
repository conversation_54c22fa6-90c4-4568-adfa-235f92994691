<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { Page } from '@vben/common-ui';
import {
  Button,
  Card,
  Input,
  Space,
  message,
  Popconfirm,
  Modal,
  Form,
  Textarea,
  InputNumber,
  Select,
  Row,
  Col,
  Statistic,
  Empty
} from 'ant-design-vue';
import {
  getConfigGroups,
  createConfigGroup,
  updateConfigGroup,
  deleteConfigGroup,
  type ConfigGroupDetail,
  type CreateConfigGroupParams,
  type UpdateConfigGroupParams,
  type ConfigGroup
} from '#/api/config';

const router = useRouter();

// 数据状态
const loading = ref(false);
const groupList = ref<ConfigGroupDetail[]>([]);
const searchKeyword = ref('');

// 弹窗相关
const modalVisible = ref(false);
const modalTitle = ref('');
const isEdit = ref(false);
const currentGroup = ref<ConfigGroupDetail | null>(null);

// 表单数据
const formData = ref<CreateConfigGroupParams>({
  group_key: 'system',
  group_name: '',
  description: '',
  icon: '',
  sort_order: 0,
});

// 配置分组选项
const configGroupOptions = [
  { value: 'system', label: '系统配置' },
  { value: 'app', label: '应用配置' },
  { value: 'user', label: '用户配置' },
  { value: 'business', label: '业务配置' },
];

// 分组图标选项
const iconOptions = [
  { value: 'ion:settings-outline', label: '设置' },
  { value: 'ion:apps-outline', label: '应用' },
  { value: 'ion:person-outline', label: '用户' },
  { value: 'ion:business-outline', label: '业务' },
  { value: 'ion:cog-outline', label: '配置' },
  { value: 'ion:folder-outline', label: '文件夹' },
];

// 计算属性：过滤后的分组列表
const filteredGroups = ref<ConfigGroupDetail[]>([]);

// 更新过滤列表
function updateFilteredGroups() {
  if (!searchKeyword.value) {
    filteredGroups.value = groupList.value;
  } else {
    const keyword = searchKeyword.value.toLowerCase();
    filteredGroups.value = groupList.value.filter(group =>
      group.group_name.toLowerCase().includes(keyword) ||
      group.description?.toLowerCase().includes(keyword)
    );
  }
}

// 获取分组列表
async function fetchGroups() {
  loading.value = true;
  try {
    const res = await getConfigGroups();
    groupList.value = res.groups || [];
    updateFilteredGroups();
  } catch (error) {
    message.error('获取配置分组失败');
  } finally {
    loading.value = false;
  }
}

// 搜索分组
function handleSearch() {
  updateFilteredGroups();
}

// 重置搜索
function handleReset() {
  searchKeyword.value = '';
  updateFilteredGroups();
}

// 新增分组
function handleAddGroup() {
  modalTitle.value = '新增配置分组';
  isEdit.value = false;
  currentGroup.value = null;
  resetForm();
  modalVisible.value = true;
}

// 编辑分组
function handleEditGroup(group: ConfigGroupDetail) {
  modalTitle.value = '编辑配置分组';
  isEdit.value = true;
  currentGroup.value = group;
  formData.value = {
    group_key: group.group_key,
    group_name: group.group_name,
    description: group.description || '',
    icon: group.icon || '',
    sort_order: group.sort_order || 0,
  };
  modalVisible.value = true;
}

// 删除分组
async function handleDeleteGroup(groupKey: string) {
  try {
    await deleteConfigGroup(groupKey);
    message.success('删除成功');
    fetchGroups();
  } catch (error) {
    message.error('删除失败');
  }
}

// 查看分组详情
function handleViewGroup(groupKey: string) {
  router.push(`/system/config/group/${groupKey}`);
}

// 保存分组
async function handleSave() {
  try {
    if (isEdit.value && currentGroup.value) {
      await updateConfigGroup(currentGroup.value.group_key, formData.value);
      message.success('更新成功');
    } else {
      await createConfigGroup(formData.value);
      message.success('创建成功');
    }
    modalVisible.value = false;
    fetchGroups();
  } catch (error) {
    message.error(isEdit.value ? '更新失败' : '创建失败');
  }
}

// 取消弹窗
function handleCancel() {
  modalVisible.value = false;
  resetForm();
}

// 重置表单
function resetForm() {
  formData.value = {
    group_key: 'system',
    group_name: '',
    description: '',
    icon: '',
    sort_order: 0,
  };
}

onMounted(fetchGroups);
</script>

<template>
  <div>
    <Page title="配置管理">
      <!-- 搜索和操作栏 -->
      <Card class="mb-4">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <Input
              v-model:value="searchKeyword"
              placeholder="搜索分组名称或描述"
              allow-clear
              style="width: 300px"
              @input="handleSearch"
            />
            <Button @click="handleReset">重置</Button>
          </div>
          <Button type="primary" @click="handleAddGroup">新增分组</Button>
        </div>
      </Card>

      <!-- 分组卡片列表 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <Card
          v-for="group in filteredGroups"
          :key="group.id"
          :loading="loading"
          class="hover:shadow-lg transition-shadow cursor-pointer"
          @click="handleViewGroup(group.id)"
        >
          <template #title>
            <div class="flex items-center space-x-2">
              <span class="text-lg">{{ group.icon || '📁' }}</span>
              <span>{{ group.group_name }}</span>
            </div>
          </template>
          <template #extra>
            <Space>
              <Button
                type="text"
                size="small"
                @click.stop="handleEditGroup(group)"
              >
                编辑
              </Button>
              <Popconfirm
                title="确认删除该分组？"
                @confirm="handleDeleteGroup(group.group_key)"
                @click.stop
              >
                <Button type="text" size="small" danger>删除</Button>
              </Popconfirm>
            </Space>
          </template>

          <div class="space-y-3">
            <p class="text-gray-600 text-sm min-h-[40px]">
              {{ group.description || '暂无描述' }}
            </p>
            <div class="flex justify-between items-center text-sm">
              <span class="text-gray-500">配置项数量</span>
              <span class="font-semibold text-blue-600">{{ group.config_count || 0 }}</span>
            </div>
            <div class="flex justify-between items-center text-sm">
              <span class="text-gray-500">创建时间</span>
              <span class="text-gray-600">{{ group.created_at }}</span>
            </div>
          </div>
        </Card>

        <!-- 空状态 -->
        <div v-if="!loading && filteredGroups.length === 0" class="col-span-full">
          <Empty description="暂无配置分组" />
        </div>
      </div>
    </Page>

    <!-- 分组弹窗 -->
    <Modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="600px"
      @ok="handleSave"
      @cancel="handleCancel"
    >
      <Form :model="formData" layout="vertical">
        <Form.Item label="分组键" required>
          <Select
            v-model:value="formData.group_key"
            placeholder="选择分组键"
            :disabled="isEdit"
          >
            <Select.Option v-for="option in configGroupOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="分组名称" required>
          <Input v-model:value="formData.group_name" placeholder="请输入分组名称" />
        </Form.Item>
        <Form.Item label="分组图标">
          <Select v-model:value="formData.icon" placeholder="选择图标" allow-clear>
            <Select.Option v-for="option in iconOptions" :key="option.value" :value="option.value">
              <span>{{ option.value.includes('ion:') ? '🔧' : option.value }} {{ option.label }}</span>
            </Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="描述">
          <Textarea v-model:value="formData.description" placeholder="请输入分组描述" :rows="3" />
        </Form.Item>
        <Form.Item label="排序">
          <InputNumber v-model:value="formData.sort_order" :min="0" style="width: 100%" />
        </Form.Item>
      </Form>
    </Modal>
  </div>
</template>
