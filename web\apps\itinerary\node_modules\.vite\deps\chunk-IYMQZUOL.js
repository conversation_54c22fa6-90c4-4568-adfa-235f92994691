import {
  CalendarOutlined_default,
  ClockCircleOutlined_default,
  SwapRightOutlined_default
} from "./chunk-NUKNDUR6.js";
import {
  shallowequal_default
} from "./chunk-ZRV6AM63.js";
import {
  genActiveStyle,
  genBasicInputStyle,
  genHoverStyle,
  initInputToken
} from "./chunk-DBXUWOF6.js";
import {
  isPresetColor,
  isPresetStatusColor
} from "./chunk-MII2IA65.js";
import {
  button_default
} from "./chunk-EVRSOU6N.js";
import {
  wave_default
} from "./chunk-RITNDQGS.js";
import {
  useMemo
} from "./chunk-CU3CGNH3.js";
import {
  useMergedState
} from "./chunk-MUT3H2QD.js";
import {
  CloseOutlined_default
} from "./chunk-IGAEXFDE.js";
import {
  CloseCircleFilled_default
} from "./chunk-KW6CXKWV.js";
import {
  getMergedStatus,
  getStatusClassNames
} from "./chunk-UXUQURGQ.js";
import {
  omit_default
} from "./chunk-HN4DNUMZ.js";
import {
  initMoveMotion,
  initSlideMotion,
  slideDownIn,
  slideDownOut,
  slideUpIn,
  slideUpOut,
  vc_trigger_default
} from "./chunk-3CD3MHN3.js";
import {
  KeyCode_default
} from "./chunk-LSZPS4UL.js";
import {
  cloneElement
} from "./chunk-BWL5KWVI.js";
import {
  useState
} from "./chunk-3D7FXVUD.js";
import {
  genCompactItemStyle
} from "./chunk-E4JG6O6U.js";
import {
  isVisible_default
} from "./chunk-FASTSLIU.js";
import {
  wrapperRaf
} from "./chunk-Q2SDZBKG.js";
import {
  useCompactItemContext
} from "./chunk-PEPD45WG.js";
import {
  FormItemInputContext,
  useInjectFormItemContext
} from "./chunk-ZSGXYBNN.js";
import {
  devWarning_default
} from "./chunk-2TUNZNV7.js";
import {
  vue_types_default
} from "./chunk-SS23FT44.js";
import {
  _objectSpread2,
  arrayType,
  booleanType,
  capitalize,
  classNames_default,
  eventType,
  functionType,
  genComponentStyleHook,
  genPresetColor,
  merge,
  noteOnce,
  objectType,
  resetComponent,
  roundedArrow,
  someType,
  stringType,
  textEllipsis,
  useConfigInject_default,
  useLocaleReceiver,
  warning,
  warning_default,
  warning_default2
} from "./chunk-7CXY6ZY4.js";
import {
  TinyColor
} from "./chunk-DOLVIAJN.js";
import {
  en_US_default2 as en_US_default
} from "./chunk-PR6BNF3W.js";
import {
  _extends
} from "./chunk-YTNASAWS.js";
import {
  Fragment,
  computed,
  createTextVNode,
  createVNode,
  defineComponent,
  getCurrentInstance,
  getCurrentScope,
  inject,
  nextTick,
  onBeforeUnmount,
  onBeforeUpdate,
  onMounted,
  onScopeDispose,
  provide,
  ref,
  shallowRef,
  toRef,
  unref,
  useAttrs,
  watch,
  watchEffect
} from "./chunk-3IESGBRV.js";
import {
  require_dayjs_min
} from "./chunk-6DTVAI5U.js";
import {
  __commonJS,
  __toESM
} from "./chunk-EWTE5DHJ.js";

// ../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekday.js
var require_weekday = __commonJS({
  "../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekday.js"(exports, module) {
    !function(e, t) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : (e = "undefined" != typeof globalThis ? globalThis : e || self).dayjs_plugin_weekday = t();
    }(exports, function() {
      "use strict";
      return function(e, t) {
        t.prototype.weekday = function(e2) {
          var t2 = this.$locale().weekStart || 0, i = this.$W, n = (i < t2 ? i + 7 : i) - t2;
          return this.$utils().u(e2) ? n : this.subtract(n, "day").add(e2, "day");
        };
      };
    });
  }
});

// ../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/localeData.js
var require_localeData = __commonJS({
  "../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/localeData.js"(exports, module) {
    !function(n, e) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define(e) : (n = "undefined" != typeof globalThis ? globalThis : n || self).dayjs_plugin_localeData = e();
    }(exports, function() {
      "use strict";
      return function(n, e, t) {
        var r = e.prototype, o = function(n2) {
          return n2 && (n2.indexOf ? n2 : n2.s);
        }, u = function(n2, e2, t2, r2, u2) {
          var i2 = n2.name ? n2 : n2.$locale(), a2 = o(i2[e2]), s2 = o(i2[t2]), f = a2 || s2.map(function(n3) {
            return n3.slice(0, r2);
          });
          if (!u2) return f;
          var d = i2.weekStart;
          return f.map(function(n3, e3) {
            return f[(e3 + (d || 0)) % 7];
          });
        }, i = function() {
          return t.Ls[t.locale()];
        }, a = function(n2, e2) {
          return n2.formats[e2] || function(n3) {
            return n3.replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g, function(n4, e3, t2) {
              return e3 || t2.slice(1);
            });
          }(n2.formats[e2.toUpperCase()]);
        }, s = function() {
          var n2 = this;
          return { months: function(e2) {
            return e2 ? e2.format("MMMM") : u(n2, "months");
          }, monthsShort: function(e2) {
            return e2 ? e2.format("MMM") : u(n2, "monthsShort", "months", 3);
          }, firstDayOfWeek: function() {
            return n2.$locale().weekStart || 0;
          }, weekdays: function(e2) {
            return e2 ? e2.format("dddd") : u(n2, "weekdays");
          }, weekdaysMin: function(e2) {
            return e2 ? e2.format("dd") : u(n2, "weekdaysMin", "weekdays", 2);
          }, weekdaysShort: function(e2) {
            return e2 ? e2.format("ddd") : u(n2, "weekdaysShort", "weekdays", 3);
          }, longDateFormat: function(e2) {
            return a(n2.$locale(), e2);
          }, meridiem: this.$locale().meridiem, ordinal: this.$locale().ordinal };
        };
        r.localeData = function() {
          return s.bind(this)();
        }, t.localeData = function() {
          var n2 = i();
          return { firstDayOfWeek: function() {
            return n2.weekStart || 0;
          }, weekdays: function() {
            return t.weekdays();
          }, weekdaysShort: function() {
            return t.weekdaysShort();
          }, weekdaysMin: function() {
            return t.weekdaysMin();
          }, months: function() {
            return t.months();
          }, monthsShort: function() {
            return t.monthsShort();
          }, longDateFormat: function(e2) {
            return a(n2, e2);
          }, meridiem: n2.meridiem, ordinal: n2.ordinal };
        }, t.months = function() {
          return u(i(), "months");
        }, t.monthsShort = function() {
          return u(i(), "monthsShort", "months", 3);
        }, t.weekdays = function(n2) {
          return u(i(), "weekdays", null, null, n2);
        }, t.weekdaysShort = function(n2) {
          return u(i(), "weekdaysShort", "weekdays", 3, n2);
        }, t.weekdaysMin = function(n2) {
          return u(i(), "weekdaysMin", "weekdays", 2, n2);
        };
      };
    });
  }
});

// ../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekOfYear.js
var require_weekOfYear = __commonJS({
  "../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekOfYear.js"(exports, module) {
    !function(e, t) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : (e = "undefined" != typeof globalThis ? globalThis : e || self).dayjs_plugin_weekOfYear = t();
    }(exports, function() {
      "use strict";
      var e = "week", t = "year";
      return function(i, n, r) {
        var f = n.prototype;
        f.week = function(i2) {
          if (void 0 === i2 && (i2 = null), null !== i2) return this.add(7 * (i2 - this.week()), "day");
          var n2 = this.$locale().yearStart || 1;
          if (11 === this.month() && this.date() > 25) {
            var f2 = r(this).startOf(t).add(1, t).date(n2), s = r(this).endOf(e);
            if (f2.isBefore(s)) return 1;
          }
          var a = r(this).startOf(t).date(n2).startOf(e).subtract(1, "millisecond"), o = this.diff(a, e, true);
          return o < 0 ? r(this).startOf("week").week() : Math.ceil(o);
        }, f.weeks = function(e2) {
          return void 0 === e2 && (e2 = null), this.week(e2);
        };
      };
    });
  }
});

// ../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekYear.js
var require_weekYear = __commonJS({
  "../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekYear.js"(exports, module) {
    !function(e, t) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : (e = "undefined" != typeof globalThis ? globalThis : e || self).dayjs_plugin_weekYear = t();
    }(exports, function() {
      "use strict";
      return function(e, t) {
        t.prototype.weekYear = function() {
          var e2 = this.month(), t2 = this.week(), n = this.year();
          return 1 === t2 && 11 === e2 ? n + 1 : 0 === e2 && t2 >= 52 ? n - 1 : n;
        };
      };
    });
  }
});

// ../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/quarterOfYear.js
var require_quarterOfYear = __commonJS({
  "../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/quarterOfYear.js"(exports, module) {
    !function(t, n) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = n() : "function" == typeof define && define.amd ? define(n) : (t = "undefined" != typeof globalThis ? globalThis : t || self).dayjs_plugin_quarterOfYear = n();
    }(exports, function() {
      "use strict";
      var t = "month", n = "quarter";
      return function(e, i) {
        var r = i.prototype;
        r.quarter = function(t2) {
          return this.$utils().u(t2) ? Math.ceil((this.month() + 1) / 3) : this.month(this.month() % 3 + 3 * (t2 - 1));
        };
        var s = r.add;
        r.add = function(e2, i2) {
          return e2 = Number(e2), this.$utils().p(i2) === n ? this.add(3 * e2, t) : s.bind(this)(e2, i2);
        };
        var u = r.startOf;
        r.startOf = function(e2, i2) {
          var r2 = this.$utils(), s2 = !!r2.u(i2) || i2;
          if (r2.p(e2) === n) {
            var o = this.quarter() - 1;
            return s2 ? this.month(3 * o).startOf(t).startOf("day") : this.month(3 * o + 2).endOf(t).endOf("day");
          }
          return u.bind(this)(e2, i2);
        };
      };
    });
  }
});

// ../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/advancedFormat.js
var require_advancedFormat = __commonJS({
  "../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/advancedFormat.js"(exports, module) {
    !function(e, t) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : (e = "undefined" != typeof globalThis ? globalThis : e || self).dayjs_plugin_advancedFormat = t();
    }(exports, function() {
      "use strict";
      return function(e, t) {
        var r = t.prototype, n = r.format;
        r.format = function(e2) {
          var t2 = this, r2 = this.$locale();
          if (!this.isValid()) return n.bind(this)(e2);
          var s = this.$utils(), a = (e2 || "YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g, function(e3) {
            switch (e3) {
              case "Q":
                return Math.ceil((t2.$M + 1) / 3);
              case "Do":
                return r2.ordinal(t2.$D);
              case "gggg":
                return t2.weekYear();
              case "GGGG":
                return t2.isoWeekYear();
              case "wo":
                return r2.ordinal(t2.week(), "W");
              case "w":
              case "ww":
                return s.s(t2.week(), "w" === e3 ? 1 : 2, "0");
              case "W":
              case "WW":
                return s.s(t2.isoWeek(), "W" === e3 ? 1 : 2, "0");
              case "k":
              case "kk":
                return s.s(String(0 === t2.$H ? 24 : t2.$H), "k" === e3 ? 1 : 2, "0");
              case "X":
                return Math.floor(t2.$d.getTime() / 1e3);
              case "x":
                return t2.$d.getTime();
              case "z":
                return "[" + t2.offsetName() + "]";
              case "zzz":
                return "[" + t2.offsetName("long") + "]";
              default:
                return e3;
            }
          });
          return n.bind(this)(a);
        };
      };
    });
  }
});

// ../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/customParseFormat.js
var require_customParseFormat = __commonJS({
  "../../node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/customParseFormat.js"(exports, module) {
    !function(e, t) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : (e = "undefined" != typeof globalThis ? globalThis : e || self).dayjs_plugin_customParseFormat = t();
    }(exports, function() {
      "use strict";
      var e = { LTS: "h:mm:ss A", LT: "h:mm A", L: "MM/DD/YYYY", LL: "MMMM D, YYYY", LLL: "MMMM D, YYYY h:mm A", LLLL: "dddd, MMMM D, YYYY h:mm A" }, t = /(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g, n = /\d/, r = /\d\d/, i = /\d\d?/, o = /\d*[^-_:/,()\s\d]+/, s = {}, a = function(e2) {
        return (e2 = +e2) + (e2 > 68 ? 1900 : 2e3);
      };
      var f = function(e2) {
        return function(t2) {
          this[e2] = +t2;
        };
      }, h = [/[+-]\d\d:?(\d\d)?|Z/, function(e2) {
        (this.zone || (this.zone = {})).offset = function(e3) {
          if (!e3) return 0;
          if ("Z" === e3) return 0;
          var t2 = e3.match(/([+-]|\d\d)/g), n2 = 60 * t2[1] + (+t2[2] || 0);
          return 0 === n2 ? 0 : "+" === t2[0] ? -n2 : n2;
        }(e2);
      }], u = function(e2) {
        var t2 = s[e2];
        return t2 && (t2.indexOf ? t2 : t2.s.concat(t2.f));
      }, d = function(e2, t2) {
        var n2, r2 = s.meridiem;
        if (r2) {
          for (var i2 = 1; i2 <= 24; i2 += 1) if (e2.indexOf(r2(i2, 0, t2)) > -1) {
            n2 = i2 > 12;
            break;
          }
        } else n2 = e2 === (t2 ? "pm" : "PM");
        return n2;
      }, c = { A: [o, function(e2) {
        this.afternoon = d(e2, false);
      }], a: [o, function(e2) {
        this.afternoon = d(e2, true);
      }], Q: [n, function(e2) {
        this.month = 3 * (e2 - 1) + 1;
      }], S: [n, function(e2) {
        this.milliseconds = 100 * +e2;
      }], SS: [r, function(e2) {
        this.milliseconds = 10 * +e2;
      }], SSS: [/\d{3}/, function(e2) {
        this.milliseconds = +e2;
      }], s: [i, f("seconds")], ss: [i, f("seconds")], m: [i, f("minutes")], mm: [i, f("minutes")], H: [i, f("hours")], h: [i, f("hours")], HH: [i, f("hours")], hh: [i, f("hours")], D: [i, f("day")], DD: [r, f("day")], Do: [o, function(e2) {
        var t2 = s.ordinal, n2 = e2.match(/\d+/);
        if (this.day = n2[0], t2) for (var r2 = 1; r2 <= 31; r2 += 1) t2(r2).replace(/\[|\]/g, "") === e2 && (this.day = r2);
      }], w: [i, f("week")], ww: [r, f("week")], M: [i, f("month")], MM: [r, f("month")], MMM: [o, function(e2) {
        var t2 = u("months"), n2 = (u("monthsShort") || t2.map(function(e3) {
          return e3.slice(0, 3);
        })).indexOf(e2) + 1;
        if (n2 < 1) throw new Error();
        this.month = n2 % 12 || n2;
      }], MMMM: [o, function(e2) {
        var t2 = u("months").indexOf(e2) + 1;
        if (t2 < 1) throw new Error();
        this.month = t2 % 12 || t2;
      }], Y: [/[+-]?\d+/, f("year")], YY: [r, function(e2) {
        this.year = a(e2);
      }], YYYY: [/\d{4}/, f("year")], Z: h, ZZ: h };
      function l(n2) {
        var r2, i2;
        r2 = n2, i2 = s && s.formats;
        for (var o2 = (n2 = r2.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g, function(t2, n3, r3) {
          var o3 = r3 && r3.toUpperCase();
          return n3 || i2[r3] || e[r3] || i2[o3].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g, function(e2, t3, n4) {
            return t3 || n4.slice(1);
          });
        })).match(t), a2 = o2.length, f2 = 0; f2 < a2; f2 += 1) {
          var h2 = o2[f2], u2 = c[h2], d2 = u2 && u2[0], l2 = u2 && u2[1];
          o2[f2] = l2 ? { regex: d2, parser: l2 } : h2.replace(/^\[|\]$/g, "");
        }
        return function(e2) {
          for (var t2 = {}, n3 = 0, r3 = 0; n3 < a2; n3 += 1) {
            var i3 = o2[n3];
            if ("string" == typeof i3) r3 += i3.length;
            else {
              var s2 = i3.regex, f3 = i3.parser, h3 = e2.slice(r3), u3 = s2.exec(h3)[0];
              f3.call(t2, u3), e2 = e2.replace(u3, "");
            }
          }
          return function(e3) {
            var t3 = e3.afternoon;
            if (void 0 !== t3) {
              var n4 = e3.hours;
              t3 ? n4 < 12 && (e3.hours += 12) : 12 === n4 && (e3.hours = 0), delete e3.afternoon;
            }
          }(t2), t2;
        };
      }
      return function(e2, t2, n2) {
        n2.p.customParseFormat = true, e2 && e2.parseTwoDigitYear && (a = e2.parseTwoDigitYear);
        var r2 = t2.prototype, i2 = r2.parse;
        r2.parse = function(e3) {
          var t3 = e3.date, r3 = e3.utc, o2 = e3.args;
          this.$u = r3;
          var a2 = o2[1];
          if ("string" == typeof a2) {
            var f2 = true === o2[2], h2 = true === o2[3], u2 = f2 || h2, d2 = o2[2];
            h2 && (d2 = o2[2]), s = this.$locale(), !f2 && d2 && (s = n2.Ls[d2]), this.$d = function(e4, t4, n3, r4) {
              try {
                if (["x", "X"].indexOf(t4) > -1) return new Date(("X" === t4 ? 1e3 : 1) * e4);
                var i3 = l(t4)(e4), o3 = i3.year, s2 = i3.month, a3 = i3.day, f3 = i3.hours, h3 = i3.minutes, u3 = i3.seconds, d3 = i3.milliseconds, c3 = i3.zone, m2 = i3.week, M2 = /* @__PURE__ */ new Date(), Y = a3 || (o3 || s2 ? 1 : M2.getDate()), p = o3 || M2.getFullYear(), v = 0;
                o3 && !s2 || (v = s2 > 0 ? s2 - 1 : M2.getMonth());
                var D, w = f3 || 0, g = h3 || 0, y = u3 || 0, L = d3 || 0;
                return c3 ? new Date(Date.UTC(p, v, Y, w, g, y, L + 60 * c3.offset * 1e3)) : n3 ? new Date(Date.UTC(p, v, Y, w, g, y, L)) : (D = new Date(p, v, Y, w, g, y, L), m2 && (D = r4(D).week(m2).toDate()), D);
              } catch (e5) {
                return /* @__PURE__ */ new Date("");
              }
            }(t3, a2, r3, n2), this.init(), d2 && true !== d2 && (this.$L = this.locale(d2).$L), u2 && t3 != this.format(a2) && (this.$d = /* @__PURE__ */ new Date("")), s = {};
          } else if (a2 instanceof Array) for (var c2 = a2.length, m = 1; m <= c2; m += 1) {
            o2[1] = a2[m - 1];
            var M = n2.apply(this, o2);
            if (M.isValid()) {
              this.$d = M.$d, this.$L = M.$L, this.init();
              break;
            }
            m === c2 && (this.$d = /* @__PURE__ */ new Date(""));
          }
          else i2.call(this, e3);
        };
      };
    });
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/tag/style/index.js
var genTagStatusStyle = (token, status, cssVariableType) => {
  const capitalizedCssVariableType = capitalize(cssVariableType);
  return {
    [`${token.componentCls}-${status}`]: {
      color: token[`color${cssVariableType}`],
      background: token[`color${capitalizedCssVariableType}Bg`],
      borderColor: token[`color${capitalizedCssVariableType}Border`],
      [`&${token.componentCls}-borderless`]: {
        borderColor: "transparent"
      }
    }
  };
};
var genPresetStyle = (token) => genPresetColor(token, (colorKey, _ref) => {
  let {
    textColor,
    lightBorderColor,
    lightColor,
    darkColor
  } = _ref;
  return {
    [`${token.componentCls}-${colorKey}`]: {
      color: textColor,
      background: lightColor,
      borderColor: lightBorderColor,
      // Inverse color
      "&-inverse": {
        color: token.colorTextLightSolid,
        background: darkColor,
        borderColor: darkColor
      },
      [`&${token.componentCls}-borderless`]: {
        borderColor: "transparent"
      }
    }
  };
});
var genBaseStyle = (token) => {
  const {
    paddingXXS,
    lineWidth,
    tagPaddingHorizontal,
    componentCls
  } = token;
  const paddingInline = tagPaddingHorizontal - lineWidth;
  const iconMarginInline = paddingXXS - lineWidth;
  return {
    // Result
    [componentCls]: _extends(_extends({}, resetComponent(token)), {
      display: "inline-block",
      height: "auto",
      marginInlineEnd: token.marginXS,
      paddingInline,
      fontSize: token.tagFontSize,
      lineHeight: `${token.tagLineHeight}px`,
      whiteSpace: "nowrap",
      background: token.tagDefaultBg,
      border: `${token.lineWidth}px ${token.lineType} ${token.colorBorder}`,
      borderRadius: token.borderRadiusSM,
      opacity: 1,
      transition: `all ${token.motionDurationMid}`,
      textAlign: "start",
      // RTL
      [`&${componentCls}-rtl`]: {
        direction: "rtl"
      },
      "&, a, a:hover": {
        color: token.tagDefaultColor
      },
      [`${componentCls}-close-icon`]: {
        marginInlineStart: iconMarginInline,
        color: token.colorTextDescription,
        fontSize: token.tagIconSize,
        cursor: "pointer",
        transition: `all ${token.motionDurationMid}`,
        "&:hover": {
          color: token.colorTextHeading
        }
      },
      [`&${componentCls}-has-color`]: {
        borderColor: "transparent",
        [`&, a, a:hover, ${token.iconCls}-close, ${token.iconCls}-close:hover`]: {
          color: token.colorTextLightSolid
        }
      },
      [`&-checkable`]: {
        backgroundColor: "transparent",
        borderColor: "transparent",
        cursor: "pointer",
        [`&:not(${componentCls}-checkable-checked):hover`]: {
          color: token.colorPrimary,
          backgroundColor: token.colorFillSecondary
        },
        "&:active, &-checked": {
          color: token.colorTextLightSolid
        },
        "&-checked": {
          backgroundColor: token.colorPrimary,
          "&:hover": {
            backgroundColor: token.colorPrimaryHover
          }
        },
        "&:active": {
          backgroundColor: token.colorPrimaryActive
        }
      },
      [`&-hidden`]: {
        display: "none"
      },
      // To ensure that a space will be placed between character and `Icon`.
      [`> ${token.iconCls} + span, > span + ${token.iconCls}`]: {
        marginInlineStart: paddingInline
      }
    }),
    [`${componentCls}-borderless`]: {
      borderColor: "transparent",
      background: token.tagBorderlessBg
    }
  };
};
var style_default = genComponentStyleHook("Tag", (token) => {
  const {
    fontSize,
    lineHeight,
    lineWidth,
    fontSizeIcon
  } = token;
  const tagHeight = Math.round(fontSize * lineHeight);
  const tagFontSize = token.fontSizeSM;
  const tagLineHeight = tagHeight - lineWidth * 2;
  const tagDefaultBg = token.colorFillAlter;
  const tagDefaultColor = token.colorText;
  const tagToken = merge(token, {
    tagFontSize,
    tagLineHeight,
    tagDefaultBg,
    tagDefaultColor,
    tagIconSize: fontSizeIcon - 2 * lineWidth,
    tagPaddingHorizontal: 8,
    tagBorderlessBg: token.colorFillTertiary
  });
  return [genBaseStyle(tagToken), genPresetStyle(tagToken), genTagStatusStyle(tagToken, "success", "Success"), genTagStatusStyle(tagToken, "processing", "Info"), genTagStatusStyle(tagToken, "error", "Error"), genTagStatusStyle(tagToken, "warning", "Warning")];
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/tag/CheckableTag.js
var checkableTagProps = () => ({
  prefixCls: String,
  checked: {
    type: Boolean,
    default: void 0
  },
  onChange: {
    type: Function
  },
  onClick: {
    type: Function
  },
  "onUpdate:checked": Function
});
var CheckableTag = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ACheckableTag",
  inheritAttrs: false,
  props: checkableTagProps(),
  // emits: ['update:checked', 'change', 'click'],
  setup(props, _ref) {
    let {
      slots,
      emit,
      attrs
    } = _ref;
    const {
      prefixCls
    } = useConfigInject_default("tag", props);
    const [wrapSSR, hashId] = style_default(prefixCls);
    const handleClick = (e) => {
      const {
        checked
      } = props;
      emit("update:checked", !checked);
      emit("change", !checked);
      emit("click", e);
    };
    const cls = computed(() => classNames_default(prefixCls.value, hashId.value, {
      [`${prefixCls.value}-checkable`]: true,
      [`${prefixCls.value}-checkable-checked`]: props.checked
    }));
    return () => {
      var _a2;
      return wrapSSR(createVNode("span", _objectSpread2(_objectSpread2({}, attrs), {}, {
        "class": [cls.value, attrs.class],
        "onClick": handleClick
      }), [(_a2 = slots.default) === null || _a2 === void 0 ? void 0 : _a2.call(slots)]));
    };
  }
});
var CheckableTag_default = CheckableTag;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/tag/index.js
var tagProps = () => ({
  prefixCls: String,
  color: {
    type: String
  },
  closable: {
    type: Boolean,
    default: false
  },
  closeIcon: vue_types_default.any,
  /** @deprecated `visible` will be removed in next major version. */
  visible: {
    type: Boolean,
    default: void 0
  },
  onClose: {
    type: Function
  },
  onClick: eventType(),
  "onUpdate:visible": Function,
  icon: vue_types_default.any,
  bordered: {
    type: Boolean,
    default: true
  }
});
var Tag = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "ATag",
  inheritAttrs: false,
  props: tagProps(),
  // emits: ['update:visible', 'close'],
  slots: Object,
  setup(props, _ref) {
    let {
      slots,
      emit,
      attrs
    } = _ref;
    const {
      prefixCls,
      direction
    } = useConfigInject_default("tag", props);
    const [wrapSSR, hashId] = style_default(prefixCls);
    const visible = shallowRef(true);
    if (true) {
      warning_default2(props.visible === void 0, "Tag", '`visible` is deprecated, please use `<Tag v-show="visible" />` instead.');
    }
    watchEffect(() => {
      if (props.visible !== void 0) {
        visible.value = props.visible;
      }
    });
    const handleCloseClick = (e) => {
      e.stopPropagation();
      emit("update:visible", false);
      emit("close", e);
      if (e.defaultPrevented) {
        return;
      }
      if (props.visible === void 0) {
        visible.value = false;
      }
    };
    const isInternalColor = computed(() => isPresetColor(props.color) || isPresetStatusColor(props.color));
    const tagClassName = computed(() => classNames_default(prefixCls.value, hashId.value, {
      [`${prefixCls.value}-${props.color}`]: isInternalColor.value,
      [`${prefixCls.value}-has-color`]: props.color && !isInternalColor.value,
      [`${prefixCls.value}-hidden`]: !visible.value,
      [`${prefixCls.value}-rtl`]: direction.value === "rtl",
      [`${prefixCls.value}-borderless`]: !props.bordered
    }));
    const handleClick = (e) => {
      emit("click", e);
    };
    return () => {
      var _a2, _b, _c;
      const {
        icon = (_a2 = slots.icon) === null || _a2 === void 0 ? void 0 : _a2.call(slots),
        color,
        closeIcon = (_b = slots.closeIcon) === null || _b === void 0 ? void 0 : _b.call(slots),
        closable = false
      } = props;
      const renderCloseIcon = () => {
        if (closable) {
          return closeIcon ? createVNode("span", {
            "class": `${prefixCls.value}-close-icon`,
            "onClick": handleCloseClick
          }, [closeIcon]) : createVNode(CloseOutlined_default, {
            "class": `${prefixCls.value}-close-icon`,
            "onClick": handleCloseClick
          }, null);
        }
        return null;
      };
      const tagStyle = {
        backgroundColor: color && !isInternalColor.value ? color : void 0
      };
      const iconNode = icon || null;
      const children = (_c = slots.default) === null || _c === void 0 ? void 0 : _c.call(slots);
      const kids = iconNode ? createVNode(Fragment, null, [iconNode, createVNode("span", null, [children])]) : children;
      const isNeedWave = props.onClick !== void 0;
      const tagNode = createVNode("span", _objectSpread2(_objectSpread2({}, attrs), {}, {
        "onClick": handleClick,
        "class": [tagClassName.value, attrs.class],
        "style": [tagStyle, attrs.style]
      }), [kids, renderCloseIcon()]);
      return wrapSSR(isNeedWave ? createVNode(wave_default, null, {
        default: () => [tagNode]
      }) : tagNode);
    };
  }
});
Tag.CheckableTag = CheckableTag_default;
Tag.install = function(app) {
  app.component(Tag.name, Tag);
  app.component(CheckableTag_default.name, CheckableTag_default);
  return app;
};
var tag_default = Tag;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/generate/dayjs.js
var import_dayjs = __toESM(require_dayjs_min());
var import_weekday = __toESM(require_weekday());
var import_localeData = __toESM(require_localeData());
var import_weekOfYear = __toESM(require_weekOfYear());
var import_weekYear = __toESM(require_weekYear());
var import_quarterOfYear = __toESM(require_quarterOfYear());
var import_advancedFormat = __toESM(require_advancedFormat());
var import_customParseFormat = __toESM(require_customParseFormat());
import_dayjs.default.extend(import_customParseFormat.default);
import_dayjs.default.extend(import_advancedFormat.default);
import_dayjs.default.extend(import_weekday.default);
import_dayjs.default.extend(import_localeData.default);
import_dayjs.default.extend(import_weekOfYear.default);
import_dayjs.default.extend(import_weekYear.default);
import_dayjs.default.extend(import_quarterOfYear.default);
import_dayjs.default.extend((_o, c) => {
  const proto = c.prototype;
  const oldFormat = proto.format;
  proto.format = function f(formatStr) {
    const str = (formatStr || "").replace("Wo", "wo");
    return oldFormat.bind(this)(str);
  };
});
var localeMap = {
  // ar_EG:
  // az_AZ:
  // bg_BG:
  bn_BD: "bn-bd",
  by_BY: "be",
  // ca_ES:
  // cs_CZ:
  // da_DK:
  // de_DE:
  // el_GR:
  en_GB: "en-gb",
  en_US: "en",
  // es_ES:
  // et_EE:
  // fa_IR:
  // fi_FI:
  fr_BE: "fr",
  fr_CA: "fr-ca",
  // fr_FR:
  // ga_IE:
  // gl_ES:
  // he_IL:
  // hi_IN:
  // hr_HR:
  // hu_HU:
  hy_AM: "hy-am",
  // id_ID:
  // is_IS:
  // it_IT:
  // ja_JP:
  // ka_GE:
  // kk_KZ:
  // km_KH:
  kmr_IQ: "ku",
  // kn_IN:
  // ko_KR:
  // ku_IQ: // previous ku in antd
  // lt_LT:
  // lv_LV:
  // mk_MK:
  // ml_IN:
  // mn_MN:
  // ms_MY:
  // nb_NO:
  // ne_NP:
  nl_BE: "nl-be",
  // nl_NL:
  // pl_PL:
  pt_BR: "pt-br",
  // pt_PT:
  // ro_RO:
  // ru_RU:
  // sk_SK:
  // sl_SI:
  // sr_RS:
  // sv_SE:
  // ta_IN:
  // th_TH:
  // tr_TR:
  // uk_UA:
  // ur_PK:
  // vi_VN:
  zh_CN: "zh-cn",
  zh_HK: "zh-hk",
  zh_TW: "zh-tw"
};
var parseLocale = (locale) => {
  const mapLocale = localeMap[locale];
  return mapLocale || locale.split("_")[0];
};
var parseNoMatchNotice = () => {
  noteOnce(false, "Not match any format. Please help to fire a issue about this.");
};
var advancedFormatRegex = /\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|k{1,2}|S/g;
function findTargetStr(val, index, segmentation) {
  const items = [...new Set(val.split(segmentation))];
  let idx = 0;
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    idx += item.length;
    if (idx > index) {
      return item;
    }
    idx += segmentation.length;
  }
}
var toDateWithValueFormat = (val, valueFormat) => {
  if (!val) return null;
  if (import_dayjs.default.isDayjs(val)) {
    return val;
  }
  const matchs = valueFormat.matchAll(advancedFormatRegex);
  let baseDate = (0, import_dayjs.default)(val, valueFormat);
  if (matchs === null) {
    return baseDate;
  }
  for (const match of matchs) {
    const origin = match[0];
    const index = match["index"];
    if (origin === "Q") {
      const segmentation = val.slice(index - 1, index);
      const quarterStr = findTargetStr(val, index, segmentation).match(/\d+/)[0];
      baseDate = baseDate.quarter(parseInt(quarterStr));
    }
    if (origin.toLowerCase() === "wo") {
      const segmentation = val.slice(index - 1, index);
      const weekStr = findTargetStr(val, index, segmentation).match(/\d+/)[0];
      baseDate = baseDate.week(parseInt(weekStr));
    }
    if (origin.toLowerCase() === "ww") {
      baseDate = baseDate.week(parseInt(val.slice(index, index + origin.length)));
    }
    if (origin.toLowerCase() === "w") {
      baseDate = baseDate.week(parseInt(val.slice(index, index + origin.length + 1)));
    }
  }
  return baseDate;
};
var generateConfig = {
  // get
  getNow: () => (0, import_dayjs.default)(),
  getFixedDate: (string) => (0, import_dayjs.default)(string, ["YYYY-M-DD", "YYYY-MM-DD"]),
  getEndDate: (date) => date.endOf("month"),
  getWeekDay: (date) => {
    const clone = date.locale("en");
    return clone.weekday() + clone.localeData().firstDayOfWeek();
  },
  getYear: (date) => date.year(),
  getMonth: (date) => date.month(),
  getDate: (date) => date.date(),
  getHour: (date) => date.hour(),
  getMinute: (date) => date.minute(),
  getSecond: (date) => date.second(),
  // set
  addYear: (date, diff) => date.add(diff, "year"),
  addMonth: (date, diff) => date.add(diff, "month"),
  addDate: (date, diff) => date.add(diff, "day"),
  setYear: (date, year) => date.year(year),
  setMonth: (date, month) => date.month(month),
  setDate: (date, num) => date.date(num),
  setHour: (date, hour) => date.hour(hour),
  setMinute: (date, minute) => date.minute(minute),
  setSecond: (date, second) => date.second(second),
  // Compare
  isAfter: (date1, date2) => date1.isAfter(date2),
  isValidate: (date) => date.isValid(),
  locale: {
    getWeekFirstDay: (locale) => (0, import_dayjs.default)().locale(parseLocale(locale)).localeData().firstDayOfWeek(),
    getWeekFirstDate: (locale, date) => date.locale(parseLocale(locale)).weekday(0),
    getWeek: (locale, date) => date.locale(parseLocale(locale)).week(),
    getShortWeekDays: (locale) => (0, import_dayjs.default)().locale(parseLocale(locale)).localeData().weekdaysMin(),
    getShortMonths: (locale) => (0, import_dayjs.default)().locale(parseLocale(locale)).localeData().monthsShort(),
    format: (locale, date, format) => date.locale(parseLocale(locale)).format(format),
    parse: (locale, text, formats) => {
      const localeStr = parseLocale(locale);
      for (let i = 0; i < formats.length; i += 1) {
        const format = formats[i];
        const formatText = text;
        if (format.includes("wo") || format.includes("Wo")) {
          const year = formatText.split("-")[0];
          const weekStr = formatText.split("-")[1];
          const firstWeek = (0, import_dayjs.default)(year, "YYYY").startOf("year").locale(localeStr);
          for (let j = 0; j <= 52; j += 1) {
            const nextWeek = firstWeek.add(j, "week");
            if (nextWeek.format("Wo") === weekStr) {
              return nextWeek;
            }
          }
          parseNoMatchNotice();
          return null;
        }
        const date = (0, import_dayjs.default)(formatText, format, true).locale(localeStr);
        if (date.isValid()) {
          return date;
        }
      }
      if (!text) {
        parseNoMatchNotice();
      }
      return null;
    }
  },
  toDate: (value, valueFormat) => {
    if (Array.isArray(value)) {
      return value.map((val) => toDateWithValueFormat(val, valueFormat));
    } else {
      return toDateWithValueFormat(value, valueFormat);
    }
  },
  toString: (value, valueFormat) => {
    if (Array.isArray(value)) {
      return value.map((val) => import_dayjs.default.isDayjs(val) ? val.format(valueFormat) : val);
    } else {
      return import_dayjs.default.isDayjs(value) ? value.format(valueFormat) : value;
    }
  }
};
var dayjs_default = generateConfig;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/date-picker/PickerButton.js
var PickerButton = (props, _ref) => {
  let {
    attrs,
    slots
  } = _ref;
  return createVNode(button_default, _objectSpread2(_objectSpread2({
    "size": "small",
    "type": "primary"
  }, props), attrs), slots);
};
var PickerButton_default = PickerButton;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/date-picker/PickerTag.js
function PickerTag(props, _ref) {
  let {
    slots,
    attrs
  } = _ref;
  return createVNode(tag_default, _objectSpread2(_objectSpread2({
    "color": "blue"
  }, props), attrs), slots);
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/hooks/useMergeProps.js
function useMergeProps(props) {
  const attrs = useAttrs();
  return _extends(_extends({}, props), attrs);
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/PanelContext.js
var PanelContextKey = Symbol("PanelContextProps");
var useProvidePanel = (props) => {
  provide(PanelContextKey, props);
};
var useInjectPanel = () => {
  return inject(PanelContextKey, {});
};

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/Header.js
var HIDDEN_STYLE = {
  visibility: "hidden"
};
function Header(_props, _ref) {
  let {
    slots
  } = _ref;
  var _a2;
  const props = useMergeProps(_props);
  const {
    prefixCls,
    prevIcon = "‹",
    nextIcon = "›",
    superPrevIcon = "«",
    superNextIcon = "»",
    onSuperPrev,
    onSuperNext,
    onPrev,
    onNext
  } = props;
  const {
    hideNextBtn,
    hidePrevBtn
  } = useInjectPanel();
  return createVNode("div", {
    "class": prefixCls
  }, [onSuperPrev && createVNode("button", {
    "type": "button",
    "onClick": onSuperPrev,
    "tabindex": -1,
    "class": `${prefixCls}-super-prev-btn`,
    "style": hidePrevBtn.value ? HIDDEN_STYLE : {}
  }, [superPrevIcon]), onPrev && createVNode("button", {
    "type": "button",
    "onClick": onPrev,
    "tabindex": -1,
    "class": `${prefixCls}-prev-btn`,
    "style": hidePrevBtn.value ? HIDDEN_STYLE : {}
  }, [prevIcon]), createVNode("div", {
    "class": `${prefixCls}-view`
  }, [(_a2 = slots.default) === null || _a2 === void 0 ? void 0 : _a2.call(slots)]), onNext && createVNode("button", {
    "type": "button",
    "onClick": onNext,
    "tabindex": -1,
    "class": `${prefixCls}-next-btn`,
    "style": hideNextBtn.value ? HIDDEN_STYLE : {}
  }, [nextIcon]), onSuperNext && createVNode("button", {
    "type": "button",
    "onClick": onSuperNext,
    "tabindex": -1,
    "class": `${prefixCls}-super-next-btn`,
    "style": hideNextBtn.value ? HIDDEN_STYLE : {}
  }, [superNextIcon])]);
}
Header.displayName = "Header";
Header.inheritAttrs = false;
var Header_default = Header;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/DecadePanel/DecadeHeader.js
function DecadeHeader(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    generateConfig: generateConfig2,
    viewDate,
    onPrevDecades,
    onNextDecades
  } = props;
  const {
    hideHeader
  } = useInjectPanel();
  if (hideHeader) {
    return null;
  }
  const headerPrefixCls = `${prefixCls}-header`;
  const yearNumber = generateConfig2.getYear(viewDate);
  const startYear = Math.floor(yearNumber / DECADE_DISTANCE_COUNT) * DECADE_DISTANCE_COUNT;
  const endYear = startYear + DECADE_DISTANCE_COUNT - 1;
  return createVNode(Header_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": headerPrefixCls,
    "onSuperPrev": onPrevDecades,
    "onSuperNext": onNextDecades
  }), {
    default: () => [startYear, createTextVNode("-"), endYear]
  });
}
DecadeHeader.displayName = "DecadeHeader";
DecadeHeader.inheritAttrs = false;
var DecadeHeader_default = DecadeHeader;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/utils/timeUtil.js
function setTime(generateConfig2, date, hour, minute, second) {
  let nextTime = generateConfig2.setHour(date, hour);
  nextTime = generateConfig2.setMinute(nextTime, minute);
  nextTime = generateConfig2.setSecond(nextTime, second);
  return nextTime;
}
function setDateTime(generateConfig2, date, defaultDate) {
  if (!defaultDate) {
    return date;
  }
  let newDate = date;
  newDate = generateConfig2.setHour(newDate, generateConfig2.getHour(defaultDate));
  newDate = generateConfig2.setMinute(newDate, generateConfig2.getMinute(defaultDate));
  newDate = generateConfig2.setSecond(newDate, generateConfig2.getSecond(defaultDate));
  return newDate;
}
function getLowerBoundTime(hour, minute, second, hourStep, minuteStep, secondStep) {
  const lowerBoundHour = Math.floor(hour / hourStep) * hourStep;
  if (lowerBoundHour < hour) {
    return [lowerBoundHour, 60 - minuteStep, 60 - secondStep];
  }
  const lowerBoundMinute = Math.floor(minute / minuteStep) * minuteStep;
  if (lowerBoundMinute < minute) {
    return [lowerBoundHour, lowerBoundMinute, 60 - secondStep];
  }
  const lowerBoundSecond = Math.floor(second / secondStep) * secondStep;
  return [lowerBoundHour, lowerBoundMinute, lowerBoundSecond];
}
function getLastDay(generateConfig2, date) {
  const year = generateConfig2.getYear(date);
  const month = generateConfig2.getMonth(date) + 1;
  const endDate = generateConfig2.getEndDate(generateConfig2.getFixedDate(`${year}-${month}-01`));
  const lastDay = generateConfig2.getDate(endDate);
  const monthShow = month < 10 ? `0${month}` : `${month}`;
  return `${year}-${monthShow}-${lastDay}`;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/PanelBody.js
function PanelBody(_props) {
  const {
    prefixCls,
    disabledDate,
    onSelect,
    picker,
    rowNum,
    colNum,
    prefixColumn,
    rowClassName,
    baseDate,
    getCellClassName,
    getCellText,
    getCellNode,
    getCellDate,
    generateConfig: generateConfig2,
    titleCell,
    headerCells
  } = useMergeProps(_props);
  const {
    onDateMouseenter,
    onDateMouseleave,
    mode
  } = useInjectPanel();
  const cellPrefixCls = `${prefixCls}-cell`;
  const rows = [];
  for (let i = 0; i < rowNum; i += 1) {
    const row = [];
    let rowStartDate;
    for (let j = 0; j < colNum; j += 1) {
      const offset = i * colNum + j;
      const currentDate = getCellDate(baseDate, offset);
      const disabled = getCellDateDisabled({
        cellDate: currentDate,
        mode: mode.value,
        disabledDate,
        generateConfig: generateConfig2
      });
      if (j === 0) {
        rowStartDate = currentDate;
        if (prefixColumn) {
          row.push(prefixColumn(rowStartDate));
        }
      }
      const title = titleCell && titleCell(currentDate);
      row.push(createVNode("td", {
        "key": j,
        "title": title,
        "class": classNames_default(cellPrefixCls, _extends({
          [`${cellPrefixCls}-disabled`]: disabled,
          [`${cellPrefixCls}-start`]: getCellText(currentDate) === 1 || picker === "year" && Number(title) % 10 === 0,
          [`${cellPrefixCls}-end`]: title === getLastDay(generateConfig2, currentDate) || picker === "year" && Number(title) % 10 === 9
        }, getCellClassName(currentDate))),
        "onClick": (e) => {
          e.stopPropagation();
          if (!disabled) {
            onSelect(currentDate);
          }
        },
        "onMouseenter": () => {
          if (!disabled && onDateMouseenter) {
            onDateMouseenter(currentDate);
          }
        },
        "onMouseleave": () => {
          if (!disabled && onDateMouseleave) {
            onDateMouseleave(currentDate);
          }
        }
      }, [getCellNode ? getCellNode(currentDate) : createVNode("div", {
        "class": `${cellPrefixCls}-inner`
      }, [getCellText(currentDate)])]));
    }
    rows.push(createVNode("tr", {
      "key": i,
      "class": rowClassName && rowClassName(rowStartDate)
    }, [row]));
  }
  return createVNode("div", {
    "class": `${prefixCls}-body`
  }, [createVNode("table", {
    "class": `${prefixCls}-content`
  }, [headerCells && createVNode("thead", null, [createVNode("tr", null, [headerCells])]), createVNode("tbody", null, [rows])])]);
}
PanelBody.displayName = "PanelBody";
PanelBody.inheritAttrs = false;
var PanelBody_default = PanelBody;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/DecadePanel/DecadeBody.js
var DECADE_COL_COUNT = 3;
var DECADE_ROW_COUNT = 4;
function DecadeBody(_props) {
  const props = useMergeProps(_props);
  const DECADE_UNIT_DIFF_DES = DECADE_UNIT_DIFF - 1;
  const {
    prefixCls,
    viewDate,
    generateConfig: generateConfig2
  } = props;
  const cellPrefixCls = `${prefixCls}-cell`;
  const yearNumber = generateConfig2.getYear(viewDate);
  const decadeYearNumber = Math.floor(yearNumber / DECADE_UNIT_DIFF) * DECADE_UNIT_DIFF;
  const startDecadeYear = Math.floor(yearNumber / DECADE_DISTANCE_COUNT) * DECADE_DISTANCE_COUNT;
  const endDecadeYear = startDecadeYear + DECADE_DISTANCE_COUNT - 1;
  const baseDecadeYear = generateConfig2.setYear(viewDate, startDecadeYear - Math.ceil((DECADE_COL_COUNT * DECADE_ROW_COUNT * DECADE_UNIT_DIFF - DECADE_DISTANCE_COUNT) / 2));
  const getCellClassName = (date) => {
    const startDecadeNumber = generateConfig2.getYear(date);
    const endDecadeNumber = startDecadeNumber + DECADE_UNIT_DIFF_DES;
    return {
      [`${cellPrefixCls}-in-view`]: startDecadeYear <= startDecadeNumber && endDecadeNumber <= endDecadeYear,
      [`${cellPrefixCls}-selected`]: startDecadeNumber === decadeYearNumber
    };
  };
  return createVNode(PanelBody_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "rowNum": DECADE_ROW_COUNT,
    "colNum": DECADE_COL_COUNT,
    "baseDate": baseDecadeYear,
    "getCellText": (date) => {
      const startDecadeNumber = generateConfig2.getYear(date);
      return `${startDecadeNumber}-${startDecadeNumber + DECADE_UNIT_DIFF_DES}`;
    },
    "getCellClassName": getCellClassName,
    "getCellDate": (date, offset) => generateConfig2.addYear(date, offset * DECADE_UNIT_DIFF)
  }), null);
}
DecadeBody.displayName = "DecadeBody";
DecadeBody.inheritAttrs = false;
var DecadeBody_default = DecadeBody;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/utils/uiUtil.js
var scrollIds = /* @__PURE__ */ new Map();
function waitElementReady(element, callback) {
  let id;
  function tryOrNextFrame() {
    if (isVisible_default(element)) {
      callback();
    } else {
      id = wrapperRaf(() => {
        tryOrNextFrame();
      });
    }
  }
  tryOrNextFrame();
  return () => {
    wrapperRaf.cancel(id);
  };
}
function scrollTo(element, to, duration) {
  if (scrollIds.get(element)) {
    wrapperRaf.cancel(scrollIds.get(element));
  }
  if (duration <= 0) {
    scrollIds.set(element, wrapperRaf(() => {
      element.scrollTop = to;
    }));
    return;
  }
  const difference = to - element.scrollTop;
  const perTick = difference / duration * 10;
  scrollIds.set(element, wrapperRaf(() => {
    element.scrollTop += perTick;
    if (element.scrollTop !== to) {
      scrollTo(element, to, duration - 10);
    }
  }));
}
function createKeydownHandler(event, _ref) {
  let {
    onLeftRight,
    onCtrlLeftRight,
    onUpDown,
    onPageUpDown,
    onEnter
  } = _ref;
  const {
    which,
    ctrlKey,
    metaKey
  } = event;
  switch (which) {
    case KeyCode_default.LEFT:
      if (ctrlKey || metaKey) {
        if (onCtrlLeftRight) {
          onCtrlLeftRight(-1);
          return true;
        }
      } else if (onLeftRight) {
        onLeftRight(-1);
        return true;
      }
      break;
    case KeyCode_default.RIGHT:
      if (ctrlKey || metaKey) {
        if (onCtrlLeftRight) {
          onCtrlLeftRight(1);
          return true;
        }
      } else if (onLeftRight) {
        onLeftRight(1);
        return true;
      }
      break;
    case KeyCode_default.UP:
      if (onUpDown) {
        onUpDown(-1);
        return true;
      }
      break;
    case KeyCode_default.DOWN:
      if (onUpDown) {
        onUpDown(1);
        return true;
      }
      break;
    case KeyCode_default.PAGE_UP:
      if (onPageUpDown) {
        onPageUpDown(-1);
        return true;
      }
      break;
    case KeyCode_default.PAGE_DOWN:
      if (onPageUpDown) {
        onPageUpDown(1);
        return true;
      }
      break;
    case KeyCode_default.ENTER:
      if (onEnter) {
        onEnter();
        return true;
      }
      break;
  }
  return false;
}
function getDefaultFormat(format, picker, showTime, use12Hours) {
  let mergedFormat = format;
  if (!mergedFormat) {
    switch (picker) {
      case "time":
        mergedFormat = use12Hours ? "hh:mm:ss a" : "HH:mm:ss";
        break;
      case "week":
        mergedFormat = "gggg-wo";
        break;
      case "month":
        mergedFormat = "YYYY-MM";
        break;
      case "quarter":
        mergedFormat = "YYYY-[Q]Q";
        break;
      case "year":
        mergedFormat = "YYYY";
        break;
      default:
        mergedFormat = showTime ? "YYYY-MM-DD HH:mm:ss" : "YYYY-MM-DD";
    }
  }
  return mergedFormat;
}
function getInputSize(picker, format, generateConfig2) {
  const defaultSize = picker === "time" ? 8 : 10;
  const length = typeof format === "function" ? format(generateConfig2.getNow()).length : format.length;
  return Math.max(defaultSize, length) + 2;
}
var globalClickFunc = null;
var clickCallbacks = /* @__PURE__ */ new Set();
function addGlobalMousedownEvent(callback) {
  if (!globalClickFunc && typeof window !== "undefined" && window.addEventListener) {
    globalClickFunc = (e) => {
      [...clickCallbacks].forEach((queueFunc) => {
        queueFunc(e);
      });
    };
    window.addEventListener("mousedown", globalClickFunc);
  }
  clickCallbacks.add(callback);
  return () => {
    clickCallbacks.delete(callback);
    if (clickCallbacks.size === 0) {
      window.removeEventListener("mousedown", globalClickFunc);
      globalClickFunc = null;
    }
  };
}
function getTargetFromEvent(e) {
  var _a2;
  const target = e.target;
  if (e.composed && target.shadowRoot) {
    return ((_a2 = e.composedPath) === null || _a2 === void 0 ? void 0 : _a2.call(e)[0]) || target;
  }
  return target;
}
var getYearNextMode = (next) => {
  if (next === "month" || next === "date") {
    return "year";
  }
  return next;
};
var getMonthNextMode = (next) => {
  if (next === "date") {
    return "month";
  }
  return next;
};
var getQuarterNextMode = (next) => {
  if (next === "month" || next === "date") {
    return "quarter";
  }
  return next;
};
var getWeekNextMode = (next) => {
  if (next === "date") {
    return "week";
  }
  return next;
};
var PickerModeMap = {
  year: getYearNextMode,
  month: getMonthNextMode,
  quarter: getQuarterNextMode,
  week: getWeekNextMode,
  time: null,
  date: null
};
function elementsContains(elements, target) {
  if (false) {
    return false;
  }
  return elements.some((ele) => ele && ele.contains(target));
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/DecadePanel/index.js
var DECADE_UNIT_DIFF = 10;
var DECADE_DISTANCE_COUNT = DECADE_UNIT_DIFF * 10;
function DecadePanel(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    onViewDateChange,
    generateConfig: generateConfig2,
    viewDate,
    operationRef,
    onSelect,
    onPanelChange
  } = props;
  const panelPrefixCls = `${prefixCls}-decade-panel`;
  operationRef.value = {
    onKeydown: (event) => createKeydownHandler(event, {
      onLeftRight: (diff) => {
        onSelect(generateConfig2.addYear(viewDate, diff * DECADE_UNIT_DIFF), "key");
      },
      onCtrlLeftRight: (diff) => {
        onSelect(generateConfig2.addYear(viewDate, diff * DECADE_DISTANCE_COUNT), "key");
      },
      onUpDown: (diff) => {
        onSelect(generateConfig2.addYear(viewDate, diff * DECADE_UNIT_DIFF * DECADE_COL_COUNT), "key");
      },
      onEnter: () => {
        onPanelChange("year", viewDate);
      }
    })
  };
  const onDecadesChange = (diff) => {
    const newDate = generateConfig2.addYear(viewDate, diff * DECADE_DISTANCE_COUNT);
    onViewDateChange(newDate);
    onPanelChange(null, newDate);
  };
  const onInternalSelect = (date) => {
    onSelect(date, "mouse");
    onPanelChange("year", date);
  };
  return createVNode("div", {
    "class": panelPrefixCls
  }, [createVNode(DecadeHeader_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": prefixCls,
    "onPrevDecades": () => {
      onDecadesChange(-1);
    },
    "onNextDecades": () => {
      onDecadesChange(1);
    }
  }), null), createVNode(DecadeBody_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": prefixCls,
    "onSelect": onInternalSelect
  }), null)]);
}
DecadePanel.displayName = "DecadePanel";
DecadePanel.inheritAttrs = false;
var DecadePanel_default = DecadePanel;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/utils/dateUtil.js
var WEEK_DAY_COUNT = 7;
function isNullEqual(value1, value2) {
  if (!value1 && !value2) {
    return true;
  }
  if (!value1 || !value2) {
    return false;
  }
  return void 0;
}
function isSameDecade(generateConfig2, decade1, decade2) {
  const equal = isNullEqual(decade1, decade2);
  if (typeof equal === "boolean") {
    return equal;
  }
  const num1 = Math.floor(generateConfig2.getYear(decade1) / 10);
  const num2 = Math.floor(generateConfig2.getYear(decade2) / 10);
  return num1 === num2;
}
function isSameYear(generateConfig2, year1, year2) {
  const equal = isNullEqual(year1, year2);
  if (typeof equal === "boolean") {
    return equal;
  }
  return generateConfig2.getYear(year1) === generateConfig2.getYear(year2);
}
function getQuarter(generateConfig2, date) {
  const quota = Math.floor(generateConfig2.getMonth(date) / 3);
  return quota + 1;
}
function isSameQuarter(generateConfig2, quarter1, quarter2) {
  const equal = isNullEqual(quarter1, quarter2);
  if (typeof equal === "boolean") {
    return equal;
  }
  return isSameYear(generateConfig2, quarter1, quarter2) && getQuarter(generateConfig2, quarter1) === getQuarter(generateConfig2, quarter2);
}
function isSameMonth(generateConfig2, month1, month2) {
  const equal = isNullEqual(month1, month2);
  if (typeof equal === "boolean") {
    return equal;
  }
  return isSameYear(generateConfig2, month1, month2) && generateConfig2.getMonth(month1) === generateConfig2.getMonth(month2);
}
function isSameDate(generateConfig2, date1, date2) {
  const equal = isNullEqual(date1, date2);
  if (typeof equal === "boolean") {
    return equal;
  }
  return generateConfig2.getYear(date1) === generateConfig2.getYear(date2) && generateConfig2.getMonth(date1) === generateConfig2.getMonth(date2) && generateConfig2.getDate(date1) === generateConfig2.getDate(date2);
}
function isSameTime(generateConfig2, time1, time2) {
  const equal = isNullEqual(time1, time2);
  if (typeof equal === "boolean") {
    return equal;
  }
  return generateConfig2.getHour(time1) === generateConfig2.getHour(time2) && generateConfig2.getMinute(time1) === generateConfig2.getMinute(time2) && generateConfig2.getSecond(time1) === generateConfig2.getSecond(time2);
}
function isSameWeek(generateConfig2, locale, date1, date2) {
  const equal = isNullEqual(date1, date2);
  if (typeof equal === "boolean") {
    return equal;
  }
  return generateConfig2.locale.getWeek(locale, date1) === generateConfig2.locale.getWeek(locale, date2);
}
function isEqual(generateConfig2, value1, value2) {
  return isSameDate(generateConfig2, value1, value2) && isSameTime(generateConfig2, value1, value2);
}
function isInRange(generateConfig2, startDate, endDate, current) {
  if (!startDate || !endDate || !current) {
    return false;
  }
  return !isSameDate(generateConfig2, startDate, current) && !isSameDate(generateConfig2, endDate, current) && generateConfig2.isAfter(current, startDate) && generateConfig2.isAfter(endDate, current);
}
function getWeekStartDate(locale, generateConfig2, value) {
  const weekFirstDay = generateConfig2.locale.getWeekFirstDay(locale);
  const monthStartDate = generateConfig2.setDate(value, 1);
  const startDateWeekDay = generateConfig2.getWeekDay(monthStartDate);
  let alignStartDate = generateConfig2.addDate(monthStartDate, weekFirstDay - startDateWeekDay);
  if (generateConfig2.getMonth(alignStartDate) === generateConfig2.getMonth(value) && generateConfig2.getDate(alignStartDate) > 1) {
    alignStartDate = generateConfig2.addDate(alignStartDate, -7);
  }
  return alignStartDate;
}
function getClosingViewDate(viewDate, picker, generateConfig2) {
  let offset = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1;
  switch (picker) {
    case "year":
      return generateConfig2.addYear(viewDate, offset * 10);
    case "quarter":
    case "month":
      return generateConfig2.addYear(viewDate, offset);
    default:
      return generateConfig2.addMonth(viewDate, offset);
  }
}
function formatValue(value, _ref) {
  let {
    generateConfig: generateConfig2,
    locale,
    format
  } = _ref;
  return typeof format === "function" ? format(value) : generateConfig2.locale.format(locale.locale, value, format);
}
function parseValue(value, _ref2) {
  let {
    generateConfig: generateConfig2,
    locale,
    formatList
  } = _ref2;
  if (!value || typeof formatList[0] === "function") {
    return null;
  }
  return generateConfig2.locale.parse(locale.locale, value, formatList);
}
function getCellDateDisabled(_ref3) {
  let {
    cellDate,
    mode,
    disabledDate,
    generateConfig: generateConfig2
  } = _ref3;
  if (!disabledDate) return false;
  const getDisabledFromRange = (currentMode, start, end) => {
    let current = start;
    while (current <= end) {
      let date;
      switch (currentMode) {
        case "date": {
          date = generateConfig2.setDate(cellDate, current);
          if (!disabledDate(date)) {
            return false;
          }
          break;
        }
        case "month": {
          date = generateConfig2.setMonth(cellDate, current);
          if (!getCellDateDisabled({
            cellDate: date,
            mode: "month",
            generateConfig: generateConfig2,
            disabledDate
          })) {
            return false;
          }
          break;
        }
        case "year": {
          date = generateConfig2.setYear(cellDate, current);
          if (!getCellDateDisabled({
            cellDate: date,
            mode: "year",
            generateConfig: generateConfig2,
            disabledDate
          })) {
            return false;
          }
          break;
        }
      }
      current += 1;
    }
    return true;
  };
  switch (mode) {
    case "date":
    case "week": {
      return disabledDate(cellDate);
    }
    case "month": {
      const startDate = 1;
      const endDate = generateConfig2.getDate(generateConfig2.getEndDate(cellDate));
      return getDisabledFromRange("date", startDate, endDate);
    }
    case "quarter": {
      const startMonth = Math.floor(generateConfig2.getMonth(cellDate) / 3) * 3;
      const endMonth = startMonth + 2;
      return getDisabledFromRange("month", startMonth, endMonth);
    }
    case "year": {
      return getDisabledFromRange("month", 0, 11);
    }
    case "decade": {
      const year = generateConfig2.getYear(cellDate);
      const startYear = Math.floor(year / DECADE_UNIT_DIFF) * DECADE_UNIT_DIFF;
      const endYear = startYear + DECADE_UNIT_DIFF - 1;
      return getDisabledFromRange("year", startYear, endYear);
    }
  }
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/TimePanel/TimeHeader.js
function TimeHeader(_props) {
  const props = useMergeProps(_props);
  const {
    hideHeader
  } = useInjectPanel();
  if (hideHeader.value) {
    return null;
  }
  const {
    prefixCls,
    generateConfig: generateConfig2,
    locale,
    value,
    format
  } = props;
  const headerPrefixCls = `${prefixCls}-header`;
  return createVNode(Header_default, {
    "prefixCls": headerPrefixCls
  }, {
    default: () => [value ? formatValue(value, {
      locale,
      format,
      generateConfig: generateConfig2
    }) : " "]
  });
}
TimeHeader.displayName = "TimeHeader";
TimeHeader.inheritAttrs = false;
var TimeHeader_default = TimeHeader;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/TimePanel/TimeUnitColumn.js
var TimeUnitColumn_default = defineComponent({
  name: "TimeUnitColumn",
  props: ["prefixCls", "units", "onSelect", "value", "active", "hideDisabledOptions"],
  setup(props) {
    const {
      open
    } = useInjectPanel();
    const ulRef = shallowRef(null);
    const liRefs = ref(/* @__PURE__ */ new Map());
    const scrollRef = ref();
    watch(() => props.value, () => {
      const li = liRefs.value.get(props.value);
      if (li && open.value !== false) {
        scrollTo(ulRef.value, li.offsetTop, 120);
      }
    });
    onBeforeUnmount(() => {
      var _a2;
      (_a2 = scrollRef.value) === null || _a2 === void 0 ? void 0 : _a2.call(scrollRef);
    });
    watch(open, () => {
      var _a2;
      (_a2 = scrollRef.value) === null || _a2 === void 0 ? void 0 : _a2.call(scrollRef);
      nextTick(() => {
        if (open.value) {
          const li = liRefs.value.get(props.value);
          if (li) {
            scrollRef.value = waitElementReady(li, () => {
              scrollTo(ulRef.value, li.offsetTop, 0);
            });
          }
        }
      });
    }, {
      immediate: true,
      flush: "post"
    });
    return () => {
      const {
        prefixCls,
        units,
        onSelect,
        value,
        active,
        hideDisabledOptions
      } = props;
      const cellPrefixCls = `${prefixCls}-cell`;
      return createVNode("ul", {
        "class": classNames_default(`${prefixCls}-column`, {
          [`${prefixCls}-column-active`]: active
        }),
        "ref": ulRef,
        "style": {
          position: "relative"
        }
      }, [units.map((unit) => {
        if (hideDisabledOptions && unit.disabled) {
          return null;
        }
        return createVNode("li", {
          "key": unit.value,
          "ref": (element) => {
            liRefs.value.set(unit.value, element);
          },
          "class": classNames_default(cellPrefixCls, {
            [`${cellPrefixCls}-disabled`]: unit.disabled,
            [`${cellPrefixCls}-selected`]: value === unit.value
          }),
          "onClick": () => {
            if (unit.disabled) {
              return;
            }
            onSelect(unit.value);
          }
        }, [createVNode("div", {
          "class": `${cellPrefixCls}-inner`
        }, [unit.label])]);
      })]);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/utils/miscUtil.js
function leftPad(str, length) {
  let fill = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : "0";
  let current = String(str);
  while (current.length < length) {
    current = `${fill}${str}`;
  }
  return current;
}
var tuple = function() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  return args;
};
function toArray(val) {
  if (val === null || val === void 0) {
    return [];
  }
  return Array.isArray(val) ? val : [val];
}
function getDataOrAriaProps(props) {
  const retProps = {};
  Object.keys(props).forEach((key) => {
    if ((key.startsWith("data-") || key.startsWith("aria-") || key === "role" || key === "name") && !key.startsWith("data-__")) {
      retProps[key] = props[key];
    }
  });
  return retProps;
}
function getValue(values, index) {
  return values ? values[index] : null;
}
function updateValues(values, value, index) {
  const newValues = [getValue(values, 0), getValue(values, 1)];
  newValues[index] = typeof value === "function" ? value(newValues[index]) : value;
  if (!newValues[0] && !newValues[1]) {
    return null;
  }
  return newValues;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/TimePanel/TimeBody.js
function generateUnits(start, end, step, disabledUnits) {
  const units = [];
  for (let i = start; i <= end; i += step) {
    units.push({
      label: leftPad(i, 2),
      value: i,
      disabled: (disabledUnits || []).includes(i)
    });
  }
  return units;
}
var TimeBody = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "TimeBody",
  inheritAttrs: false,
  props: ["generateConfig", "prefixCls", "operationRef", "activeColumnIndex", "value", "showHour", "showMinute", "showSecond", "use12Hours", "hourStep", "minuteStep", "secondStep", "disabledHours", "disabledMinutes", "disabledSeconds", "disabledTime", "hideDisabledOptions", "onSelect"],
  setup(props) {
    const originHour = computed(() => props.value ? props.generateConfig.getHour(props.value) : -1);
    const isPM = computed(() => {
      if (props.use12Hours) {
        return originHour.value >= 12;
      } else {
        return false;
      }
    });
    const hour = computed(() => {
      if (props.use12Hours) {
        return originHour.value % 12;
      } else {
        return originHour.value;
      }
    });
    const minute = computed(() => props.value ? props.generateConfig.getMinute(props.value) : -1);
    const second = computed(() => props.value ? props.generateConfig.getSecond(props.value) : -1);
    const now = ref(props.generateConfig.getNow());
    const mergedDisabledHours = ref();
    const mergedDisabledMinutes = ref();
    const mergedDisabledSeconds = ref();
    onBeforeUpdate(() => {
      now.value = props.generateConfig.getNow();
    });
    watchEffect(() => {
      if (props.disabledTime) {
        const disabledConfig = props.disabledTime(now);
        [mergedDisabledHours.value, mergedDisabledMinutes.value, mergedDisabledSeconds.value] = [disabledConfig.disabledHours, disabledConfig.disabledMinutes, disabledConfig.disabledSeconds];
      } else {
        [mergedDisabledHours.value, mergedDisabledMinutes.value, mergedDisabledSeconds.value] = [props.disabledHours, props.disabledMinutes, props.disabledSeconds];
      }
    });
    const setTime2 = (isNewPM, newHour, newMinute, newSecond) => {
      let newDate = props.value || props.generateConfig.getNow();
      const mergedHour = Math.max(0, newHour);
      const mergedMinute = Math.max(0, newMinute);
      const mergedSecond = Math.max(0, newSecond);
      newDate = setTime(props.generateConfig, newDate, !props.use12Hours || !isNewPM ? mergedHour : mergedHour + 12, mergedMinute, mergedSecond);
      return newDate;
    };
    const rawHours = computed(() => {
      var _a2;
      return generateUnits(0, 23, (_a2 = props.hourStep) !== null && _a2 !== void 0 ? _a2 : 1, mergedDisabledHours.value && mergedDisabledHours.value());
    });
    const AMPMDisabled = computed(() => {
      if (!props.use12Hours) {
        return [false, false];
      }
      const AMPMDisabled2 = [true, true];
      rawHours.value.forEach((_ref) => {
        let {
          disabled,
          value: hourValue
        } = _ref;
        if (disabled) return;
        if (hourValue >= 12) {
          AMPMDisabled2[1] = false;
        } else {
          AMPMDisabled2[0] = false;
        }
      });
      return AMPMDisabled2;
    });
    const hours = computed(() => {
      if (!props.use12Hours) return rawHours.value;
      return rawHours.value.filter(isPM.value ? (hourMeta) => hourMeta.value >= 12 : (hourMeta) => hourMeta.value < 12).map((hourMeta) => {
        const hourValue = hourMeta.value % 12;
        const hourLabel = hourValue === 0 ? "12" : leftPad(hourValue, 2);
        return _extends(_extends({}, hourMeta), {
          label: hourLabel,
          value: hourValue
        });
      });
    });
    const minutes = computed(() => {
      var _a2;
      return generateUnits(0, 59, (_a2 = props.minuteStep) !== null && _a2 !== void 0 ? _a2 : 1, mergedDisabledMinutes.value && mergedDisabledMinutes.value(originHour.value));
    });
    const seconds = computed(() => {
      var _a2;
      return generateUnits(0, 59, (_a2 = props.secondStep) !== null && _a2 !== void 0 ? _a2 : 1, mergedDisabledSeconds.value && mergedDisabledSeconds.value(originHour.value, minute.value));
    });
    return () => {
      const {
        prefixCls,
        operationRef,
        activeColumnIndex,
        showHour,
        showMinute,
        showSecond,
        use12Hours,
        hideDisabledOptions,
        onSelect
      } = props;
      const columns = [];
      const contentPrefixCls = `${prefixCls}-content`;
      const columnPrefixCls = `${prefixCls}-time-panel`;
      operationRef.value = {
        onUpDown: (diff) => {
          const column = columns[activeColumnIndex];
          if (column) {
            const valueIndex = column.units.findIndex((unit) => unit.value === column.value);
            const unitLen = column.units.length;
            for (let i = 1; i < unitLen; i += 1) {
              const nextUnit = column.units[(valueIndex + diff * i + unitLen) % unitLen];
              if (nextUnit.disabled !== true) {
                column.onSelect(nextUnit.value);
                break;
              }
            }
          }
        }
      };
      function addColumnNode(condition, node, columnValue, units, onColumnSelect) {
        if (condition !== false) {
          columns.push({
            node: cloneElement(node, {
              prefixCls: columnPrefixCls,
              value: columnValue,
              active: activeColumnIndex === columns.length,
              onSelect: onColumnSelect,
              units,
              hideDisabledOptions
            }),
            onSelect: onColumnSelect,
            value: columnValue,
            units
          });
        }
      }
      addColumnNode(showHour, createVNode(TimeUnitColumn_default, {
        "key": "hour"
      }, null), hour.value, hours.value, (num) => {
        onSelect(setTime2(isPM.value, num, minute.value, second.value), "mouse");
      });
      addColumnNode(showMinute, createVNode(TimeUnitColumn_default, {
        "key": "minute"
      }, null), minute.value, minutes.value, (num) => {
        onSelect(setTime2(isPM.value, hour.value, num, second.value), "mouse");
      });
      addColumnNode(showSecond, createVNode(TimeUnitColumn_default, {
        "key": "second"
      }, null), second.value, seconds.value, (num) => {
        onSelect(setTime2(isPM.value, hour.value, minute.value, num), "mouse");
      });
      let PMIndex = -1;
      if (typeof isPM.value === "boolean") {
        PMIndex = isPM.value ? 1 : 0;
      }
      addColumnNode(use12Hours === true, createVNode(TimeUnitColumn_default, {
        "key": "12hours"
      }, null), PMIndex, [{
        label: "AM",
        value: 0,
        disabled: AMPMDisabled.value[0]
      }, {
        label: "PM",
        value: 1,
        disabled: AMPMDisabled.value[1]
      }], (num) => {
        onSelect(setTime2(!!num, hour.value, minute.value, second.value), "mouse");
      });
      return createVNode("div", {
        "class": contentPrefixCls
      }, [columns.map((_ref2) => {
        let {
          node
        } = _ref2;
        return node;
      })]);
    };
  }
});
var TimeBody_default = TimeBody;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/TimePanel/index.js
var countBoolean = (boolList) => boolList.filter((bool) => bool !== false).length;
function TimePanel(_props) {
  const props = useMergeProps(_props);
  const {
    generateConfig: generateConfig2,
    format = "HH:mm:ss",
    prefixCls,
    active,
    operationRef,
    showHour,
    showMinute,
    showSecond,
    use12Hours = false,
    onSelect,
    value
  } = props;
  const panelPrefixCls = `${prefixCls}-time-panel`;
  const bodyOperationRef = ref();
  const activeColumnIndex = ref(-1);
  const columnsCount = countBoolean([showHour, showMinute, showSecond, use12Hours]);
  operationRef.value = {
    onKeydown: (event) => createKeydownHandler(event, {
      onLeftRight: (diff) => {
        activeColumnIndex.value = (activeColumnIndex.value + diff + columnsCount) % columnsCount;
      },
      onUpDown: (diff) => {
        if (activeColumnIndex.value === -1) {
          activeColumnIndex.value = 0;
        } else if (bodyOperationRef.value) {
          bodyOperationRef.value.onUpDown(diff);
        }
      },
      onEnter: () => {
        onSelect(value || generateConfig2.getNow(), "key");
        activeColumnIndex.value = -1;
      }
    }),
    onBlur: () => {
      activeColumnIndex.value = -1;
    }
  };
  return createVNode("div", {
    "class": classNames_default(panelPrefixCls, {
      [`${panelPrefixCls}-active`]: active
    })
  }, [createVNode(TimeHeader_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "format": format,
    "prefixCls": prefixCls
  }), null), createVNode(TimeBody_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": prefixCls,
    "activeColumnIndex": activeColumnIndex.value,
    "operationRef": bodyOperationRef
  }), null)]);
}
TimePanel.displayName = "TimePanel";
TimePanel.inheritAttrs = false;
var TimePanel_default = TimePanel;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/hooks/useCellClassName.js
function useCellClassName(_ref) {
  let {
    cellPrefixCls,
    generateConfig: generateConfig2,
    rangedValue,
    hoverRangedValue,
    isInView,
    isSameCell,
    offsetCell,
    today,
    value
  } = _ref;
  function getClassName(currentDate) {
    const prevDate = offsetCell(currentDate, -1);
    const nextDate = offsetCell(currentDate, 1);
    const rangeStart = getValue(rangedValue, 0);
    const rangeEnd = getValue(rangedValue, 1);
    const hoverStart = getValue(hoverRangedValue, 0);
    const hoverEnd = getValue(hoverRangedValue, 1);
    const isRangeHovered = isInRange(generateConfig2, hoverStart, hoverEnd, currentDate);
    function isRangeStart(date) {
      return isSameCell(rangeStart, date);
    }
    function isRangeEnd(date) {
      return isSameCell(rangeEnd, date);
    }
    const isHoverStart = isSameCell(hoverStart, currentDate);
    const isHoverEnd = isSameCell(hoverEnd, currentDate);
    const isHoverEdgeStart = (isRangeHovered || isHoverEnd) && (!isInView(prevDate) || isRangeEnd(prevDate));
    const isHoverEdgeEnd = (isRangeHovered || isHoverStart) && (!isInView(nextDate) || isRangeStart(nextDate));
    return {
      // In view
      [`${cellPrefixCls}-in-view`]: isInView(currentDate),
      // Range
      [`${cellPrefixCls}-in-range`]: isInRange(generateConfig2, rangeStart, rangeEnd, currentDate),
      [`${cellPrefixCls}-range-start`]: isRangeStart(currentDate),
      [`${cellPrefixCls}-range-end`]: isRangeEnd(currentDate),
      [`${cellPrefixCls}-range-start-single`]: isRangeStart(currentDate) && !rangeEnd,
      [`${cellPrefixCls}-range-end-single`]: isRangeEnd(currentDate) && !rangeStart,
      [`${cellPrefixCls}-range-start-near-hover`]: isRangeStart(currentDate) && (isSameCell(prevDate, hoverStart) || isInRange(generateConfig2, hoverStart, hoverEnd, prevDate)),
      [`${cellPrefixCls}-range-end-near-hover`]: isRangeEnd(currentDate) && (isSameCell(nextDate, hoverEnd) || isInRange(generateConfig2, hoverStart, hoverEnd, nextDate)),
      // Range Hover
      [`${cellPrefixCls}-range-hover`]: isRangeHovered,
      [`${cellPrefixCls}-range-hover-start`]: isHoverStart,
      [`${cellPrefixCls}-range-hover-end`]: isHoverEnd,
      // Range Edge
      [`${cellPrefixCls}-range-hover-edge-start`]: isHoverEdgeStart,
      [`${cellPrefixCls}-range-hover-edge-end`]: isHoverEdgeEnd,
      [`${cellPrefixCls}-range-hover-edge-start-near-range`]: isHoverEdgeStart && isSameCell(prevDate, rangeEnd),
      [`${cellPrefixCls}-range-hover-edge-end-near-range`]: isHoverEdgeEnd && isSameCell(nextDate, rangeStart),
      // Others
      [`${cellPrefixCls}-today`]: isSameCell(today, currentDate),
      [`${cellPrefixCls}-selected`]: isSameCell(value, currentDate)
    };
  }
  return getClassName;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/RangeContext.js
var RangeContextKey = Symbol("RangeContextProps");
var useProvideRange = (props) => {
  provide(RangeContextKey, props);
};
var useInjectRange = () => {
  return inject(RangeContextKey, {
    rangedValue: ref(),
    hoverRangedValue: ref(),
    inRange: ref(),
    panelPosition: ref()
  });
};
var RangeContextProvider = defineComponent({
  compatConfig: {
    MODE: 3
  },
  name: "PanelContextProvider",
  inheritAttrs: false,
  props: {
    value: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props, _ref) {
    let {
      slots
    } = _ref;
    const value = {
      rangedValue: ref(props.value.rangedValue),
      hoverRangedValue: ref(props.value.hoverRangedValue),
      inRange: ref(props.value.inRange),
      panelPosition: ref(props.value.panelPosition)
    };
    useProvideRange(value);
    toRef;
    watch(() => props.value, () => {
      Object.keys(props.value).forEach((key) => {
        if (value[key]) {
          value[key].value = props.value[key];
        }
      });
    });
    return () => {
      var _a2;
      return (_a2 = slots.default) === null || _a2 === void 0 ? void 0 : _a2.call(slots);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/DatePanel/DateBody.js
function DateBody(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    generateConfig: generateConfig2,
    prefixColumn,
    locale,
    rowCount,
    viewDate,
    value,
    dateRender
  } = props;
  const {
    rangedValue,
    hoverRangedValue
  } = useInjectRange();
  const baseDate = getWeekStartDate(locale.locale, generateConfig2, viewDate);
  const cellPrefixCls = `${prefixCls}-cell`;
  const weekFirstDay = generateConfig2.locale.getWeekFirstDay(locale.locale);
  const today = generateConfig2.getNow();
  const headerCells = [];
  const weekDaysLocale = locale.shortWeekDays || (generateConfig2.locale.getShortWeekDays ? generateConfig2.locale.getShortWeekDays(locale.locale) : []);
  if (prefixColumn) {
    headerCells.push(createVNode("th", {
      "key": "empty",
      "aria-label": "empty cell"
    }, null));
  }
  for (let i = 0; i < WEEK_DAY_COUNT; i += 1) {
    headerCells.push(createVNode("th", {
      "key": i
    }, [weekDaysLocale[(i + weekFirstDay) % WEEK_DAY_COUNT]]));
  }
  const getCellClassName = useCellClassName({
    cellPrefixCls,
    today,
    value,
    generateConfig: generateConfig2,
    rangedValue: prefixColumn ? null : rangedValue.value,
    hoverRangedValue: prefixColumn ? null : hoverRangedValue.value,
    isSameCell: (current, target) => isSameDate(generateConfig2, current, target),
    isInView: (date) => isSameMonth(generateConfig2, date, viewDate),
    offsetCell: (date, offset) => generateConfig2.addDate(date, offset)
  });
  const getCellNode = dateRender ? (date) => dateRender({
    current: date,
    today
  }) : void 0;
  return createVNode(PanelBody_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "rowNum": rowCount,
    "colNum": WEEK_DAY_COUNT,
    "baseDate": baseDate,
    "getCellNode": getCellNode,
    "getCellText": generateConfig2.getDate,
    "getCellClassName": getCellClassName,
    "getCellDate": generateConfig2.addDate,
    "titleCell": (date) => formatValue(date, {
      locale,
      format: "YYYY-MM-DD",
      generateConfig: generateConfig2
    }),
    "headerCells": headerCells
  }), null);
}
DateBody.displayName = "DateBody";
DateBody.inheritAttrs = false;
DateBody.props = [
  "prefixCls",
  "generateConfig",
  "value?",
  "viewDate",
  "locale",
  "rowCount",
  "onSelect",
  "dateRender?",
  "disabledDate?",
  // Used for week panel
  "prefixColumn?",
  "rowClassName?"
];
var DateBody_default = DateBody;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/DatePanel/DateHeader.js
function DateHeader(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    generateConfig: generateConfig2,
    locale,
    viewDate,
    onNextMonth,
    onPrevMonth,
    onNextYear,
    onPrevYear,
    onYearClick,
    onMonthClick
  } = props;
  const {
    hideHeader
  } = useInjectPanel();
  if (hideHeader.value) {
    return null;
  }
  const headerPrefixCls = `${prefixCls}-header`;
  const monthsLocale = locale.shortMonths || (generateConfig2.locale.getShortMonths ? generateConfig2.locale.getShortMonths(locale.locale) : []);
  const month = generateConfig2.getMonth(viewDate);
  const yearNode = createVNode("button", {
    "type": "button",
    "key": "year",
    "onClick": onYearClick,
    "tabindex": -1,
    "class": `${prefixCls}-year-btn`
  }, [formatValue(viewDate, {
    locale,
    format: locale.yearFormat,
    generateConfig: generateConfig2
  })]);
  const monthNode = createVNode("button", {
    "type": "button",
    "key": "month",
    "onClick": onMonthClick,
    "tabindex": -1,
    "class": `${prefixCls}-month-btn`
  }, [locale.monthFormat ? formatValue(viewDate, {
    locale,
    format: locale.monthFormat,
    generateConfig: generateConfig2
  }) : monthsLocale[month]]);
  const monthYearNodes = locale.monthBeforeYear ? [monthNode, yearNode] : [yearNode, monthNode];
  return createVNode(Header_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": headerPrefixCls,
    "onSuperPrev": onPrevYear,
    "onPrev": onPrevMonth,
    "onNext": onNextMonth,
    "onSuperNext": onNextYear
  }), {
    default: () => [monthYearNodes]
  });
}
DateHeader.displayName = "DateHeader";
DateHeader.inheritAttrs = false;
var DateHeader_default = DateHeader;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/DatePanel/index.js
var DATE_ROW_COUNT = 6;
function DatePanel(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    panelName = "date",
    keyboardConfig,
    active,
    operationRef,
    generateConfig: generateConfig2,
    value,
    viewDate,
    onViewDateChange,
    onPanelChange,
    onSelect
  } = props;
  const panelPrefixCls = `${prefixCls}-${panelName}-panel`;
  operationRef.value = {
    onKeydown: (event) => createKeydownHandler(event, _extends({
      onLeftRight: (diff) => {
        onSelect(generateConfig2.addDate(value || viewDate, diff), "key");
      },
      onCtrlLeftRight: (diff) => {
        onSelect(generateConfig2.addYear(value || viewDate, diff), "key");
      },
      onUpDown: (diff) => {
        onSelect(generateConfig2.addDate(value || viewDate, diff * WEEK_DAY_COUNT), "key");
      },
      onPageUpDown: (diff) => {
        onSelect(generateConfig2.addMonth(value || viewDate, diff), "key");
      }
    }, keyboardConfig))
  };
  const onYearChange = (diff) => {
    const newDate = generateConfig2.addYear(viewDate, diff);
    onViewDateChange(newDate);
    onPanelChange(null, newDate);
  };
  const onMonthChange = (diff) => {
    const newDate = generateConfig2.addMonth(viewDate, diff);
    onViewDateChange(newDate);
    onPanelChange(null, newDate);
  };
  return createVNode("div", {
    "class": classNames_default(panelPrefixCls, {
      [`${panelPrefixCls}-active`]: active
    })
  }, [createVNode(DateHeader_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": prefixCls,
    "value": value,
    "viewDate": viewDate,
    "onPrevYear": () => {
      onYearChange(-1);
    },
    "onNextYear": () => {
      onYearChange(1);
    },
    "onPrevMonth": () => {
      onMonthChange(-1);
    },
    "onNextMonth": () => {
      onMonthChange(1);
    },
    "onMonthClick": () => {
      onPanelChange("month", viewDate);
    },
    "onYearClick": () => {
      onPanelChange("year", viewDate);
    }
  }), null), createVNode(DateBody_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "onSelect": (date) => onSelect(date, "mouse"),
    "prefixCls": prefixCls,
    "value": value,
    "viewDate": viewDate,
    "rowCount": DATE_ROW_COUNT
  }), null)]);
}
DatePanel.displayName = "DatePanel";
DatePanel.inheritAttrs = false;
var DatePanel_default = DatePanel;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/DatetimePanel/index.js
var ACTIVE_PANEL = tuple("date", "time");
function DatetimePanel(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    operationRef,
    generateConfig: generateConfig2,
    value,
    defaultValue,
    disabledTime,
    showTime,
    onSelect
  } = props;
  const panelPrefixCls = `${prefixCls}-datetime-panel`;
  const activePanel = ref(null);
  const dateOperationRef = ref({});
  const timeOperationRef = ref({});
  const timeProps = typeof showTime === "object" ? _extends({}, showTime) : {};
  function getNextActive(offset) {
    const activeIndex = ACTIVE_PANEL.indexOf(activePanel.value) + offset;
    const nextActivePanel = ACTIVE_PANEL[activeIndex] || null;
    return nextActivePanel;
  }
  const onBlur = (e) => {
    if (timeOperationRef.value.onBlur) {
      timeOperationRef.value.onBlur(e);
    }
    activePanel.value = null;
  };
  operationRef.value = {
    onKeydown: (event) => {
      if (event.which === KeyCode_default.TAB) {
        const nextActivePanel = getNextActive(event.shiftKey ? -1 : 1);
        activePanel.value = nextActivePanel;
        if (nextActivePanel) {
          event.preventDefault();
        }
        return true;
      }
      if (activePanel.value) {
        const ref2 = activePanel.value === "date" ? dateOperationRef : timeOperationRef;
        if (ref2.value && ref2.value.onKeydown) {
          ref2.value.onKeydown(event);
        }
        return true;
      }
      if ([KeyCode_default.LEFT, KeyCode_default.RIGHT, KeyCode_default.UP, KeyCode_default.DOWN].includes(event.which)) {
        activePanel.value = "date";
        return true;
      }
      return false;
    },
    onBlur,
    onClose: onBlur
  };
  const onInternalSelect = (date, source) => {
    let selectedDate = date;
    if (source === "date" && !value && timeProps.defaultValue) {
      selectedDate = generateConfig2.setHour(selectedDate, generateConfig2.getHour(timeProps.defaultValue));
      selectedDate = generateConfig2.setMinute(selectedDate, generateConfig2.getMinute(timeProps.defaultValue));
      selectedDate = generateConfig2.setSecond(selectedDate, generateConfig2.getSecond(timeProps.defaultValue));
    } else if (source === "time" && !value && defaultValue) {
      selectedDate = generateConfig2.setYear(selectedDate, generateConfig2.getYear(defaultValue));
      selectedDate = generateConfig2.setMonth(selectedDate, generateConfig2.getMonth(defaultValue));
      selectedDate = generateConfig2.setDate(selectedDate, generateConfig2.getDate(defaultValue));
    }
    if (onSelect) {
      onSelect(selectedDate, "mouse");
    }
  };
  const disabledTimes = disabledTime ? disabledTime(value || null) : {};
  return createVNode("div", {
    "class": classNames_default(panelPrefixCls, {
      [`${panelPrefixCls}-active`]: activePanel.value
    })
  }, [createVNode(DatePanel_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "operationRef": dateOperationRef,
    "active": activePanel.value === "date",
    "onSelect": (date) => {
      onInternalSelect(setDateTime(generateConfig2, date, !value && typeof showTime === "object" ? showTime.defaultValue : null), "date");
    }
  }), null), createVNode(TimePanel_default, _objectSpread2(_objectSpread2(_objectSpread2(_objectSpread2({}, props), {}, {
    "format": void 0
  }, timeProps), disabledTimes), {}, {
    "disabledTime": null,
    "defaultValue": void 0,
    "operationRef": timeOperationRef,
    "active": activePanel.value === "time",
    "onSelect": (date) => {
      onInternalSelect(date, "time");
    }
  }), null)]);
}
DatetimePanel.displayName = "DatetimePanel";
DatetimePanel.inheritAttrs = false;
var DatetimePanel_default = DatetimePanel;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/WeekPanel/index.js
function WeekPanel(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    generateConfig: generateConfig2,
    locale,
    value
  } = props;
  const cellPrefixCls = `${prefixCls}-cell`;
  const prefixColumn = (date) => createVNode("td", {
    "key": "week",
    "class": classNames_default(cellPrefixCls, `${cellPrefixCls}-week`)
  }, [generateConfig2.locale.getWeek(locale.locale, date)]);
  const rowPrefixCls = `${prefixCls}-week-panel-row`;
  const rowClassName = (date) => classNames_default(rowPrefixCls, {
    [`${rowPrefixCls}-selected`]: isSameWeek(generateConfig2, locale.locale, value, date)
  });
  return createVNode(DatePanel_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "panelName": "week",
    "prefixColumn": prefixColumn,
    "rowClassName": rowClassName,
    "keyboardConfig": {
      onLeftRight: null
    }
  }), null);
}
WeekPanel.displayName = "WeekPanel";
WeekPanel.inheritAttrs = false;
var WeekPanel_default = WeekPanel;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/MonthPanel/MonthHeader.js
function MonthHeader(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    generateConfig: generateConfig2,
    locale,
    viewDate,
    onNextYear,
    onPrevYear,
    onYearClick
  } = props;
  const {
    hideHeader
  } = useInjectPanel();
  if (hideHeader.value) {
    return null;
  }
  const headerPrefixCls = `${prefixCls}-header`;
  return createVNode(Header_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": headerPrefixCls,
    "onSuperPrev": onPrevYear,
    "onSuperNext": onNextYear
  }), {
    default: () => [createVNode("button", {
      "type": "button",
      "onClick": onYearClick,
      "class": `${prefixCls}-year-btn`
    }, [formatValue(viewDate, {
      locale,
      format: locale.yearFormat,
      generateConfig: generateConfig2
    })])]
  });
}
MonthHeader.displayName = "MonthHeader";
MonthHeader.inheritAttrs = false;
var MonthHeader_default = MonthHeader;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/MonthPanel/MonthBody.js
var MONTH_COL_COUNT = 3;
var MONTH_ROW_COUNT = 4;
function MonthBody(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    locale,
    value,
    viewDate,
    generateConfig: generateConfig2,
    monthCellRender
  } = props;
  const {
    rangedValue,
    hoverRangedValue
  } = useInjectRange();
  const cellPrefixCls = `${prefixCls}-cell`;
  const getCellClassName = useCellClassName({
    cellPrefixCls,
    value,
    generateConfig: generateConfig2,
    rangedValue: rangedValue.value,
    hoverRangedValue: hoverRangedValue.value,
    isSameCell: (current, target) => isSameMonth(generateConfig2, current, target),
    isInView: () => true,
    offsetCell: (date, offset) => generateConfig2.addMonth(date, offset)
  });
  const monthsLocale = locale.shortMonths || (generateConfig2.locale.getShortMonths ? generateConfig2.locale.getShortMonths(locale.locale) : []);
  const baseMonth = generateConfig2.setMonth(viewDate, 0);
  const getCellNode = monthCellRender ? (date) => monthCellRender({
    current: date,
    locale
  }) : void 0;
  return createVNode(PanelBody_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "rowNum": MONTH_ROW_COUNT,
    "colNum": MONTH_COL_COUNT,
    "baseDate": baseMonth,
    "getCellNode": getCellNode,
    "getCellText": (date) => locale.monthFormat ? formatValue(date, {
      locale,
      format: locale.monthFormat,
      generateConfig: generateConfig2
    }) : monthsLocale[generateConfig2.getMonth(date)],
    "getCellClassName": getCellClassName,
    "getCellDate": generateConfig2.addMonth,
    "titleCell": (date) => formatValue(date, {
      locale,
      format: "YYYY-MM",
      generateConfig: generateConfig2
    })
  }), null);
}
MonthBody.displayName = "MonthBody";
MonthBody.inheritAttrs = false;
var MonthBody_default = MonthBody;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/MonthPanel/index.js
function MonthPanel(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    operationRef,
    onViewDateChange,
    generateConfig: generateConfig2,
    value,
    viewDate,
    onPanelChange,
    onSelect
  } = props;
  const panelPrefixCls = `${prefixCls}-month-panel`;
  operationRef.value = {
    onKeydown: (event) => createKeydownHandler(event, {
      onLeftRight: (diff) => {
        onSelect(generateConfig2.addMonth(value || viewDate, diff), "key");
      },
      onCtrlLeftRight: (diff) => {
        onSelect(generateConfig2.addYear(value || viewDate, diff), "key");
      },
      onUpDown: (diff) => {
        onSelect(generateConfig2.addMonth(value || viewDate, diff * MONTH_COL_COUNT), "key");
      },
      onEnter: () => {
        onPanelChange("date", value || viewDate);
      }
    })
  };
  const onYearChange = (diff) => {
    const newDate = generateConfig2.addYear(viewDate, diff);
    onViewDateChange(newDate);
    onPanelChange(null, newDate);
  };
  return createVNode("div", {
    "class": panelPrefixCls
  }, [createVNode(MonthHeader_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": prefixCls,
    "onPrevYear": () => {
      onYearChange(-1);
    },
    "onNextYear": () => {
      onYearChange(1);
    },
    "onYearClick": () => {
      onPanelChange("year", viewDate);
    }
  }), null), createVNode(MonthBody_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": prefixCls,
    "onSelect": (date) => {
      onSelect(date, "mouse");
      onPanelChange("date", date);
    }
  }), null)]);
}
MonthPanel.displayName = "MonthPanel";
MonthPanel.inheritAttrs = false;
var MonthPanel_default = MonthPanel;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/QuarterPanel/QuarterHeader.js
function QuarterHeader(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    generateConfig: generateConfig2,
    locale,
    viewDate,
    onNextYear,
    onPrevYear,
    onYearClick
  } = props;
  const {
    hideHeader
  } = useInjectPanel();
  if (hideHeader.value) {
    return null;
  }
  const headerPrefixCls = `${prefixCls}-header`;
  return createVNode(Header_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": headerPrefixCls,
    "onSuperPrev": onPrevYear,
    "onSuperNext": onNextYear
  }), {
    default: () => [createVNode("button", {
      "type": "button",
      "onClick": onYearClick,
      "class": `${prefixCls}-year-btn`
    }, [formatValue(viewDate, {
      locale,
      format: locale.yearFormat,
      generateConfig: generateConfig2
    })])]
  });
}
QuarterHeader.displayName = "QuarterHeader";
QuarterHeader.inheritAttrs = false;
var QuarterHeader_default = QuarterHeader;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/QuarterPanel/QuarterBody.js
var QUARTER_COL_COUNT = 4;
var QUARTER_ROW_COUNT = 1;
function QuarterBody(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    locale,
    value,
    viewDate,
    generateConfig: generateConfig2
  } = props;
  const {
    rangedValue,
    hoverRangedValue
  } = useInjectRange();
  const cellPrefixCls = `${prefixCls}-cell`;
  const getCellClassName = useCellClassName({
    cellPrefixCls,
    value,
    generateConfig: generateConfig2,
    rangedValue: rangedValue.value,
    hoverRangedValue: hoverRangedValue.value,
    isSameCell: (current, target) => isSameQuarter(generateConfig2, current, target),
    isInView: () => true,
    offsetCell: (date, offset) => generateConfig2.addMonth(date, offset * 3)
  });
  const baseQuarter = generateConfig2.setDate(generateConfig2.setMonth(viewDate, 0), 1);
  return createVNode(PanelBody_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "rowNum": QUARTER_ROW_COUNT,
    "colNum": QUARTER_COL_COUNT,
    "baseDate": baseQuarter,
    "getCellText": (date) => formatValue(date, {
      locale,
      format: locale.quarterFormat || "[Q]Q",
      generateConfig: generateConfig2
    }),
    "getCellClassName": getCellClassName,
    "getCellDate": (date, offset) => generateConfig2.addMonth(date, offset * 3),
    "titleCell": (date) => formatValue(date, {
      locale,
      format: "YYYY-[Q]Q",
      generateConfig: generateConfig2
    })
  }), null);
}
QuarterBody.displayName = "QuarterBody";
QuarterBody.inheritAttrs = false;
var QuarterBody_default = QuarterBody;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/QuarterPanel/index.js
function QuarterPanel(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    operationRef,
    onViewDateChange,
    generateConfig: generateConfig2,
    value,
    viewDate,
    onPanelChange,
    onSelect
  } = props;
  const panelPrefixCls = `${prefixCls}-quarter-panel`;
  operationRef.value = {
    onKeydown: (event) => createKeydownHandler(event, {
      onLeftRight: (diff) => {
        onSelect(generateConfig2.addMonth(value || viewDate, diff * 3), "key");
      },
      onCtrlLeftRight: (diff) => {
        onSelect(generateConfig2.addYear(value || viewDate, diff), "key");
      },
      onUpDown: (diff) => {
        onSelect(generateConfig2.addYear(value || viewDate, diff), "key");
      }
    })
  };
  const onYearChange = (diff) => {
    const newDate = generateConfig2.addYear(viewDate, diff);
    onViewDateChange(newDate);
    onPanelChange(null, newDate);
  };
  return createVNode("div", {
    "class": panelPrefixCls
  }, [createVNode(QuarterHeader_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": prefixCls,
    "onPrevYear": () => {
      onYearChange(-1);
    },
    "onNextYear": () => {
      onYearChange(1);
    },
    "onYearClick": () => {
      onPanelChange("year", viewDate);
    }
  }), null), createVNode(QuarterBody_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": prefixCls,
    "onSelect": (date) => {
      onSelect(date, "mouse");
    }
  }), null)]);
}
QuarterPanel.displayName = "QuarterPanel";
QuarterPanel.inheritAttrs = false;
var QuarterPanel_default = QuarterPanel;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/YearPanel/YearHeader.js
function YearHeader(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    generateConfig: generateConfig2,
    viewDate,
    onPrevDecade,
    onNextDecade,
    onDecadeClick
  } = props;
  const {
    hideHeader
  } = useInjectPanel();
  if (hideHeader.value) {
    return null;
  }
  const headerPrefixCls = `${prefixCls}-header`;
  const yearNumber = generateConfig2.getYear(viewDate);
  const startYear = Math.floor(yearNumber / YEAR_DECADE_COUNT) * YEAR_DECADE_COUNT;
  const endYear = startYear + YEAR_DECADE_COUNT - 1;
  return createVNode(Header_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": headerPrefixCls,
    "onSuperPrev": onPrevDecade,
    "onSuperNext": onNextDecade
  }), {
    default: () => [createVNode("button", {
      "type": "button",
      "onClick": onDecadeClick,
      "class": `${prefixCls}-decade-btn`
    }, [startYear, createTextVNode("-"), endYear])]
  });
}
YearHeader.displayName = "YearHeader";
YearHeader.inheritAttrs = false;
var YearHeader_default = YearHeader;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/YearPanel/YearBody.js
var YEAR_COL_COUNT = 3;
var YEAR_ROW_COUNT = 4;
function YearBody(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    value,
    viewDate,
    locale,
    generateConfig: generateConfig2
  } = props;
  const {
    rangedValue,
    hoverRangedValue
  } = useInjectRange();
  const yearPrefixCls = `${prefixCls}-cell`;
  const yearNumber = generateConfig2.getYear(viewDate);
  const startYear = Math.floor(yearNumber / YEAR_DECADE_COUNT) * YEAR_DECADE_COUNT;
  const endYear = startYear + YEAR_DECADE_COUNT - 1;
  const baseYear = generateConfig2.setYear(viewDate, startYear - Math.ceil((YEAR_COL_COUNT * YEAR_ROW_COUNT - YEAR_DECADE_COUNT) / 2));
  const isInView = (date) => {
    const currentYearNumber = generateConfig2.getYear(date);
    return startYear <= currentYearNumber && currentYearNumber <= endYear;
  };
  const getCellClassName = useCellClassName({
    cellPrefixCls: yearPrefixCls,
    value,
    generateConfig: generateConfig2,
    rangedValue: rangedValue.value,
    hoverRangedValue: hoverRangedValue.value,
    isSameCell: (current, target) => isSameYear(generateConfig2, current, target),
    isInView,
    offsetCell: (date, offset) => generateConfig2.addYear(date, offset)
  });
  return createVNode(PanelBody_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "rowNum": YEAR_ROW_COUNT,
    "colNum": YEAR_COL_COUNT,
    "baseDate": baseYear,
    "getCellText": generateConfig2.getYear,
    "getCellClassName": getCellClassName,
    "getCellDate": generateConfig2.addYear,
    "titleCell": (date) => formatValue(date, {
      locale,
      format: "YYYY",
      generateConfig: generateConfig2
    })
  }), null);
}
YearBody.displayName = "YearBody";
YearBody.inheritAttrs = false;
var YearBody_default = YearBody;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/panels/YearPanel/index.js
var YEAR_DECADE_COUNT = 10;
function YearPanel(_props) {
  const props = useMergeProps(_props);
  const {
    prefixCls,
    operationRef,
    onViewDateChange,
    generateConfig: generateConfig2,
    value,
    viewDate,
    sourceMode,
    onSelect,
    onPanelChange
  } = props;
  const panelPrefixCls = `${prefixCls}-year-panel`;
  operationRef.value = {
    onKeydown: (event) => createKeydownHandler(event, {
      onLeftRight: (diff) => {
        onSelect(generateConfig2.addYear(value || viewDate, diff), "key");
      },
      onCtrlLeftRight: (diff) => {
        onSelect(generateConfig2.addYear(value || viewDate, diff * YEAR_DECADE_COUNT), "key");
      },
      onUpDown: (diff) => {
        onSelect(generateConfig2.addYear(value || viewDate, diff * YEAR_COL_COUNT), "key");
      },
      onEnter: () => {
        onPanelChange(sourceMode === "date" ? "date" : "month", value || viewDate);
      }
    })
  };
  const onDecadeChange = (diff) => {
    const newDate = generateConfig2.addYear(viewDate, diff * 10);
    onViewDateChange(newDate);
    onPanelChange(null, newDate);
  };
  return createVNode("div", {
    "class": panelPrefixCls
  }, [createVNode(YearHeader_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": prefixCls,
    "onPrevDecade": () => {
      onDecadeChange(-1);
    },
    "onNextDecade": () => {
      onDecadeChange(1);
    },
    "onDecadeClick": () => {
      onPanelChange("decade", viewDate);
    }
  }), null), createVNode(YearBody_default, _objectSpread2(_objectSpread2({}, props), {}, {
    "prefixCls": prefixCls,
    "onSelect": (date) => {
      onPanelChange(sourceMode === "date" ? "date" : "month", date);
      onSelect(date, "mouse");
    }
  }), null)]);
}
YearPanel.displayName = "YearPanel";
YearPanel.inheritAttrs = false;
var YearPanel_default = YearPanel;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/utils/getExtraFooter.js
function getExtraFooter(prefixCls, mode, renderExtraFooter) {
  if (!renderExtraFooter) {
    return null;
  }
  return createVNode("div", {
    "class": `${prefixCls}-footer-extra`
  }, [renderExtraFooter(mode)]);
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/utils/getRanges.js
function getRanges(_ref) {
  let {
    prefixCls,
    components = {},
    needConfirmButton,
    onNow,
    onOk,
    okDisabled,
    showNow,
    locale
  } = _ref;
  let presetNode;
  let okNode;
  if (needConfirmButton) {
    const Button = components.button || "button";
    if (onNow && showNow !== false) {
      presetNode = createVNode("li", {
        "class": `${prefixCls}-now`
      }, [createVNode("a", {
        "class": `${prefixCls}-now-btn`,
        "onClick": onNow
      }, [locale.now])]);
    }
    okNode = needConfirmButton && createVNode("li", {
      "class": `${prefixCls}-ok`
    }, [createVNode(Button, {
      "disabled": okDisabled,
      "onClick": (e) => {
        e.stopPropagation();
        onOk && onOk();
      }
    }, {
      default: () => [locale.ok]
    })]);
  }
  if (!presetNode && !okNode) {
    return null;
  }
  return createVNode("ul", {
    "class": `${prefixCls}-ranges`
  }, [presetNode, okNode]);
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/PickerPanel.js
function PickerPanel() {
  return defineComponent({
    name: "PickerPanel",
    inheritAttrs: false,
    props: {
      prefixCls: String,
      locale: Object,
      generateConfig: Object,
      value: Object,
      defaultValue: Object,
      pickerValue: Object,
      defaultPickerValue: Object,
      disabledDate: Function,
      mode: String,
      picker: {
        type: String,
        default: "date"
      },
      tabindex: {
        type: [Number, String],
        default: 0
      },
      showNow: {
        type: Boolean,
        default: void 0
      },
      showTime: [Boolean, Object],
      showToday: Boolean,
      renderExtraFooter: Function,
      dateRender: Function,
      hideHeader: {
        type: Boolean,
        default: void 0
      },
      onSelect: Function,
      onChange: Function,
      onPanelChange: Function,
      onMousedown: Function,
      onPickerValueChange: Function,
      onOk: Function,
      components: Object,
      direction: String,
      hourStep: {
        type: Number,
        default: 1
      },
      minuteStep: {
        type: Number,
        default: 1
      },
      secondStep: {
        type: Number,
        default: 1
      }
    },
    setup(props, _ref) {
      let {
        attrs
      } = _ref;
      const needConfirmButton = computed(() => props.picker === "date" && !!props.showTime || props.picker === "time");
      const isHourStepValid = computed(() => 24 % props.hourStep === 0);
      const isMinuteStepValid = computed(() => 60 % props.minuteStep === 0);
      const isSecondStepValid = computed(() => 60 % props.secondStep === 0);
      if (true) {
        watchEffect(() => {
          const {
            generateConfig: generateConfig2,
            value,
            hourStep = 1,
            minuteStep = 1,
            secondStep = 1
          } = props;
          warning(!value || generateConfig2.isValidate(value), "Invalidate date pass to `value`.");
          warning(!value || generateConfig2.isValidate(value), "Invalidate date pass to `defaultValue`.");
          warning(isHourStepValid.value, `\`hourStep\` ${hourStep} is invalid. It should be a factor of 24.`);
          warning(isMinuteStepValid.value, `\`minuteStep\` ${minuteStep} is invalid. It should be a factor of 60.`);
          warning(isSecondStepValid.value, `\`secondStep\` ${secondStep} is invalid. It should be a factor of 60.`);
        });
      }
      const panelContext = useInjectPanel();
      const {
        operationRef,
        onSelect: onContextSelect,
        hideRanges,
        defaultOpenValue
      } = panelContext;
      const {
        inRange,
        panelPosition,
        rangedValue,
        hoverRangedValue
      } = useInjectRange();
      const panelRef = ref({});
      const [mergedValue, setInnerValue] = useMergedState(null, {
        value: toRef(props, "value"),
        defaultValue: props.defaultValue,
        postState: (val) => {
          if (!val && (defaultOpenValue === null || defaultOpenValue === void 0 ? void 0 : defaultOpenValue.value) && props.picker === "time") {
            return defaultOpenValue.value;
          }
          return val;
        }
      });
      const [viewDate, setInnerViewDate] = useMergedState(null, {
        value: toRef(props, "pickerValue"),
        defaultValue: props.defaultPickerValue || mergedValue.value,
        postState: (date) => {
          const {
            generateConfig: generateConfig2,
            showTime,
            defaultValue
          } = props;
          const now = generateConfig2.getNow();
          if (!date) return now;
          if (!mergedValue.value && props.showTime) {
            if (typeof showTime === "object") {
              return setDateTime(generateConfig2, Array.isArray(date) ? date[0] : date, showTime.defaultValue || now);
            }
            if (defaultValue) {
              return setDateTime(generateConfig2, Array.isArray(date) ? date[0] : date, defaultValue);
            }
            return setDateTime(generateConfig2, Array.isArray(date) ? date[0] : date, now);
          }
          return date;
        }
      });
      const setViewDate = (date) => {
        setInnerViewDate(date);
        if (props.onPickerValueChange) {
          props.onPickerValueChange(date);
        }
      };
      const getInternalNextMode = (nextMode) => {
        const getNextMode = PickerModeMap[props.picker];
        if (getNextMode) {
          return getNextMode(nextMode);
        }
        return nextMode;
      };
      const [mergedMode, setInnerMode] = useMergedState(() => {
        if (props.picker === "time") {
          return "time";
        }
        return getInternalNextMode("date");
      }, {
        value: toRef(props, "mode")
      });
      watch(() => props.picker, () => {
        setInnerMode(props.picker);
      });
      const sourceMode = ref(mergedMode.value);
      const setSourceMode = (val) => {
        sourceMode.value = val;
      };
      const onInternalPanelChange = (newMode, viewValue) => {
        const {
          onPanelChange,
          generateConfig: generateConfig2
        } = props;
        const nextMode = getInternalNextMode(newMode || mergedMode.value);
        setSourceMode(mergedMode.value);
        setInnerMode(nextMode);
        if (onPanelChange && (mergedMode.value !== nextMode || isEqual(generateConfig2, viewDate.value, viewDate.value))) {
          onPanelChange(viewValue, nextMode);
        }
      };
      const triggerSelect = function(date, type) {
        let forceTriggerSelect = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
        const {
          picker,
          generateConfig: generateConfig2,
          onSelect,
          onChange,
          disabledDate
        } = props;
        if (mergedMode.value === picker || forceTriggerSelect) {
          setInnerValue(date);
          if (onSelect) {
            onSelect(date);
          }
          if (onContextSelect) {
            onContextSelect(date, type);
          }
          if (onChange && !isEqual(generateConfig2, date, mergedValue.value) && !(disabledDate === null || disabledDate === void 0 ? void 0 : disabledDate(date))) {
            onChange(date);
          }
        }
      };
      const onInternalKeydown = (e) => {
        if (panelRef.value && panelRef.value.onKeydown) {
          if ([KeyCode_default.LEFT, KeyCode_default.RIGHT, KeyCode_default.UP, KeyCode_default.DOWN, KeyCode_default.PAGE_UP, KeyCode_default.PAGE_DOWN, KeyCode_default.ENTER].includes(e.which)) {
            e.preventDefault();
          }
          return panelRef.value.onKeydown(e);
        }
        {
          warning(false, "Panel not correct handle keyDown event. Please help to fire issue about this.");
          return false;
        }
      };
      const onInternalBlur = (e) => {
        if (panelRef.value && panelRef.value.onBlur) {
          panelRef.value.onBlur(e);
        }
      };
      const onNow = () => {
        const {
          generateConfig: generateConfig2,
          hourStep,
          minuteStep,
          secondStep
        } = props;
        const now = generateConfig2.getNow();
        const lowerBoundTime = getLowerBoundTime(generateConfig2.getHour(now), generateConfig2.getMinute(now), generateConfig2.getSecond(now), isHourStepValid.value ? hourStep : 1, isMinuteStepValid.value ? minuteStep : 1, isSecondStepValid.value ? secondStep : 1);
        const adjustedNow = setTime(
          generateConfig2,
          now,
          lowerBoundTime[0],
          // hour
          lowerBoundTime[1],
          // minute
          lowerBoundTime[2]
        );
        triggerSelect(adjustedNow, "submit");
      };
      const classString = computed(() => {
        const {
          prefixCls,
          direction
        } = props;
        return classNames_default(`${prefixCls}-panel`, {
          [`${prefixCls}-panel-has-range`]: rangedValue && rangedValue.value && rangedValue.value[0] && rangedValue.value[1],
          [`${prefixCls}-panel-has-range-hover`]: hoverRangedValue && hoverRangedValue.value && hoverRangedValue.value[0] && hoverRangedValue.value[1],
          [`${prefixCls}-panel-rtl`]: direction === "rtl"
        });
      });
      useProvidePanel(_extends(_extends({}, panelContext), {
        mode: mergedMode,
        hideHeader: computed(() => {
          var _a2;
          return props.hideHeader !== void 0 ? props.hideHeader : (_a2 = panelContext.hideHeader) === null || _a2 === void 0 ? void 0 : _a2.value;
        }),
        hidePrevBtn: computed(() => inRange.value && panelPosition.value === "right"),
        hideNextBtn: computed(() => inRange.value && panelPosition.value === "left")
      }));
      watch(() => props.value, () => {
        if (props.value) {
          setInnerViewDate(props.value);
        }
      });
      return () => {
        const {
          prefixCls = "ant-picker",
          locale,
          generateConfig: generateConfig2,
          disabledDate,
          picker = "date",
          tabindex = 0,
          showNow,
          showTime,
          showToday,
          renderExtraFooter,
          onMousedown,
          onOk,
          components
        } = props;
        if (operationRef && panelPosition.value !== "right") {
          operationRef.value = {
            onKeydown: onInternalKeydown,
            onClose: () => {
              if (panelRef.value && panelRef.value.onClose) {
                panelRef.value.onClose();
              }
            }
          };
        }
        let panelNode;
        const pickerProps = _extends(_extends(_extends({}, attrs), props), {
          operationRef: panelRef,
          prefixCls,
          viewDate: viewDate.value,
          value: mergedValue.value,
          onViewDateChange: setViewDate,
          sourceMode: sourceMode.value,
          onPanelChange: onInternalPanelChange,
          disabledDate
        });
        delete pickerProps.onChange;
        delete pickerProps.onSelect;
        switch (mergedMode.value) {
          case "decade":
            panelNode = createVNode(DecadePanel_default, _objectSpread2(_objectSpread2({}, pickerProps), {}, {
              "onSelect": (date, type) => {
                setViewDate(date);
                triggerSelect(date, type);
              }
            }), null);
            break;
          case "year":
            panelNode = createVNode(YearPanel_default, _objectSpread2(_objectSpread2({}, pickerProps), {}, {
              "onSelect": (date, type) => {
                setViewDate(date);
                triggerSelect(date, type);
              }
            }), null);
            break;
          case "month":
            panelNode = createVNode(MonthPanel_default, _objectSpread2(_objectSpread2({}, pickerProps), {}, {
              "onSelect": (date, type) => {
                setViewDate(date);
                triggerSelect(date, type);
              }
            }), null);
            break;
          case "quarter":
            panelNode = createVNode(QuarterPanel_default, _objectSpread2(_objectSpread2({}, pickerProps), {}, {
              "onSelect": (date, type) => {
                setViewDate(date);
                triggerSelect(date, type);
              }
            }), null);
            break;
          case "week":
            panelNode = createVNode(WeekPanel_default, _objectSpread2(_objectSpread2({}, pickerProps), {}, {
              "onSelect": (date, type) => {
                setViewDate(date);
                triggerSelect(date, type);
              }
            }), null);
            break;
          case "time":
            delete pickerProps.showTime;
            panelNode = createVNode(TimePanel_default, _objectSpread2(_objectSpread2(_objectSpread2({}, pickerProps), typeof showTime === "object" ? showTime : null), {}, {
              "onSelect": (date, type) => {
                setViewDate(date);
                triggerSelect(date, type);
              }
            }), null);
            break;
          default:
            if (showTime) {
              panelNode = createVNode(DatetimePanel_default, _objectSpread2(_objectSpread2({}, pickerProps), {}, {
                "onSelect": (date, type) => {
                  setViewDate(date);
                  triggerSelect(date, type);
                }
              }), null);
            } else {
              panelNode = createVNode(DatePanel_default, _objectSpread2(_objectSpread2({}, pickerProps), {}, {
                "onSelect": (date, type) => {
                  setViewDate(date);
                  triggerSelect(date, type);
                }
              }), null);
            }
        }
        let extraFooter;
        let rangesNode;
        if (!(hideRanges === null || hideRanges === void 0 ? void 0 : hideRanges.value)) {
          extraFooter = getExtraFooter(prefixCls, mergedMode.value, renderExtraFooter);
          rangesNode = getRanges({
            prefixCls,
            components,
            needConfirmButton: needConfirmButton.value,
            okDisabled: !mergedValue.value || disabledDate && disabledDate(mergedValue.value),
            locale,
            showNow,
            onNow: needConfirmButton.value && onNow,
            onOk: () => {
              if (mergedValue.value) {
                triggerSelect(mergedValue.value, "submit", true);
                if (onOk) {
                  onOk(mergedValue.value);
                }
              }
            }
          });
        }
        let todayNode;
        if (showToday && mergedMode.value === "date" && picker === "date" && !showTime) {
          const now = generateConfig2.getNow();
          const todayCls = `${prefixCls}-today-btn`;
          const disabled = disabledDate && disabledDate(now);
          todayNode = createVNode("a", {
            "class": classNames_default(todayCls, disabled && `${todayCls}-disabled`),
            "aria-disabled": disabled,
            "onClick": () => {
              if (!disabled) {
                triggerSelect(now, "mouse", true);
              }
            }
          }, [locale.today]);
        }
        return createVNode("div", {
          "tabindex": tabindex,
          "class": classNames_default(classString.value, attrs.class),
          "style": attrs.style,
          "onKeydown": onInternalKeydown,
          "onBlur": onInternalBlur,
          "onMousedown": onMousedown
        }, [panelNode, extraFooter || rangesNode || todayNode ? createVNode("div", {
          "class": `${prefixCls}-footer`
        }, [extraFooter, rangesNode, todayNode]) : null]);
      };
    }
  });
}
var InterPickerPanel = PickerPanel();
var PickerPanel_default = (props) => createVNode(InterPickerPanel, props);

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/PickerTrigger.js
var BUILT_IN_PLACEMENTS = {
  bottomLeft: {
    points: ["tl", "bl"],
    offset: [0, 4],
    overflow: {
      adjustX: 1,
      adjustY: 1
    }
  },
  bottomRight: {
    points: ["tr", "br"],
    offset: [0, 4],
    overflow: {
      adjustX: 1,
      adjustY: 1
    }
  },
  topLeft: {
    points: ["bl", "tl"],
    offset: [0, -4],
    overflow: {
      adjustX: 0,
      adjustY: 1
    }
  },
  topRight: {
    points: ["br", "tr"],
    offset: [0, -4],
    overflow: {
      adjustX: 0,
      adjustY: 1
    }
  }
};
function PickerTrigger(props, _ref) {
  let {
    slots
  } = _ref;
  const {
    prefixCls,
    popupStyle,
    visible,
    dropdownClassName,
    dropdownAlign,
    transitionName,
    getPopupContainer,
    range,
    popupPlacement,
    direction
  } = useMergeProps(props);
  const dropdownPrefixCls = `${prefixCls}-dropdown`;
  const getPopupPlacement = () => {
    if (popupPlacement !== void 0) {
      return popupPlacement;
    }
    return direction === "rtl" ? "bottomRight" : "bottomLeft";
  };
  return createVNode(vc_trigger_default, {
    "showAction": [],
    "hideAction": [],
    "popupPlacement": getPopupPlacement(),
    "builtinPlacements": BUILT_IN_PLACEMENTS,
    "prefixCls": dropdownPrefixCls,
    "popupTransitionName": transitionName,
    "popupAlign": dropdownAlign,
    "popupVisible": visible,
    "popupClassName": classNames_default(dropdownClassName, {
      [`${dropdownPrefixCls}-range`]: range,
      [`${dropdownPrefixCls}-rtl`]: direction === "rtl"
    }),
    "popupStyle": popupStyle,
    "getPopupContainer": getPopupContainer
  }, {
    default: slots.default,
    popup: slots.popupElement
  });
}
var PickerTrigger_default = PickerTrigger;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/PresetPanel.js
var PresetPanel_default = defineComponent({
  name: "PresetPanel",
  props: {
    prefixCls: String,
    presets: {
      type: Array,
      default: () => []
    },
    onClick: Function,
    onHover: Function
  },
  setup(props) {
    return () => {
      if (!props.presets.length) {
        return null;
      }
      return createVNode("div", {
        "class": `${props.prefixCls}-presets`
      }, [createVNode("ul", null, [props.presets.map((_ref, index) => {
        let {
          label,
          value
        } = _ref;
        return createVNode("li", {
          "key": index,
          "onClick": (e) => {
            e.stopPropagation();
            props.onClick(value);
          },
          "onMouseenter": () => {
            var _a2;
            (_a2 = props.onHover) === null || _a2 === void 0 ? void 0 : _a2.call(props, value);
          },
          "onMouseleave": () => {
            var _a2;
            (_a2 = props.onHover) === null || _a2 === void 0 ? void 0 : _a2.call(props, null);
          }
        }, [label]);
      })])]);
    };
  }
});

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/hooks/usePickerInput.js
function usePickerInput(_ref) {
  let {
    open,
    value,
    isClickOutside,
    triggerOpen,
    forwardKeydown,
    onKeydown,
    blurToCancel,
    onSubmit,
    onCancel,
    onFocus,
    onBlur
  } = _ref;
  const typing = shallowRef(false);
  const focused = shallowRef(false);
  const preventBlurRef = shallowRef(false);
  const valueChangedRef = shallowRef(false);
  const preventDefaultRef = shallowRef(false);
  const inputProps = computed(() => ({
    onMousedown: () => {
      typing.value = true;
      triggerOpen(true);
    },
    onKeydown: (e) => {
      const preventDefault = () => {
        preventDefaultRef.value = true;
      };
      onKeydown(e, preventDefault);
      if (preventDefaultRef.value) return;
      switch (e.which) {
        case KeyCode_default.ENTER: {
          if (!open.value) {
            triggerOpen(true);
          } else if (onSubmit() !== false) {
            typing.value = true;
          }
          e.preventDefault();
          return;
        }
        case KeyCode_default.TAB: {
          if (typing.value && open.value && !e.shiftKey) {
            typing.value = false;
            e.preventDefault();
          } else if (!typing.value && open.value) {
            if (!forwardKeydown(e) && e.shiftKey) {
              typing.value = true;
              e.preventDefault();
            }
          }
          return;
        }
        case KeyCode_default.ESC: {
          typing.value = true;
          onCancel();
          return;
        }
      }
      if (!open.value && ![KeyCode_default.SHIFT].includes(e.which)) {
        triggerOpen(true);
      } else if (!typing.value) {
        forwardKeydown(e);
      }
    },
    onFocus: (e) => {
      typing.value = true;
      focused.value = true;
      if (onFocus) {
        onFocus(e);
      }
    },
    onBlur: (e) => {
      if (preventBlurRef.value || !isClickOutside(document.activeElement)) {
        preventBlurRef.value = false;
        return;
      }
      if (blurToCancel.value) {
        setTimeout(() => {
          let {
            activeElement
          } = document;
          while (activeElement && activeElement.shadowRoot) {
            activeElement = activeElement.shadowRoot.activeElement;
          }
          if (isClickOutside(activeElement)) {
            onCancel();
          }
        }, 0);
      } else if (open.value) {
        triggerOpen(false);
        if (valueChangedRef.value) {
          onSubmit();
        }
      }
      focused.value = false;
      if (onBlur) {
        onBlur(e);
      }
    }
  }));
  watch(open, () => {
    valueChangedRef.value = false;
  });
  watch(value, () => {
    valueChangedRef.value = true;
  });
  const globalMousedownEvent = shallowRef();
  onMounted(() => {
    globalMousedownEvent.value = addGlobalMousedownEvent((e) => {
      const target = getTargetFromEvent(e);
      if (open.value) {
        const clickedOutside = isClickOutside(target);
        if (!clickedOutside) {
          preventBlurRef.value = true;
          wrapperRaf(() => {
            preventBlurRef.value = false;
          });
        } else if (!focused.value || clickedOutside) {
          triggerOpen(false);
        }
      }
    });
  });
  onBeforeUnmount(() => {
    globalMousedownEvent.value && globalMousedownEvent.value();
  });
  return [inputProps, {
    focused,
    typing
  }];
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/hooks/useTextValueMapping.js
function useTextValueMapping(_ref) {
  let {
    valueTexts,
    onTextChange
  } = _ref;
  const text = ref("");
  function triggerTextChange(value) {
    text.value = value;
    onTextChange(value);
  }
  function resetText() {
    text.value = valueTexts.value[0];
  }
  watch(() => [...valueTexts.value], function(cur) {
    let pre = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];
    if (cur.join("||") !== pre.join("||") && valueTexts.value.every((valText) => valText !== text.value)) {
      resetText();
    }
  }, {
    immediate: true
  });
  return [text, triggerTextChange, resetText];
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/hooks/useValueTexts.js
function useValueTexts(value, _ref) {
  let {
    formatList,
    generateConfig: generateConfig2,
    locale
  } = _ref;
  const texts = useMemo(() => {
    if (!value.value) {
      return [[""], ""];
    }
    let firstValueText2 = "";
    const fullValueTexts2 = [];
    for (let i = 0; i < formatList.value.length; i += 1) {
      const format = formatList.value[i];
      const formatStr = formatValue(value.value, {
        generateConfig: generateConfig2.value,
        locale: locale.value,
        format
      });
      fullValueTexts2.push(formatStr);
      if (i === 0) {
        firstValueText2 = formatStr;
      }
    }
    return [fullValueTexts2, firstValueText2];
  }, [value, formatList], (next, prev) => prev[0] !== next[0] || !shallowequal_default(prev[1], next[1]));
  const fullValueTexts = computed(() => texts.value[0]);
  const firstValueText = computed(() => texts.value[1]);
  return [fullValueTexts, firstValueText];
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/hooks/useHoverValue.js
function useHoverValue(valueText, _ref) {
  let {
    formatList,
    generateConfig: generateConfig2,
    locale
  } = _ref;
  const innerValue = ref(null);
  let rafId;
  function setValue(val) {
    let immediately = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
    wrapperRaf.cancel(rafId);
    if (immediately) {
      innerValue.value = val;
      return;
    }
    rafId = wrapperRaf(() => {
      innerValue.value = val;
    });
  }
  const [, firstText] = useValueTexts(innerValue, {
    formatList,
    generateConfig: generateConfig2,
    locale
  });
  function onEnter(date) {
    setValue(date);
  }
  function onLeave() {
    let immediately = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;
    setValue(null, immediately);
  }
  watch(valueText, () => {
    onLeave(true);
  });
  onBeforeUnmount(() => {
    wrapperRaf.cancel(rafId);
  });
  return [firstText, onEnter, onLeave];
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/hooks/usePresets.js
function usePresets(presets, legacyRanges) {
  return computed(() => {
    if (presets === null || presets === void 0 ? void 0 : presets.value) {
      return presets.value;
    }
    if (legacyRanges === null || legacyRanges === void 0 ? void 0 : legacyRanges.value) {
      warning_default(false, "`ranges` is deprecated. Please use `presets` instead.");
      const rangeLabels = Object.keys(legacyRanges.value);
      return rangeLabels.map((label) => {
        const range = legacyRanges.value[label];
        const newValues = typeof range === "function" ? range() : range;
        return {
          label,
          value: newValues
        };
      });
    }
    return [];
  });
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/utils/warnUtil.js
function legacyPropsWarning(props) {
  const {
    picker,
    disabledHours,
    disabledMinutes,
    disabledSeconds
  } = props;
  if (picker === "time" && (disabledHours || disabledMinutes || disabledSeconds)) {
    warning(false, `'disabledHours', 'disabledMinutes', 'disabledSeconds' will be removed in the next major version, please use 'disabledTime' instead.`);
  }
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/Picker.js
function Picker() {
  return defineComponent({
    name: "Picker",
    inheritAttrs: false,
    props: ["prefixCls", "id", "tabindex", "dropdownClassName", "dropdownAlign", "popupStyle", "transitionName", "generateConfig", "locale", "inputReadOnly", "allowClear", "autofocus", "showTime", "showNow", "showHour", "showMinute", "showSecond", "picker", "format", "use12Hours", "value", "defaultValue", "open", "defaultOpen", "defaultOpenValue", "suffixIcon", "presets", "clearIcon", "disabled", "disabledDate", "placeholder", "getPopupContainer", "panelRender", "inputRender", "onChange", "onOpenChange", "onPanelChange", "onFocus", "onBlur", "onMousedown", "onMouseup", "onMouseenter", "onMouseleave", "onContextmenu", "onClick", "onKeydown", "onSelect", "direction", "autocomplete", "showToday", "renderExtraFooter", "dateRender", "minuteStep", "hourStep", "secondStep", "hideDisabledOptions"],
    setup(props, _ref) {
      let {
        attrs,
        expose
      } = _ref;
      const inputRef = ref(null);
      const presets = computed(() => props.presets);
      const presetList = usePresets(presets);
      const picker = computed(() => {
        var _a2;
        return (_a2 = props.picker) !== null && _a2 !== void 0 ? _a2 : "date";
      });
      const needConfirmButton = computed(() => picker.value === "date" && !!props.showTime || picker.value === "time");
      if (true) {
        legacyPropsWarning(props);
      }
      const formatList = computed(() => toArray(getDefaultFormat(props.format, picker.value, props.showTime, props.use12Hours)));
      const panelDivRef = ref(null);
      const inputDivRef = ref(null);
      const containerRef = ref(null);
      const [mergedValue, setInnerValue] = useMergedState(null, {
        value: toRef(props, "value"),
        defaultValue: props.defaultValue
      });
      const selectedValue = ref(mergedValue.value);
      const setSelectedValue = (val) => {
        selectedValue.value = val;
      };
      const operationRef = ref(null);
      const [mergedOpen, triggerInnerOpen] = useMergedState(false, {
        value: toRef(props, "open"),
        defaultValue: props.defaultOpen,
        postState: (postOpen) => props.disabled ? false : postOpen,
        onChange: (newOpen) => {
          if (props.onOpenChange) {
            props.onOpenChange(newOpen);
          }
          if (!newOpen && operationRef.value && operationRef.value.onClose) {
            operationRef.value.onClose();
          }
        }
      });
      const [valueTexts, firstValueText] = useValueTexts(selectedValue, {
        formatList,
        generateConfig: toRef(props, "generateConfig"),
        locale: toRef(props, "locale")
      });
      const [text, triggerTextChange, resetText] = useTextValueMapping({
        valueTexts,
        onTextChange: (newText) => {
          const inputDate = parseValue(newText, {
            locale: props.locale,
            formatList: formatList.value,
            generateConfig: props.generateConfig
          });
          if (inputDate && (!props.disabledDate || !props.disabledDate(inputDate))) {
            setSelectedValue(inputDate);
          }
        }
      });
      const triggerChange = (newValue) => {
        const {
          onChange,
          generateConfig: generateConfig2,
          locale
        } = props;
        setSelectedValue(newValue);
        setInnerValue(newValue);
        if (onChange && !isEqual(generateConfig2, mergedValue.value, newValue)) {
          onChange(newValue, newValue ? formatValue(newValue, {
            generateConfig: generateConfig2,
            locale,
            format: formatList.value[0]
          }) : "");
        }
      };
      const triggerOpen = (newOpen) => {
        if (props.disabled && newOpen) {
          return;
        }
        triggerInnerOpen(newOpen);
      };
      const forwardKeydown = (e) => {
        if (mergedOpen.value && operationRef.value && operationRef.value.onKeydown) {
          return operationRef.value.onKeydown(e);
        }
        {
          warning(false, "Picker not correct forward Keydown operation. Please help to fire issue about this.");
          return false;
        }
      };
      const onInternalMouseup = function() {
        if (props.onMouseup) {
          props.onMouseup(...arguments);
        }
        if (inputRef.value) {
          inputRef.value.focus();
          triggerOpen(true);
        }
      };
      const [inputProps, {
        focused,
        typing
      }] = usePickerInput({
        blurToCancel: needConfirmButton,
        open: mergedOpen,
        value: text,
        triggerOpen,
        forwardKeydown,
        isClickOutside: (target) => !elementsContains([panelDivRef.value, inputDivRef.value, containerRef.value], target),
        onSubmit: () => {
          if (
            // When user typing disabledDate with keyboard and enter, this value will be empty
            !selectedValue.value || // Normal disabled check
            props.disabledDate && props.disabledDate(selectedValue.value)
          ) {
            return false;
          }
          triggerChange(selectedValue.value);
          triggerOpen(false);
          resetText();
          return true;
        },
        onCancel: () => {
          triggerOpen(false);
          setSelectedValue(mergedValue.value);
          resetText();
        },
        onKeydown: (e, preventDefault) => {
          var _a2;
          (_a2 = props.onKeydown) === null || _a2 === void 0 ? void 0 : _a2.call(props, e, preventDefault);
        },
        onFocus: (e) => {
          var _a2;
          (_a2 = props.onFocus) === null || _a2 === void 0 ? void 0 : _a2.call(props, e);
        },
        onBlur: (e) => {
          var _a2;
          (_a2 = props.onBlur) === null || _a2 === void 0 ? void 0 : _a2.call(props, e);
        }
      });
      watch([mergedOpen, valueTexts], () => {
        if (!mergedOpen.value) {
          setSelectedValue(mergedValue.value);
          if (!valueTexts.value.length || valueTexts.value[0] === "") {
            triggerTextChange("");
          } else if (firstValueText.value !== text.value) {
            resetText();
          }
        }
      });
      watch(picker, () => {
        if (!mergedOpen.value) {
          resetText();
        }
      });
      watch(mergedValue, () => {
        setSelectedValue(mergedValue.value);
      });
      const [hoverValue, onEnter, onLeave] = useHoverValue(text, {
        formatList,
        generateConfig: toRef(props, "generateConfig"),
        locale: toRef(props, "locale")
      });
      const onContextSelect = (date, type) => {
        if (type === "submit" || type !== "key" && !needConfirmButton.value) {
          triggerChange(date);
          triggerOpen(false);
        }
      };
      useProvidePanel({
        operationRef,
        hideHeader: computed(() => picker.value === "time"),
        onSelect: onContextSelect,
        open: mergedOpen,
        defaultOpenValue: toRef(props, "defaultOpenValue"),
        onDateMouseenter: onEnter,
        onDateMouseleave: onLeave
      });
      expose({
        focus: () => {
          if (inputRef.value) {
            inputRef.value.focus();
          }
        },
        blur: () => {
          if (inputRef.value) {
            inputRef.value.blur();
          }
        }
      });
      return () => {
        const {
          prefixCls = "rc-picker",
          id,
          tabindex,
          dropdownClassName,
          dropdownAlign,
          popupStyle,
          transitionName,
          generateConfig: generateConfig2,
          locale,
          inputReadOnly,
          allowClear,
          autofocus,
          picker: picker2 = "date",
          defaultOpenValue,
          suffixIcon,
          clearIcon,
          disabled,
          placeholder,
          getPopupContainer,
          panelRender,
          onMousedown,
          onMouseenter,
          onMouseleave,
          onContextmenu,
          onClick,
          onSelect,
          direction,
          autocomplete = "off"
        } = props;
        const panelProps = _extends(_extends(_extends({}, props), attrs), {
          class: classNames_default({
            [`${prefixCls}-panel-focused`]: !typing.value
          }),
          style: void 0,
          pickerValue: void 0,
          onPickerValueChange: void 0,
          onChange: null
        });
        let panelNode = createVNode("div", {
          "class": `${prefixCls}-panel-layout`
        }, [createVNode(PresetPanel_default, {
          "prefixCls": prefixCls,
          "presets": presetList.value,
          "onClick": (nextValue) => {
            triggerChange(nextValue);
            triggerOpen(false);
          }
        }, null), createVNode(PickerPanel_default, _objectSpread2(_objectSpread2({}, panelProps), {}, {
          "generateConfig": generateConfig2,
          "value": selectedValue.value,
          "locale": locale,
          "tabindex": -1,
          "onSelect": (date) => {
            onSelect === null || onSelect === void 0 ? void 0 : onSelect(date);
            setSelectedValue(date);
          },
          "direction": direction,
          "onPanelChange": (viewDate, mode) => {
            const {
              onPanelChange
            } = props;
            onLeave(true);
            onPanelChange === null || onPanelChange === void 0 ? void 0 : onPanelChange(viewDate, mode);
          }
        }), null)]);
        if (panelRender) {
          panelNode = panelRender(panelNode);
        }
        const panel = createVNode("div", {
          "class": `${prefixCls}-panel-container`,
          "ref": panelDivRef,
          "onMousedown": (e) => {
            e.preventDefault();
          }
        }, [panelNode]);
        let suffixNode;
        if (suffixIcon) {
          suffixNode = createVNode("span", {
            "class": `${prefixCls}-suffix`
          }, [suffixIcon]);
        }
        let clearNode;
        if (allowClear && mergedValue.value && !disabled) {
          clearNode = createVNode("span", {
            "onMousedown": (e) => {
              e.preventDefault();
              e.stopPropagation();
            },
            "onMouseup": (e) => {
              e.preventDefault();
              e.stopPropagation();
              triggerChange(null);
              triggerOpen(false);
            },
            "class": `${prefixCls}-clear`,
            "role": "button"
          }, [clearIcon || createVNode("span", {
            "class": `${prefixCls}-clear-btn`
          }, null)]);
        }
        const mergedInputProps = _extends(_extends(_extends(_extends({
          id,
          tabindex,
          disabled,
          readonly: inputReadOnly || typeof formatList.value[0] === "function" || !typing.value,
          value: hoverValue.value || text.value,
          onInput: (e) => {
            triggerTextChange(e.target.value);
          },
          autofocus,
          placeholder,
          ref: inputRef,
          title: text.value
        }, inputProps.value), {
          size: getInputSize(picker2, formatList.value[0], generateConfig2)
        }), getDataOrAriaProps(props)), {
          autocomplete
        });
        const inputNode = props.inputRender ? props.inputRender(mergedInputProps) : createVNode("input", mergedInputProps, null);
        if (true) {
          warning(!defaultOpenValue, "`defaultOpenValue` may confuse user for the current value status. Please use `defaultValue` instead.");
        }
        const popupPlacement = direction === "rtl" ? "bottomRight" : "bottomLeft";
        return createVNode("div", {
          "ref": containerRef,
          "class": classNames_default(prefixCls, attrs.class, {
            [`${prefixCls}-disabled`]: disabled,
            [`${prefixCls}-focused`]: focused.value,
            [`${prefixCls}-rtl`]: direction === "rtl"
          }),
          "style": attrs.style,
          "onMousedown": onMousedown,
          "onMouseup": onInternalMouseup,
          "onMouseenter": onMouseenter,
          "onMouseleave": onMouseleave,
          "onContextmenu": onContextmenu,
          "onClick": onClick
        }, [createVNode("div", {
          "class": classNames_default(`${prefixCls}-input`, {
            [`${prefixCls}-input-placeholder`]: !!hoverValue.value
          }),
          "ref": inputDivRef
        }, [inputNode, suffixNode, clearNode]), createVNode(PickerTrigger_default, {
          "visible": mergedOpen.value,
          "popupStyle": popupStyle,
          "prefixCls": prefixCls,
          "dropdownClassName": dropdownClassName,
          "dropdownAlign": dropdownAlign,
          "getPopupContainer": getPopupContainer,
          "transitionName": transitionName,
          "popupPlacement": popupPlacement,
          "direction": direction
        }, {
          default: () => [createVNode("div", {
            "style": {
              pointerEvents: "none",
              position: "absolute",
              top: 0,
              bottom: 0,
              left: 0,
              right: 0
            }
          }, null)],
          popupElement: () => panel
        })]);
      };
    }
  });
}
var Picker_default = Picker();

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/hooks/useRangeDisabled.js
function useRangeDisabled(_ref, openRecordsRef) {
  let {
    picker,
    locale,
    selectedValue,
    disabledDate,
    disabled,
    generateConfig: generateConfig2
  } = _ref;
  const startDate = computed(() => getValue(selectedValue.value, 0));
  const endDate = computed(() => getValue(selectedValue.value, 1));
  function weekFirstDate(date) {
    return generateConfig2.value.locale.getWeekFirstDate(locale.value.locale, date);
  }
  function monthNumber(date) {
    const year = generateConfig2.value.getYear(date);
    const month = generateConfig2.value.getMonth(date);
    return year * 100 + month;
  }
  function quarterNumber(date) {
    const year = generateConfig2.value.getYear(date);
    const quarter = getQuarter(generateConfig2.value, date);
    return year * 10 + quarter;
  }
  const disabledStartDate = (date) => {
    var _a2;
    if (disabledDate && ((_a2 = disabledDate === null || disabledDate === void 0 ? void 0 : disabledDate.value) === null || _a2 === void 0 ? void 0 : _a2.call(disabledDate, date))) {
      return true;
    }
    if (disabled[1] && endDate) {
      return !isSameDate(generateConfig2.value, date, endDate.value) && generateConfig2.value.isAfter(date, endDate.value);
    }
    if (openRecordsRef.value[1] && endDate.value) {
      switch (picker.value) {
        case "quarter":
          return quarterNumber(date) > quarterNumber(endDate.value);
        case "month":
          return monthNumber(date) > monthNumber(endDate.value);
        case "week":
          return weekFirstDate(date) > weekFirstDate(endDate.value);
        default:
          return !isSameDate(generateConfig2.value, date, endDate.value) && generateConfig2.value.isAfter(date, endDate.value);
      }
    }
    return false;
  };
  const disabledEndDate = (date) => {
    var _a2;
    if ((_a2 = disabledDate.value) === null || _a2 === void 0 ? void 0 : _a2.call(disabledDate, date)) {
      return true;
    }
    if (disabled[0] && startDate) {
      return !isSameDate(generateConfig2.value, date, endDate.value) && generateConfig2.value.isAfter(startDate.value, date);
    }
    if (openRecordsRef.value[0] && startDate.value) {
      switch (picker.value) {
        case "quarter":
          return quarterNumber(date) < quarterNumber(startDate.value);
        case "month":
          return monthNumber(date) < monthNumber(startDate.value);
        case "week":
          return weekFirstDate(date) < weekFirstDate(startDate.value);
        default:
          return !isSameDate(generateConfig2.value, date, startDate.value) && generateConfig2.value.isAfter(startDate.value, date);
      }
    }
    return false;
  };
  return [disabledStartDate, disabledEndDate];
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/hooks/useRangeViewDates.js
function getStartEndDistance(startDate, endDate, picker, generateConfig2) {
  const startNext = getClosingViewDate(startDate, picker, generateConfig2, 1);
  function getDistance(compareFunc) {
    if (compareFunc(startDate, endDate)) {
      return "same";
    }
    if (compareFunc(startNext, endDate)) {
      return "closing";
    }
    return "far";
  }
  switch (picker) {
    case "year":
      return getDistance((start, end) => isSameDecade(generateConfig2, start, end));
    case "quarter":
    case "month":
      return getDistance((start, end) => isSameYear(generateConfig2, start, end));
    default:
      return getDistance((start, end) => isSameMonth(generateConfig2, start, end));
  }
}
function getRangeViewDate(values, index, picker, generateConfig2) {
  const startDate = getValue(values, 0);
  const endDate = getValue(values, 1);
  if (index === 0) {
    return startDate;
  }
  if (startDate && endDate) {
    const distance = getStartEndDistance(startDate, endDate, picker, generateConfig2);
    switch (distance) {
      case "same":
        return startDate;
      case "closing":
        return startDate;
      default:
        return getClosingViewDate(endDate, picker, generateConfig2, -1);
    }
  }
  return startDate;
}
function useRangeViewDates(_ref) {
  let {
    values,
    picker,
    defaultDates,
    generateConfig: generateConfig2
  } = _ref;
  const defaultViewDates = ref([getValue(defaultDates, 0), getValue(defaultDates, 1)]);
  const viewDates = ref(null);
  const startDate = computed(() => getValue(values.value, 0));
  const endDate = computed(() => getValue(values.value, 1));
  const getViewDate = (index) => {
    if (defaultViewDates.value[index]) {
      return defaultViewDates.value[index];
    }
    return getValue(viewDates.value, index) || getRangeViewDate(values.value, index, picker.value, generateConfig2.value) || startDate.value || endDate.value || generateConfig2.value.getNow();
  };
  const startViewDate = ref(null);
  const endViewDate = ref(null);
  watchEffect(() => {
    startViewDate.value = getViewDate(0);
    endViewDate.value = getViewDate(1);
  });
  function setViewDate(viewDate, index) {
    if (viewDate) {
      let newViewDates = updateValues(viewDates.value, viewDate, index);
      defaultViewDates.value = updateValues(defaultViewDates.value, null, index) || [null, null];
      const anotherIndex = (index + 1) % 2;
      if (!getValue(values.value, anotherIndex)) {
        newViewDates = updateValues(newViewDates, viewDate, anotherIndex);
      }
      viewDates.value = newViewDates;
    } else if (startDate.value || endDate.value) {
      viewDates.value = null;
    }
  }
  return [startViewDate, endViewDate, setViewDate];
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/_vueuse/tryOnScopeDispose.js
function tryOnScopeDispose(fn) {
  if (getCurrentScope()) {
    onScopeDispose(fn);
    return true;
  }
  return false;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/_vueuse/resolveUnref.js
function resolveUnref(r) {
  return typeof r === "function" ? r() : unref(r);
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/_vueuse/unrefElement.js
function unrefElement(elRef) {
  var _a2;
  const plain = resolveUnref(elRef);
  return (_a2 = plain === null || plain === void 0 ? void 0 : plain.$el) !== null && _a2 !== void 0 ? _a2 : plain;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/_vueuse/tryOnMounted.js
function tryOnMounted(fn) {
  let sync = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;
  if (getCurrentInstance()) onMounted(fn);
  else if (sync) fn();
  else nextTick(fn);
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/_vueuse/useSupported.js
function useSupported(callback) {
  let sync = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
  const isSupported = shallowRef();
  const update = () => isSupported.value = Boolean(callback());
  update();
  tryOnMounted(update, sync);
  return isSupported;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/_vueuse/is.js
var _a;
var isClient = typeof window !== "undefined";
var isIOS = isClient && ((_a = window === null || window === void 0 ? void 0 : window.navigator) === null || _a === void 0 ? void 0 : _a.userAgent) && /iP(ad|hone|od)/.test(window.navigator.userAgent);

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/_vueuse/_configurable.js
var defaultWindow = isClient ? window : void 0;
var defaultDocument = isClient ? window.document : void 0;
var defaultNavigator = isClient ? window.navigator : void 0;
var defaultLocation = isClient ? window.location : void 0;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/_vueuse/useResizeObserver.js
var __rest = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
function useResizeObserver(target, callback) {
  let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
  const {
    window: window2 = defaultWindow
  } = options, observerOptions = __rest(options, ["window"]);
  let observer;
  const isSupported = useSupported(() => window2 && "ResizeObserver" in window2);
  const cleanup = () => {
    if (observer) {
      observer.disconnect();
      observer = void 0;
    }
  };
  const stopWatch = watch(() => unrefElement(target), (el) => {
    cleanup();
    if (isSupported.value && window2 && el) {
      observer = new ResizeObserver(callback);
      observer.observe(el, observerOptions);
    }
  }, {
    immediate: true,
    flush: "post"
  });
  const stop = () => {
    cleanup();
    stopWatch();
  };
  tryOnScopeDispose(stop);
  return {
    isSupported,
    stop
  };
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/_util/hooks/_vueuse/useElementSize.js
function useElementSize(target) {
  let initialSize = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {
    width: 0,
    height: 0
  };
  let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};
  const {
    box = "content-box"
  } = options;
  const width = shallowRef(initialSize.width);
  const height = shallowRef(initialSize.height);
  useResizeObserver(target, (_ref) => {
    let [entry] = _ref;
    const boxSize = box === "border-box" ? entry.borderBoxSize : box === "content-box" ? entry.contentBoxSize : entry.devicePixelContentBoxSize;
    if (boxSize) {
      width.value = boxSize.reduce((acc, _ref2) => {
        let {
          inlineSize
        } = _ref2;
        return acc + inlineSize;
      }, 0);
      height.value = boxSize.reduce((acc, _ref3) => {
        let {
          blockSize
        } = _ref3;
        return acc + blockSize;
      }, 0);
    } else {
      width.value = entry.contentRect.width;
      height.value = entry.contentRect.height;
    }
  }, options);
  watch(() => unrefElement(target), (ele) => {
    width.value = ele ? initialSize.width : 0;
    height.value = ele ? initialSize.height : 0;
  });
  return {
    width,
    height
  };
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/RangePicker.js
function reorderValues(values, generateConfig2) {
  if (values && values[0] && values[1] && generateConfig2.isAfter(values[0], values[1])) {
    return [values[1], values[0]];
  }
  return values;
}
function canValueTrigger(value, index, disabled, allowEmpty) {
  if (value) {
    return true;
  }
  if (allowEmpty && allowEmpty[index]) {
    return true;
  }
  if (disabled[(index + 1) % 2]) {
    return true;
  }
  return false;
}
function RangerPicker() {
  return defineComponent({
    name: "RangerPicker",
    inheritAttrs: false,
    props: ["prefixCls", "id", "popupStyle", "dropdownClassName", "transitionName", "dropdownAlign", "getPopupContainer", "generateConfig", "locale", "placeholder", "autofocus", "disabled", "format", "picker", "showTime", "showNow", "showHour", "showMinute", "showSecond", "use12Hours", "separator", "value", "defaultValue", "defaultPickerValue", "open", "defaultOpen", "disabledDate", "disabledTime", "dateRender", "panelRender", "ranges", "allowEmpty", "allowClear", "suffixIcon", "clearIcon", "pickerRef", "inputReadOnly", "mode", "renderExtraFooter", "onChange", "onOpenChange", "onPanelChange", "onCalendarChange", "onFocus", "onBlur", "onMousedown", "onMouseup", "onMouseenter", "onMouseleave", "onClick", "onOk", "onKeydown", "components", "order", "direction", "activePickerIndex", "autocomplete", "minuteStep", "hourStep", "secondStep", "hideDisabledOptions", "disabledMinutes", "presets", "prevIcon", "nextIcon", "superPrevIcon", "superNextIcon"],
    setup(props, _ref) {
      let {
        attrs,
        expose
      } = _ref;
      const needConfirmButton = computed(() => props.picker === "date" && !!props.showTime || props.picker === "time");
      const presets = computed(() => props.presets);
      const ranges = computed(() => props.ranges);
      const presetList = usePresets(presets, ranges);
      const openRecordsRef = ref({});
      const containerRef = ref(null);
      const panelDivRef = ref(null);
      const startInputDivRef = ref(null);
      const endInputDivRef = ref(null);
      const separatorRef = ref(null);
      const startInputRef = ref(null);
      const endInputRef = ref(null);
      const arrowRef = ref(null);
      if (true) {
        legacyPropsWarning(props);
      }
      const formatList = computed(() => toArray(getDefaultFormat(props.format, props.picker, props.showTime, props.use12Hours)));
      const [mergedActivePickerIndex, setMergedActivePickerIndex] = useMergedState(0, {
        value: toRef(props, "activePickerIndex")
      });
      const operationRef = ref(null);
      const mergedDisabled = computed(() => {
        const {
          disabled
        } = props;
        if (Array.isArray(disabled)) {
          return disabled;
        }
        return [disabled || false, disabled || false];
      });
      const [mergedValue, setInnerValue] = useMergedState(null, {
        value: toRef(props, "value"),
        defaultValue: props.defaultValue,
        postState: (values) => props.picker === "time" && !props.order ? values : reorderValues(values, props.generateConfig)
      });
      const [startViewDate, endViewDate, setViewDate] = useRangeViewDates({
        values: mergedValue,
        picker: toRef(props, "picker"),
        defaultDates: props.defaultPickerValue,
        generateConfig: toRef(props, "generateConfig")
      });
      const [selectedValue, setSelectedValue] = useMergedState(mergedValue.value, {
        postState: (values) => {
          let postValues = values;
          if (mergedDisabled.value[0] && mergedDisabled.value[1]) {
            return postValues;
          }
          for (let i = 0; i < 2; i += 1) {
            if (mergedDisabled.value[i] && !getValue(postValues, i) && !getValue(props.allowEmpty, i)) {
              postValues = updateValues(postValues, props.generateConfig.getNow(), i);
            }
          }
          return postValues;
        }
      });
      const [mergedModes, setInnerModes] = useMergedState([props.picker, props.picker], {
        value: toRef(props, "mode")
      });
      watch(() => props.picker, () => {
        setInnerModes([props.picker, props.picker]);
      });
      const triggerModesChange = (modes, values) => {
        var _a2;
        setInnerModes(modes);
        (_a2 = props.onPanelChange) === null || _a2 === void 0 ? void 0 : _a2.call(props, values, modes);
      };
      const [disabledStartDate, disabledEndDate] = useRangeDisabled({
        picker: toRef(props, "picker"),
        selectedValue,
        locale: toRef(props, "locale"),
        disabled: mergedDisabled,
        disabledDate: toRef(props, "disabledDate"),
        generateConfig: toRef(props, "generateConfig")
      }, openRecordsRef);
      const [mergedOpen, triggerInnerOpen] = useMergedState(false, {
        value: toRef(props, "open"),
        defaultValue: props.defaultOpen,
        postState: (postOpen) => mergedDisabled.value[mergedActivePickerIndex.value] ? false : postOpen,
        onChange: (newOpen) => {
          var _a2;
          (_a2 = props.onOpenChange) === null || _a2 === void 0 ? void 0 : _a2.call(props, newOpen);
          if (!newOpen && operationRef.value && operationRef.value.onClose) {
            operationRef.value.onClose();
          }
        }
      });
      const startOpen = computed(() => mergedOpen.value && mergedActivePickerIndex.value === 0);
      const endOpen = computed(() => mergedOpen.value && mergedActivePickerIndex.value === 1);
      const panelLeft = ref(0);
      const arrowLeft = ref(0);
      const popupMinWidth = ref(0);
      const {
        width: containerWidth
      } = useElementSize(containerRef);
      watch([mergedOpen, containerWidth], () => {
        if (!mergedOpen.value && containerRef.value) {
          popupMinWidth.value = containerWidth.value;
        }
      });
      const {
        width: panelDivWidth
      } = useElementSize(panelDivRef);
      const {
        width: arrowWidth
      } = useElementSize(arrowRef);
      const {
        width: startInputDivWidth
      } = useElementSize(startInputDivRef);
      const {
        width: separatorWidth
      } = useElementSize(separatorRef);
      watch([mergedActivePickerIndex, mergedOpen, panelDivWidth, arrowWidth, startInputDivWidth, separatorWidth, () => props.direction], () => {
        arrowLeft.value = 0;
        if (mergedActivePickerIndex.value) {
          if (startInputDivRef.value && separatorRef.value) {
            arrowLeft.value = startInputDivWidth.value + separatorWidth.value;
            if (panelDivWidth.value && arrowWidth.value && arrowLeft.value > panelDivWidth.value - arrowWidth.value - (props.direction === "rtl" || arrowRef.value.offsetLeft > arrowLeft.value ? 0 : arrowRef.value.offsetLeft)) {
              panelLeft.value = arrowLeft.value;
            }
          }
        } else if (mergedActivePickerIndex.value === 0) {
          panelLeft.value = 0;
        }
      }, {
        immediate: true
      });
      const triggerRef = ref();
      function triggerOpen(newOpen, index) {
        if (newOpen) {
          clearTimeout(triggerRef.value);
          openRecordsRef.value[index] = true;
          setMergedActivePickerIndex(index);
          triggerInnerOpen(newOpen);
          if (!mergedOpen.value) {
            setViewDate(null, index);
          }
        } else if (mergedActivePickerIndex.value === index) {
          triggerInnerOpen(newOpen);
          const openRecords = openRecordsRef.value;
          triggerRef.value = setTimeout(() => {
            if (openRecords === openRecordsRef.value) {
              openRecordsRef.value = {};
            }
          });
        }
      }
      function triggerOpenAndFocus(index) {
        triggerOpen(true, index);
        setTimeout(() => {
          const inputRef = [startInputRef, endInputRef][index];
          if (inputRef.value) {
            inputRef.value.focus();
          }
        }, 0);
      }
      function triggerChange(newValue, sourceIndex) {
        let values = newValue;
        let startValue = getValue(values, 0);
        let endValue = getValue(values, 1);
        const {
          generateConfig: generateConfig2,
          locale,
          picker,
          order,
          onCalendarChange,
          allowEmpty,
          onChange,
          showTime
        } = props;
        if (startValue && endValue && generateConfig2.isAfter(startValue, endValue)) {
          if (
            // WeekPicker only compare week
            picker === "week" && !isSameWeek(generateConfig2, locale.locale, startValue, endValue) || // QuotaPicker only compare week
            picker === "quarter" && !isSameQuarter(generateConfig2, startValue, endValue) || // Other non-TimePicker compare date
            picker !== "week" && picker !== "quarter" && picker !== "time" && !(showTime ? isEqual(generateConfig2, startValue, endValue) : isSameDate(generateConfig2, startValue, endValue))
          ) {
            if (sourceIndex === 0) {
              values = [startValue, null];
              endValue = null;
            } else {
              startValue = null;
              values = [null, endValue];
            }
            openRecordsRef.value = {
              [sourceIndex]: true
            };
          } else if (picker !== "time" || order !== false) {
            values = reorderValues(values, generateConfig2);
          }
        }
        setSelectedValue(values);
        const startStr2 = values && values[0] ? formatValue(values[0], {
          generateConfig: generateConfig2,
          locale,
          format: formatList.value[0]
        }) : "";
        const endStr2 = values && values[1] ? formatValue(values[1], {
          generateConfig: generateConfig2,
          locale,
          format: formatList.value[0]
        }) : "";
        if (onCalendarChange) {
          const info = {
            range: sourceIndex === 0 ? "start" : "end"
          };
          onCalendarChange(values, [startStr2, endStr2], info);
        }
        const canStartValueTrigger = canValueTrigger(startValue, 0, mergedDisabled.value, allowEmpty);
        const canEndValueTrigger = canValueTrigger(endValue, 1, mergedDisabled.value, allowEmpty);
        const canTrigger = values === null || canStartValueTrigger && canEndValueTrigger;
        if (canTrigger) {
          setInnerValue(values);
          if (onChange && (!isEqual(generateConfig2, getValue(mergedValue.value, 0), startValue) || !isEqual(generateConfig2, getValue(mergedValue.value, 1), endValue))) {
            onChange(values, [startStr2, endStr2]);
          }
        }
        let nextOpenIndex = null;
        if (sourceIndex === 0 && !mergedDisabled.value[1]) {
          nextOpenIndex = 1;
        } else if (sourceIndex === 1 && !mergedDisabled.value[0]) {
          nextOpenIndex = 0;
        }
        if (nextOpenIndex !== null && nextOpenIndex !== mergedActivePickerIndex.value && (!openRecordsRef.value[nextOpenIndex] || !getValue(values, nextOpenIndex)) && getValue(values, sourceIndex)) {
          triggerOpenAndFocus(nextOpenIndex);
        } else {
          triggerOpen(false, sourceIndex);
        }
      }
      const forwardKeydown = (e) => {
        if (mergedOpen && operationRef.value && operationRef.value.onKeydown) {
          return operationRef.value.onKeydown(e);
        }
        {
          warning(false, "Picker not correct forward Keydown operation. Please help to fire issue about this.");
          return false;
        }
      };
      const sharedTextHooksProps = {
        formatList,
        generateConfig: toRef(props, "generateConfig"),
        locale: toRef(props, "locale")
      };
      const [startValueTexts, firstStartValueText] = useValueTexts(computed(() => getValue(selectedValue.value, 0)), sharedTextHooksProps);
      const [endValueTexts, firstEndValueText] = useValueTexts(computed(() => getValue(selectedValue.value, 1)), sharedTextHooksProps);
      const onTextChange = (newText, index) => {
        const inputDate = parseValue(newText, {
          locale: props.locale,
          formatList: formatList.value,
          generateConfig: props.generateConfig
        });
        const disabledFunc = index === 0 ? disabledStartDate : disabledEndDate;
        if (inputDate && !disabledFunc(inputDate)) {
          setSelectedValue(updateValues(selectedValue.value, inputDate, index));
          setViewDate(inputDate, index);
        }
      };
      const [startText, triggerStartTextChange, resetStartText] = useTextValueMapping({
        valueTexts: startValueTexts,
        onTextChange: (newText) => onTextChange(newText, 0)
      });
      const [endText, triggerEndTextChange, resetEndText] = useTextValueMapping({
        valueTexts: endValueTexts,
        onTextChange: (newText) => onTextChange(newText, 1)
      });
      const [rangeHoverValue, setRangeHoverValue] = useState(null);
      const [hoverRangedValue, setHoverRangedValue] = useState(null);
      const [startHoverValue, onStartEnter, onStartLeave] = useHoverValue(startText, sharedTextHooksProps);
      const [endHoverValue, onEndEnter, onEndLeave] = useHoverValue(endText, sharedTextHooksProps);
      const onDateMouseenter = (date) => {
        setHoverRangedValue(updateValues(selectedValue.value, date, mergedActivePickerIndex.value));
        if (mergedActivePickerIndex.value === 0) {
          onStartEnter(date);
        } else {
          onEndEnter(date);
        }
      };
      const onDateMouseleave = () => {
        setHoverRangedValue(updateValues(selectedValue.value, null, mergedActivePickerIndex.value));
        if (mergedActivePickerIndex.value === 0) {
          onStartLeave();
        } else {
          onEndLeave();
        }
      };
      const getSharedInputHookProps = (index, resetText) => ({
        forwardKeydown,
        onBlur: (e) => {
          var _a2;
          (_a2 = props.onBlur) === null || _a2 === void 0 ? void 0 : _a2.call(props, e);
        },
        isClickOutside: (target) => !elementsContains([panelDivRef.value, startInputDivRef.value, endInputDivRef.value, containerRef.value], target),
        onFocus: (e) => {
          var _a2;
          setMergedActivePickerIndex(index);
          (_a2 = props.onFocus) === null || _a2 === void 0 ? void 0 : _a2.call(props, e);
        },
        triggerOpen: (newOpen) => {
          triggerOpen(newOpen, index);
        },
        onSubmit: () => {
          if (
            // When user typing disabledDate with keyboard and enter, this value will be empty
            !selectedValue.value || // Normal disabled check
            props.disabledDate && props.disabledDate(selectedValue.value[index])
          ) {
            return false;
          }
          triggerChange(selectedValue.value, index);
          resetText();
        },
        onCancel: () => {
          triggerOpen(false, index);
          setSelectedValue(mergedValue.value);
          resetText();
        }
      });
      const [startInputProps, {
        focused: startFocused,
        typing: startTyping
      }] = usePickerInput(_extends(_extends({}, getSharedInputHookProps(0, resetStartText)), {
        blurToCancel: needConfirmButton,
        open: startOpen,
        value: startText,
        onKeydown: (e, preventDefault) => {
          var _a2;
          (_a2 = props.onKeydown) === null || _a2 === void 0 ? void 0 : _a2.call(props, e, preventDefault);
        }
      }));
      const [endInputProps, {
        focused: endFocused,
        typing: endTyping
      }] = usePickerInput(_extends(_extends({}, getSharedInputHookProps(1, resetEndText)), {
        blurToCancel: needConfirmButton,
        open: endOpen,
        value: endText,
        onKeydown: (e, preventDefault) => {
          var _a2;
          (_a2 = props.onKeydown) === null || _a2 === void 0 ? void 0 : _a2.call(props, e, preventDefault);
        }
      }));
      const onPickerClick = (e) => {
        var _a2;
        (_a2 = props.onClick) === null || _a2 === void 0 ? void 0 : _a2.call(props, e);
        if (!mergedOpen.value && !startInputRef.value.contains(e.target) && !endInputRef.value.contains(e.target)) {
          if (!mergedDisabled.value[0]) {
            triggerOpenAndFocus(0);
          } else if (!mergedDisabled.value[1]) {
            triggerOpenAndFocus(1);
          }
        }
      };
      const onPickerMousedown = (e) => {
        var _a2;
        (_a2 = props.onMousedown) === null || _a2 === void 0 ? void 0 : _a2.call(props, e);
        if (mergedOpen.value && (startFocused.value || endFocused.value) && !startInputRef.value.contains(e.target) && !endInputRef.value.contains(e.target)) {
          e.preventDefault();
        }
      };
      const startStr = computed(() => {
        var _a2;
        return ((_a2 = mergedValue.value) === null || _a2 === void 0 ? void 0 : _a2[0]) ? formatValue(mergedValue.value[0], {
          locale: props.locale,
          format: "YYYYMMDDHHmmss",
          generateConfig: props.generateConfig
        }) : "";
      });
      const endStr = computed(() => {
        var _a2;
        return ((_a2 = mergedValue.value) === null || _a2 === void 0 ? void 0 : _a2[1]) ? formatValue(mergedValue.value[1], {
          locale: props.locale,
          format: "YYYYMMDDHHmmss",
          generateConfig: props.generateConfig
        }) : "";
      });
      watch([mergedOpen, startValueTexts, endValueTexts], () => {
        if (!mergedOpen.value) {
          setSelectedValue(mergedValue.value);
          if (!startValueTexts.value.length || startValueTexts.value[0] === "") {
            triggerStartTextChange("");
          } else if (firstStartValueText.value !== startText.value) {
            resetStartText();
          }
          if (!endValueTexts.value.length || endValueTexts.value[0] === "") {
            triggerEndTextChange("");
          } else if (firstEndValueText.value !== endText.value) {
            resetEndText();
          }
        }
      });
      watch([startStr, endStr], () => {
        setSelectedValue(mergedValue.value);
      });
      if (true) {
        watchEffect(() => {
          const {
            value,
            disabled
          } = props;
          if (value && Array.isArray(disabled) && (getValue(disabled, 0) && !getValue(value, 0) || getValue(disabled, 1) && !getValue(value, 1))) {
            warning(false, "`disabled` should not set with empty `value`. You should set `allowEmpty` or `value` instead.");
          }
        });
      }
      expose({
        focus: () => {
          if (startInputRef.value) {
            startInputRef.value.focus();
          }
        },
        blur: () => {
          if (startInputRef.value) {
            startInputRef.value.blur();
          }
          if (endInputRef.value) {
            endInputRef.value.blur();
          }
        }
      });
      const panelHoverRangedValue = computed(() => {
        if (mergedOpen.value && hoverRangedValue.value && hoverRangedValue.value[0] && hoverRangedValue.value[1] && props.generateConfig.isAfter(hoverRangedValue.value[1], hoverRangedValue.value[0])) {
          return hoverRangedValue.value;
        } else {
          return null;
        }
      });
      function renderPanel() {
        let panelPosition = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;
        let panelProps = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        const {
          generateConfig: generateConfig2,
          showTime,
          dateRender,
          direction,
          disabledTime,
          prefixCls,
          locale
        } = props;
        let panelShowTime = showTime;
        if (showTime && typeof showTime === "object" && showTime.defaultValue) {
          const timeDefaultValues = showTime.defaultValue;
          panelShowTime = _extends(_extends({}, showTime), {
            defaultValue: getValue(timeDefaultValues, mergedActivePickerIndex.value) || void 0
          });
        }
        let panelDateRender = null;
        if (dateRender) {
          panelDateRender = (_ref2) => {
            let {
              current: date,
              today
            } = _ref2;
            return dateRender({
              current: date,
              today,
              info: {
                range: mergedActivePickerIndex.value ? "end" : "start"
              }
            });
          };
        }
        return createVNode(RangeContextProvider, {
          "value": {
            inRange: true,
            panelPosition,
            rangedValue: rangeHoverValue.value || selectedValue.value,
            hoverRangedValue: panelHoverRangedValue.value
          }
        }, {
          default: () => [createVNode(PickerPanel_default, _objectSpread2(_objectSpread2(_objectSpread2({}, props), panelProps), {}, {
            "dateRender": panelDateRender,
            "showTime": panelShowTime,
            "mode": mergedModes.value[mergedActivePickerIndex.value],
            "generateConfig": generateConfig2,
            "style": void 0,
            "direction": direction,
            "disabledDate": mergedActivePickerIndex.value === 0 ? disabledStartDate : disabledEndDate,
            "disabledTime": (date) => {
              if (disabledTime) {
                return disabledTime(date, mergedActivePickerIndex.value === 0 ? "start" : "end");
              }
              return false;
            },
            "class": classNames_default({
              [`${prefixCls}-panel-focused`]: mergedActivePickerIndex.value === 0 ? !startTyping.value : !endTyping.value
            }),
            "value": getValue(selectedValue.value, mergedActivePickerIndex.value),
            "locale": locale,
            "tabIndex": -1,
            "onPanelChange": (date, newMode) => {
              if (mergedActivePickerIndex.value === 0) {
                onStartLeave(true);
              }
              if (mergedActivePickerIndex.value === 1) {
                onEndLeave(true);
              }
              triggerModesChange(updateValues(mergedModes.value, newMode, mergedActivePickerIndex.value), updateValues(selectedValue.value, date, mergedActivePickerIndex.value));
              let viewDate = date;
              if (panelPosition === "right" && mergedModes.value[mergedActivePickerIndex.value] === newMode) {
                viewDate = getClosingViewDate(viewDate, newMode, generateConfig2, -1);
              }
              setViewDate(viewDate, mergedActivePickerIndex.value);
            },
            "onOk": null,
            "onSelect": void 0,
            "onChange": void 0,
            "defaultValue": mergedActivePickerIndex.value === 0 ? getValue(selectedValue.value, 1) : getValue(selectedValue.value, 0)
          }), null)]
        });
      }
      const onContextSelect = (date, type) => {
        const values = updateValues(selectedValue.value, date, mergedActivePickerIndex.value);
        if (type === "submit" || type !== "key" && !needConfirmButton.value) {
          triggerChange(values, mergedActivePickerIndex.value);
          if (mergedActivePickerIndex.value === 0) {
            onStartLeave();
          } else {
            onEndLeave();
          }
        } else {
          setSelectedValue(values);
        }
      };
      useProvidePanel({
        operationRef,
        hideHeader: computed(() => props.picker === "time"),
        onDateMouseenter,
        onDateMouseleave,
        hideRanges: computed(() => true),
        onSelect: onContextSelect,
        open: mergedOpen
      });
      return () => {
        const {
          prefixCls = "rc-picker",
          id,
          popupStyle,
          dropdownClassName,
          transitionName,
          dropdownAlign,
          getPopupContainer,
          generateConfig: generateConfig2,
          locale,
          placeholder,
          autofocus,
          picker = "date",
          showTime,
          separator = "~",
          disabledDate,
          panelRender,
          allowClear,
          suffixIcon,
          clearIcon,
          inputReadOnly,
          renderExtraFooter,
          onMouseenter,
          onMouseleave,
          onMouseup,
          onOk,
          components,
          direction,
          autocomplete = "off"
        } = props;
        const arrowPositionStyle = direction === "rtl" ? {
          right: `${arrowLeft.value}px`
        } : {
          left: `${arrowLeft.value}px`
        };
        function renderPanels() {
          let panels;
          const extraNode = getExtraFooter(prefixCls, mergedModes.value[mergedActivePickerIndex.value], renderExtraFooter);
          const rangesNode = getRanges({
            prefixCls,
            components,
            needConfirmButton: needConfirmButton.value,
            okDisabled: !getValue(selectedValue.value, mergedActivePickerIndex.value) || disabledDate && disabledDate(selectedValue.value[mergedActivePickerIndex.value]),
            locale,
            onOk: () => {
              if (getValue(selectedValue.value, mergedActivePickerIndex.value)) {
                triggerChange(selectedValue.value, mergedActivePickerIndex.value);
                if (onOk) {
                  onOk(selectedValue.value);
                }
              }
            }
          });
          if (picker !== "time" && !showTime) {
            const viewDate = mergedActivePickerIndex.value === 0 ? startViewDate.value : endViewDate.value;
            const nextViewDate = getClosingViewDate(viewDate, picker, generateConfig2);
            const currentMode = mergedModes.value[mergedActivePickerIndex.value];
            const showDoublePanel = currentMode === picker;
            const leftPanel = renderPanel(showDoublePanel ? "left" : false, {
              pickerValue: viewDate,
              onPickerValueChange: (newViewDate) => {
                setViewDate(newViewDate, mergedActivePickerIndex.value);
              }
            });
            const rightPanel = renderPanel("right", {
              pickerValue: nextViewDate,
              onPickerValueChange: (newViewDate) => {
                setViewDate(getClosingViewDate(newViewDate, picker, generateConfig2, -1), mergedActivePickerIndex.value);
              }
            });
            if (direction === "rtl") {
              panels = createVNode(Fragment, null, [rightPanel, showDoublePanel && leftPanel]);
            } else {
              panels = createVNode(Fragment, null, [leftPanel, showDoublePanel && rightPanel]);
            }
          } else {
            panels = renderPanel();
          }
          let mergedNodes = createVNode("div", {
            "class": `${prefixCls}-panel-layout`
          }, [createVNode(PresetPanel_default, {
            "prefixCls": prefixCls,
            "presets": presetList.value,
            "onClick": (nextValue) => {
              triggerChange(nextValue, null);
              triggerOpen(false, mergedActivePickerIndex.value);
            },
            "onHover": (hoverValue) => {
              setRangeHoverValue(hoverValue);
            }
          }, null), createVNode("div", null, [createVNode("div", {
            "class": `${prefixCls}-panels`
          }, [panels]), (extraNode || rangesNode) && createVNode("div", {
            "class": `${prefixCls}-footer`
          }, [extraNode, rangesNode])])]);
          if (panelRender) {
            mergedNodes = panelRender(mergedNodes);
          }
          return createVNode("div", {
            "class": `${prefixCls}-panel-container`,
            "style": {
              marginLeft: `${panelLeft.value}px`
            },
            "ref": panelDivRef,
            "onMousedown": (e) => {
              e.preventDefault();
            }
          }, [mergedNodes]);
        }
        const rangePanel = createVNode("div", {
          "class": classNames_default(`${prefixCls}-range-wrapper`, `${prefixCls}-${picker}-range-wrapper`),
          "style": {
            minWidth: `${popupMinWidth.value}px`
          }
        }, [createVNode("div", {
          "ref": arrowRef,
          "class": `${prefixCls}-range-arrow`,
          "style": arrowPositionStyle
        }, null), renderPanels()]);
        let suffixNode;
        if (suffixIcon) {
          suffixNode = createVNode("span", {
            "class": `${prefixCls}-suffix`
          }, [suffixIcon]);
        }
        let clearNode;
        if (allowClear && (getValue(mergedValue.value, 0) && !mergedDisabled.value[0] || getValue(mergedValue.value, 1) && !mergedDisabled.value[1])) {
          clearNode = createVNode("span", {
            "onMousedown": (e) => {
              e.preventDefault();
              e.stopPropagation();
            },
            "onMouseup": (e) => {
              e.preventDefault();
              e.stopPropagation();
              let values = mergedValue.value;
              if (!mergedDisabled.value[0]) {
                values = updateValues(values, null, 0);
              }
              if (!mergedDisabled.value[1]) {
                values = updateValues(values, null, 1);
              }
              triggerChange(values, null);
              triggerOpen(false, mergedActivePickerIndex.value);
            },
            "class": `${prefixCls}-clear`
          }, [clearIcon || createVNode("span", {
            "class": `${prefixCls}-clear-btn`
          }, null)]);
        }
        const inputSharedProps = {
          size: getInputSize(picker, formatList.value[0], generateConfig2)
        };
        let activeBarLeft = 0;
        let activeBarWidth = 0;
        if (startInputDivRef.value && endInputDivRef.value && separatorRef.value) {
          if (mergedActivePickerIndex.value === 0) {
            activeBarWidth = startInputDivRef.value.offsetWidth;
          } else {
            activeBarLeft = arrowLeft.value;
            activeBarWidth = endInputDivRef.value.offsetWidth;
          }
        }
        const activeBarPositionStyle = direction === "rtl" ? {
          right: `${activeBarLeft}px`
        } : {
          left: `${activeBarLeft}px`
        };
        return createVNode("div", _objectSpread2({
          "ref": containerRef,
          "class": classNames_default(prefixCls, `${prefixCls}-range`, attrs.class, {
            [`${prefixCls}-disabled`]: mergedDisabled.value[0] && mergedDisabled.value[1],
            [`${prefixCls}-focused`]: mergedActivePickerIndex.value === 0 ? startFocused.value : endFocused.value,
            [`${prefixCls}-rtl`]: direction === "rtl"
          }),
          "style": attrs.style,
          "onClick": onPickerClick,
          "onMouseenter": onMouseenter,
          "onMouseleave": onMouseleave,
          "onMousedown": onPickerMousedown,
          "onMouseup": onMouseup
        }, getDataOrAriaProps(props)), [createVNode("div", {
          "class": classNames_default(`${prefixCls}-input`, {
            [`${prefixCls}-input-active`]: mergedActivePickerIndex.value === 0,
            [`${prefixCls}-input-placeholder`]: !!startHoverValue.value
          }),
          "ref": startInputDivRef
        }, [createVNode("input", _objectSpread2(_objectSpread2(_objectSpread2({
          "id": id,
          "disabled": mergedDisabled.value[0],
          "readonly": inputReadOnly || typeof formatList.value[0] === "function" || !startTyping.value,
          "value": startHoverValue.value || startText.value,
          "onInput": (e) => {
            triggerStartTextChange(e.target.value);
          },
          "autofocus": autofocus,
          "placeholder": getValue(placeholder, 0) || "",
          "ref": startInputRef
        }, startInputProps.value), inputSharedProps), {}, {
          "autocomplete": autocomplete
        }), null)]), createVNode("div", {
          "class": `${prefixCls}-range-separator`,
          "ref": separatorRef
        }, [separator]), createVNode("div", {
          "class": classNames_default(`${prefixCls}-input`, {
            [`${prefixCls}-input-active`]: mergedActivePickerIndex.value === 1,
            [`${prefixCls}-input-placeholder`]: !!endHoverValue.value
          }),
          "ref": endInputDivRef
        }, [createVNode("input", _objectSpread2(_objectSpread2(_objectSpread2({
          "disabled": mergedDisabled.value[1],
          "readonly": inputReadOnly || typeof formatList.value[0] === "function" || !endTyping.value,
          "value": endHoverValue.value || endText.value,
          "onInput": (e) => {
            triggerEndTextChange(e.target.value);
          },
          "placeholder": getValue(placeholder, 1) || "",
          "ref": endInputRef
        }, endInputProps.value), inputSharedProps), {}, {
          "autocomplete": autocomplete
        }), null)]), createVNode("div", {
          "class": `${prefixCls}-active-bar`,
          "style": _extends(_extends({}, activeBarPositionStyle), {
            width: `${activeBarWidth}px`,
            position: "absolute"
          })
        }, null), suffixNode, clearNode, createVNode(PickerTrigger_default, {
          "visible": mergedOpen.value,
          "popupStyle": popupStyle,
          "prefixCls": prefixCls,
          "dropdownClassName": dropdownClassName,
          "dropdownAlign": dropdownAlign,
          "getPopupContainer": getPopupContainer,
          "transitionName": transitionName,
          "range": true,
          "direction": direction
        }, {
          default: () => [createVNode("div", {
            "style": {
              pointerEvents: "none",
              position: "absolute",
              top: 0,
              bottom: 0,
              left: 0,
              right: 0
            }
          }, null)],
          popupElement: () => rangePanel
        })]);
      };
    }
  });
}
var InterRangerPicker = RangerPicker();
var RangePicker_default = InterRangerPicker;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-picker/index.js
var vc_picker_default = Picker_default;

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/date-picker/util.js
function getPlaceholder(locale, picker, customizePlaceholder) {
  if (customizePlaceholder !== void 0) {
    return customizePlaceholder;
  }
  if (picker === "year" && locale.lang.yearPlaceholder) {
    return locale.lang.yearPlaceholder;
  }
  if (picker === "quarter" && locale.lang.quarterPlaceholder) {
    return locale.lang.quarterPlaceholder;
  }
  if (picker === "month" && locale.lang.monthPlaceholder) {
    return locale.lang.monthPlaceholder;
  }
  if (picker === "week" && locale.lang.weekPlaceholder) {
    return locale.lang.weekPlaceholder;
  }
  if (picker === "time" && locale.timePickerLocale.placeholder) {
    return locale.timePickerLocale.placeholder;
  }
  return locale.lang.placeholder;
}
function getRangePlaceholder(locale, picker, customizePlaceholder) {
  if (customizePlaceholder !== void 0) {
    return customizePlaceholder;
  }
  if (picker === "year" && locale.lang.yearPlaceholder) {
    return locale.lang.rangeYearPlaceholder;
  }
  if (picker === "month" && locale.lang.monthPlaceholder) {
    return locale.lang.rangeMonthPlaceholder;
  }
  if (picker === "week" && locale.lang.weekPlaceholder) {
    return locale.lang.rangeWeekPlaceholder;
  }
  if (picker === "time" && locale.timePickerLocale.placeholder) {
    return locale.timePickerLocale.rangePlaceholder;
  }
  return locale.lang.rangePlaceholder;
}
function transPlacement2DropdownAlign(direction, placement) {
  const overflow = {
    adjustX: 1,
    adjustY: 1
  };
  switch (placement) {
    case "bottomLeft": {
      return {
        points: ["tl", "bl"],
        offset: [0, 4],
        overflow
      };
    }
    case "bottomRight": {
      return {
        points: ["tr", "br"],
        offset: [0, 4],
        overflow
      };
    }
    case "topLeft": {
      return {
        points: ["bl", "tl"],
        offset: [0, -4],
        overflow
      };
    }
    case "topRight": {
      return {
        points: ["br", "tr"],
        offset: [0, -4],
        overflow
      };
    }
    default: {
      return {
        points: direction === "rtl" ? ["tr", "br"] : ["tl", "bl"],
        offset: [0, 4],
        overflow
      };
    }
  }
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/date-picker/generatePicker/props.js
function commonProps() {
  return {
    id: String,
    /**
     * @deprecated `dropdownClassName` is deprecated which will be removed in next major
     *   version.Please use `popupClassName` instead.
     */
    dropdownClassName: String,
    popupClassName: String,
    popupStyle: objectType(),
    transitionName: String,
    placeholder: String,
    allowClear: booleanType(),
    autofocus: booleanType(),
    disabled: booleanType(),
    tabindex: Number,
    open: booleanType(),
    defaultOpen: booleanType(),
    /** Make input readOnly to avoid popup keyboard in mobile */
    inputReadOnly: booleanType(),
    format: someType([String, Function, Array]),
    // Value
    // format:  string | CustomFormat<DateType> | (string | CustomFormat<DateType>)[];
    // Render
    // suffixIcon?: VueNode;
    // clearIcon?: VueNode;
    // prevIcon?: VueNode;
    // nextIcon?: VueNode;
    // superPrevIcon?: VueNode;
    // superNextIcon?: VueNode;
    getPopupContainer: functionType(),
    panelRender: functionType(),
    // // Events
    onChange: functionType(),
    "onUpdate:value": functionType(),
    onOk: functionType(),
    onOpenChange: functionType(),
    "onUpdate:open": functionType(),
    onFocus: functionType(),
    onBlur: functionType(),
    onMousedown: functionType(),
    onMouseup: functionType(),
    onMouseenter: functionType(),
    onMouseleave: functionType(),
    onClick: functionType(),
    onContextmenu: functionType(),
    onKeydown: functionType(),
    // WAI-ARIA
    role: String,
    name: String,
    autocomplete: String,
    direction: stringType(),
    showToday: booleanType(),
    showTime: someType([Boolean, Object]),
    locale: objectType(),
    size: stringType(),
    bordered: booleanType(),
    dateRender: functionType(),
    disabledDate: functionType(),
    mode: stringType(),
    picker: stringType(),
    valueFormat: String,
    placement: stringType(),
    status: stringType(),
    /** @deprecated Please use `disabledTime` instead. */
    disabledHours: functionType(),
    /** @deprecated Please use `disabledTime` instead. */
    disabledMinutes: functionType(),
    /** @deprecated Please use `disabledTime` instead. */
    disabledSeconds: functionType()
  };
}
function datePickerProps() {
  return {
    defaultPickerValue: someType([Object, String]),
    defaultValue: someType([Object, String]),
    value: someType([Object, String]),
    presets: arrayType(),
    disabledTime: functionType(),
    renderExtraFooter: functionType(),
    showNow: booleanType(),
    monthCellRender: functionType(),
    // deprecated  Please use `monthCellRender"` instead.',
    monthCellContentRender: functionType()
  };
}
function rangePickerProps() {
  return {
    allowEmpty: arrayType(),
    dateRender: functionType(),
    defaultPickerValue: arrayType(),
    defaultValue: arrayType(),
    value: arrayType(),
    presets: arrayType(),
    disabledTime: functionType(),
    disabled: someType([Boolean, Array]),
    renderExtraFooter: functionType(),
    separator: {
      type: String
    },
    showTime: someType([Boolean, Object]),
    ranges: objectType(),
    placeholder: arrayType(),
    mode: arrayType(),
    onChange: functionType(),
    "onUpdate:value": functionType(),
    onCalendarChange: functionType(),
    onPanelChange: functionType(),
    onOk: functionType()
  };
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/date-picker/style/index.js
var genPikerPadding = (token, inputHeight, fontSize, paddingHorizontal) => {
  const {
    lineHeight
  } = token;
  const fontHeight = Math.floor(fontSize * lineHeight) + 2;
  const paddingTop = Math.max((inputHeight - fontHeight) / 2, 0);
  const paddingBottom = Math.max(inputHeight - fontHeight - paddingTop, 0);
  return {
    padding: `${paddingTop}px ${paddingHorizontal}px ${paddingBottom}px`
  };
};
var genPickerCellInnerStyle = (token) => {
  const {
    componentCls,
    pickerCellCls,
    pickerCellInnerCls,
    pickerPanelCellHeight,
    motionDurationSlow,
    borderRadiusSM,
    motionDurationMid,
    controlItemBgHover,
    lineWidth,
    lineType,
    colorPrimary,
    controlItemBgActive,
    colorTextLightSolid,
    controlHeightSM,
    pickerDateHoverRangeBorderColor,
    pickerCellBorderGap,
    pickerBasicCellHoverWithRangeColor,
    pickerPanelCellWidth,
    colorTextDisabled,
    colorBgContainerDisabled
  } = token;
  return {
    "&::before": {
      position: "absolute",
      top: "50%",
      insetInlineStart: 0,
      insetInlineEnd: 0,
      zIndex: 1,
      height: pickerPanelCellHeight,
      transform: "translateY(-50%)",
      transition: `all ${motionDurationSlow}`,
      content: '""'
    },
    // >>> Default
    [pickerCellInnerCls]: {
      position: "relative",
      zIndex: 2,
      display: "inline-block",
      minWidth: pickerPanelCellHeight,
      height: pickerPanelCellHeight,
      lineHeight: `${pickerPanelCellHeight}px`,
      borderRadius: borderRadiusSM,
      transition: `background ${motionDurationMid}, border ${motionDurationMid}`
    },
    // >>> Hover
    [`&:hover:not(${pickerCellCls}-in-view),
    &:hover:not(${pickerCellCls}-selected):not(${pickerCellCls}-range-start):not(${pickerCellCls}-range-end):not(${pickerCellCls}-range-hover-start):not(${pickerCellCls}-range-hover-end)`]: {
      [pickerCellInnerCls]: {
        background: controlItemBgHover
      }
    },
    // >>> Today
    [`&-in-view${pickerCellCls}-today ${pickerCellInnerCls}`]: {
      "&::before": {
        position: "absolute",
        top: 0,
        insetInlineEnd: 0,
        bottom: 0,
        insetInlineStart: 0,
        zIndex: 1,
        border: `${lineWidth}px ${lineType} ${colorPrimary}`,
        borderRadius: borderRadiusSM,
        content: '""'
      }
    },
    // >>> In Range
    [`&-in-view${pickerCellCls}-in-range`]: {
      position: "relative",
      "&::before": {
        background: controlItemBgActive
      }
    },
    // >>> Selected
    [`&-in-view${pickerCellCls}-selected ${pickerCellInnerCls},
      &-in-view${pickerCellCls}-range-start ${pickerCellInnerCls},
      &-in-view${pickerCellCls}-range-end ${pickerCellInnerCls}`]: {
      color: colorTextLightSolid,
      background: colorPrimary
    },
    [`&-in-view${pickerCellCls}-range-start:not(${pickerCellCls}-range-start-single),
      &-in-view${pickerCellCls}-range-end:not(${pickerCellCls}-range-end-single)`]: {
      "&::before": {
        background: controlItemBgActive
      }
    },
    [`&-in-view${pickerCellCls}-range-start::before`]: {
      insetInlineStart: "50%"
    },
    [`&-in-view${pickerCellCls}-range-end::before`]: {
      insetInlineEnd: "50%"
    },
    // >>> Range Hover
    [`&-in-view${pickerCellCls}-range-hover-start:not(${pickerCellCls}-in-range):not(${pickerCellCls}-range-start):not(${pickerCellCls}-range-end),
      &-in-view${pickerCellCls}-range-hover-end:not(${pickerCellCls}-in-range):not(${pickerCellCls}-range-start):not(${pickerCellCls}-range-end),
      &-in-view${pickerCellCls}-range-hover-start${pickerCellCls}-range-start-single,
      &-in-view${pickerCellCls}-range-hover-start${pickerCellCls}-range-start${pickerCellCls}-range-end${pickerCellCls}-range-end-near-hover,
      &-in-view${pickerCellCls}-range-hover-end${pickerCellCls}-range-start${pickerCellCls}-range-end${pickerCellCls}-range-start-near-hover,
      &-in-view${pickerCellCls}-range-hover-end${pickerCellCls}-range-end-single,
      &-in-view${pickerCellCls}-range-hover:not(${pickerCellCls}-in-range)`]: {
      "&::after": {
        position: "absolute",
        top: "50%",
        zIndex: 0,
        height: controlHeightSM,
        borderTop: `${lineWidth}px dashed ${pickerDateHoverRangeBorderColor}`,
        borderBottom: `${lineWidth}px dashed ${pickerDateHoverRangeBorderColor}`,
        transform: "translateY(-50%)",
        transition: `all ${motionDurationSlow}`,
        content: '""'
      }
    },
    // Add space for stash
    [`&-range-hover-start::after,
      &-range-hover-end::after,
      &-range-hover::after`]: {
      insetInlineEnd: 0,
      insetInlineStart: pickerCellBorderGap
    },
    // Hover with in range
    [`&-in-view${pickerCellCls}-in-range${pickerCellCls}-range-hover::before,
      &-in-view${pickerCellCls}-range-start${pickerCellCls}-range-hover::before,
      &-in-view${pickerCellCls}-range-end${pickerCellCls}-range-hover::before,
      &-in-view${pickerCellCls}-range-start:not(${pickerCellCls}-range-start-single)${pickerCellCls}-range-hover-start::before,
      &-in-view${pickerCellCls}-range-end:not(${pickerCellCls}-range-end-single)${pickerCellCls}-range-hover-end::before,
      ${componentCls}-panel
      > :not(${componentCls}-date-panel)
      &-in-view${pickerCellCls}-in-range${pickerCellCls}-range-hover-start::before,
      ${componentCls}-panel
      > :not(${componentCls}-date-panel)
      &-in-view${pickerCellCls}-in-range${pickerCellCls}-range-hover-end::before`]: {
      background: pickerBasicCellHoverWithRangeColor
    },
    // range start border-radius
    [`&-in-view${pickerCellCls}-range-start:not(${pickerCellCls}-range-start-single):not(${pickerCellCls}-range-end) ${pickerCellInnerCls}`]: {
      borderStartStartRadius: borderRadiusSM,
      borderEndStartRadius: borderRadiusSM,
      borderStartEndRadius: 0,
      borderEndEndRadius: 0
    },
    // range end border-radius
    [`&-in-view${pickerCellCls}-range-end:not(${pickerCellCls}-range-end-single):not(${pickerCellCls}-range-start) ${pickerCellInnerCls}`]: {
      borderStartStartRadius: 0,
      borderEndStartRadius: 0,
      borderStartEndRadius: borderRadiusSM,
      borderEndEndRadius: borderRadiusSM
    },
    [`&-range-hover${pickerCellCls}-range-end::after`]: {
      insetInlineStart: "50%"
    },
    // Edge start
    [`tr > &-in-view${pickerCellCls}-range-hover:first-child::after,
      tr > &-in-view${pickerCellCls}-range-hover-end:first-child::after,
      &-in-view${pickerCellCls}-start${pickerCellCls}-range-hover-edge-start${pickerCellCls}-range-hover-edge-start-near-range::after,
      &-in-view${pickerCellCls}-range-hover-edge-start:not(${pickerCellCls}-range-hover-edge-start-near-range)::after,
      &-in-view${pickerCellCls}-range-hover-start::after`]: {
      insetInlineStart: (pickerPanelCellWidth - pickerPanelCellHeight) / 2,
      borderInlineStart: `${lineWidth}px dashed ${pickerDateHoverRangeBorderColor}`,
      borderStartStartRadius: lineWidth,
      borderEndStartRadius: lineWidth
    },
    // Edge end
    [`tr > &-in-view${pickerCellCls}-range-hover:last-child::after,
      tr > &-in-view${pickerCellCls}-range-hover-start:last-child::after,
      &-in-view${pickerCellCls}-end${pickerCellCls}-range-hover-edge-end${pickerCellCls}-range-hover-edge-end-near-range::after,
      &-in-view${pickerCellCls}-range-hover-edge-end:not(${pickerCellCls}-range-hover-edge-end-near-range)::after,
      &-in-view${pickerCellCls}-range-hover-end::after`]: {
      insetInlineEnd: (pickerPanelCellWidth - pickerPanelCellHeight) / 2,
      borderInlineEnd: `${lineWidth}px dashed ${pickerDateHoverRangeBorderColor}`,
      borderStartEndRadius: lineWidth,
      borderEndEndRadius: lineWidth
    },
    // >>> Disabled
    "&-disabled": {
      color: colorTextDisabled,
      pointerEvents: "none",
      [pickerCellInnerCls]: {
        background: "transparent"
      },
      "&::before": {
        background: colorBgContainerDisabled
      }
    },
    [`&-disabled${pickerCellCls}-today ${pickerCellInnerCls}::before`]: {
      borderColor: colorTextDisabled
    }
  };
};
var genPanelStyle = (token) => {
  const {
    componentCls,
    pickerCellInnerCls,
    pickerYearMonthCellWidth,
    pickerControlIconSize,
    pickerPanelCellWidth,
    paddingSM,
    paddingXS,
    paddingXXS,
    colorBgContainer,
    lineWidth,
    lineType,
    borderRadiusLG,
    colorPrimary,
    colorTextHeading,
    colorSplit,
    pickerControlIconBorderWidth,
    colorIcon,
    pickerTextHeight,
    motionDurationMid,
    colorIconHover,
    fontWeightStrong,
    pickerPanelCellHeight,
    pickerCellPaddingVertical,
    colorTextDisabled,
    colorText,
    fontSize,
    pickerBasicCellHoverWithRangeColor,
    motionDurationSlow,
    pickerPanelWithoutTimeCellHeight,
    pickerQuarterPanelContentHeight,
    colorLink,
    colorLinkActive,
    colorLinkHover,
    pickerDateHoverRangeBorderColor,
    borderRadiusSM,
    colorTextLightSolid,
    borderRadius,
    controlItemBgHover,
    pickerTimePanelColumnHeight,
    pickerTimePanelColumnWidth,
    pickerTimePanelCellHeight,
    controlItemBgActive,
    marginXXS
  } = token;
  const pickerPanelWidth = pickerPanelCellWidth * 7 + paddingSM * 2 + 4;
  const hoverCellFixedDistance = (pickerPanelWidth - paddingXS * 2) / 3 - pickerYearMonthCellWidth - paddingSM;
  return {
    [componentCls]: {
      "&-panel": {
        display: "inline-flex",
        flexDirection: "column",
        textAlign: "center",
        background: colorBgContainer,
        border: `${lineWidth}px ${lineType} ${colorSplit}`,
        borderRadius: borderRadiusLG,
        outline: "none",
        "&-focused": {
          borderColor: colorPrimary
        },
        "&-rtl": {
          direction: "rtl",
          [`${componentCls}-prev-icon,
              ${componentCls}-super-prev-icon`]: {
            transform: "rotate(45deg)"
          },
          [`${componentCls}-next-icon,
              ${componentCls}-super-next-icon`]: {
            transform: "rotate(-135deg)"
          }
        }
      },
      // ========================================================
      // =                     Shared Panel                     =
      // ========================================================
      [`&-decade-panel,
        &-year-panel,
        &-quarter-panel,
        &-month-panel,
        &-week-panel,
        &-date-panel,
        &-time-panel`]: {
        display: "flex",
        flexDirection: "column",
        width: pickerPanelWidth
      },
      // ======================= Header =======================
      "&-header": {
        display: "flex",
        padding: `0 ${paddingXS}px`,
        color: colorTextHeading,
        borderBottom: `${lineWidth}px ${lineType} ${colorSplit}`,
        "> *": {
          flex: "none"
        },
        button: {
          padding: 0,
          color: colorIcon,
          lineHeight: `${pickerTextHeight}px`,
          background: "transparent",
          border: 0,
          cursor: "pointer",
          transition: `color ${motionDurationMid}`
        },
        "> button": {
          minWidth: "1.6em",
          fontSize,
          "&:hover": {
            color: colorIconHover
          }
        },
        "&-view": {
          flex: "auto",
          fontWeight: fontWeightStrong,
          lineHeight: `${pickerTextHeight}px`,
          button: {
            color: "inherit",
            fontWeight: "inherit",
            verticalAlign: "top",
            "&:not(:first-child)": {
              marginInlineStart: paddingXS
            },
            "&:hover": {
              color: colorPrimary
            }
          }
        }
      },
      // Arrow button
      [`&-prev-icon,
        &-next-icon,
        &-super-prev-icon,
        &-super-next-icon`]: {
        position: "relative",
        display: "inline-block",
        width: pickerControlIconSize,
        height: pickerControlIconSize,
        "&::before": {
          position: "absolute",
          top: 0,
          insetInlineStart: 0,
          display: "inline-block",
          width: pickerControlIconSize,
          height: pickerControlIconSize,
          border: `0 solid currentcolor`,
          borderBlockStartWidth: pickerControlIconBorderWidth,
          borderBlockEndWidth: 0,
          borderInlineStartWidth: pickerControlIconBorderWidth,
          borderInlineEndWidth: 0,
          content: '""'
        }
      },
      [`&-super-prev-icon,
        &-super-next-icon`]: {
        "&::after": {
          position: "absolute",
          top: Math.ceil(pickerControlIconSize / 2),
          insetInlineStart: Math.ceil(pickerControlIconSize / 2),
          display: "inline-block",
          width: pickerControlIconSize,
          height: pickerControlIconSize,
          border: "0 solid currentcolor",
          borderBlockStartWidth: pickerControlIconBorderWidth,
          borderBlockEndWidth: 0,
          borderInlineStartWidth: pickerControlIconBorderWidth,
          borderInlineEndWidth: 0,
          content: '""'
        }
      },
      [`&-prev-icon,
        &-super-prev-icon`]: {
        transform: "rotate(-45deg)"
      },
      [`&-next-icon,
        &-super-next-icon`]: {
        transform: "rotate(135deg)"
      },
      // ======================== Body ========================
      "&-content": {
        width: "100%",
        tableLayout: "fixed",
        borderCollapse: "collapse",
        "th, td": {
          position: "relative",
          minWidth: pickerPanelCellHeight,
          fontWeight: "normal"
        },
        th: {
          height: pickerPanelCellHeight + pickerCellPaddingVertical * 2,
          color: colorText,
          verticalAlign: "middle"
        }
      },
      "&-cell": _extends({
        padding: `${pickerCellPaddingVertical}px 0`,
        color: colorTextDisabled,
        cursor: "pointer",
        // In view
        "&-in-view": {
          color: colorText
        }
      }, genPickerCellInnerStyle(token)),
      // DatePanel only
      [`&-date-panel ${componentCls}-cell-in-view${componentCls}-cell-in-range${componentCls}-cell-range-hover-start ${pickerCellInnerCls},
        &-date-panel ${componentCls}-cell-in-view${componentCls}-cell-in-range${componentCls}-cell-range-hover-end ${pickerCellInnerCls}`]: {
        "&::after": {
          position: "absolute",
          top: 0,
          bottom: 0,
          zIndex: -1,
          background: pickerBasicCellHoverWithRangeColor,
          transition: `all ${motionDurationSlow}`,
          content: '""'
        }
      },
      [`&-date-panel
        ${componentCls}-cell-in-view${componentCls}-cell-in-range${componentCls}-cell-range-hover-start
        ${pickerCellInnerCls}::after`]: {
        insetInlineEnd: -(pickerPanelCellWidth - pickerPanelCellHeight) / 2,
        insetInlineStart: 0
      },
      [`&-date-panel ${componentCls}-cell-in-view${componentCls}-cell-in-range${componentCls}-cell-range-hover-end ${pickerCellInnerCls}::after`]: {
        insetInlineEnd: 0,
        insetInlineStart: -(pickerPanelCellWidth - pickerPanelCellHeight) / 2
      },
      // Hover with range start & end
      [`&-range-hover${componentCls}-range-start::after`]: {
        insetInlineEnd: "50%"
      },
      [`&-decade-panel,
        &-year-panel,
        &-quarter-panel,
        &-month-panel`]: {
        [`${componentCls}-content`]: {
          height: pickerPanelWithoutTimeCellHeight * 4
        },
        [pickerCellInnerCls]: {
          padding: `0 ${paddingXS}px`
        }
      },
      "&-quarter-panel": {
        [`${componentCls}-content`]: {
          height: pickerQuarterPanelContentHeight
        }
      },
      // ======================== Footer ========================
      [`&-panel ${componentCls}-footer`]: {
        borderTop: `${lineWidth}px ${lineType} ${colorSplit}`
      },
      "&-footer": {
        width: "min-content",
        minWidth: "100%",
        lineHeight: `${pickerTextHeight - 2 * lineWidth}px`,
        textAlign: "center",
        "&-extra": {
          padding: `0 ${paddingSM}`,
          lineHeight: `${pickerTextHeight - 2 * lineWidth}px`,
          textAlign: "start",
          "&:not(:last-child)": {
            borderBottom: `${lineWidth}px ${lineType} ${colorSplit}`
          }
        }
      },
      "&-now": {
        textAlign: "start"
      },
      "&-today-btn": {
        color: colorLink,
        "&:hover": {
          color: colorLinkHover
        },
        "&:active": {
          color: colorLinkActive
        },
        [`&${componentCls}-today-btn-disabled`]: {
          color: colorTextDisabled,
          cursor: "not-allowed"
        }
      },
      // ========================================================
      // =                       Special                        =
      // ========================================================
      // ===================== Decade Panel =====================
      "&-decade-panel": {
        [pickerCellInnerCls]: {
          padding: `0 ${paddingXS / 2}px`
        },
        [`${componentCls}-cell::before`]: {
          display: "none"
        }
      },
      // ============= Year & Quarter & Month Panel =============
      [`&-year-panel,
        &-quarter-panel,
        &-month-panel`]: {
        [`${componentCls}-body`]: {
          padding: `0 ${paddingXS}px`
        },
        [pickerCellInnerCls]: {
          width: pickerYearMonthCellWidth
        },
        [`${componentCls}-cell-range-hover-start::after`]: {
          insetInlineStart: hoverCellFixedDistance,
          borderInlineStart: `${lineWidth}px dashed ${pickerDateHoverRangeBorderColor}`,
          borderStartStartRadius: borderRadiusSM,
          borderBottomStartRadius: borderRadiusSM,
          borderStartEndRadius: 0,
          borderBottomEndRadius: 0,
          [`${componentCls}-panel-rtl &`]: {
            insetInlineEnd: hoverCellFixedDistance,
            borderInlineEnd: `${lineWidth}px dashed ${pickerDateHoverRangeBorderColor}`,
            borderStartStartRadius: 0,
            borderBottomStartRadius: 0,
            borderStartEndRadius: borderRadiusSM,
            borderBottomEndRadius: borderRadiusSM
          }
        },
        [`${componentCls}-cell-range-hover-end::after`]: {
          insetInlineEnd: hoverCellFixedDistance,
          borderInlineEnd: `${lineWidth}px dashed ${pickerDateHoverRangeBorderColor}`,
          borderStartStartRadius: 0,
          borderEndStartRadius: 0,
          borderStartEndRadius: borderRadius,
          borderEndEndRadius: borderRadius,
          [`${componentCls}-panel-rtl &`]: {
            insetInlineStart: hoverCellFixedDistance,
            borderInlineStart: `${lineWidth}px dashed ${pickerDateHoverRangeBorderColor}`,
            borderStartStartRadius: borderRadius,
            borderEndStartRadius: borderRadius,
            borderStartEndRadius: 0,
            borderEndEndRadius: 0
          }
        }
      },
      // ====================== Week Panel ======================
      "&-week-panel": {
        [`${componentCls}-body`]: {
          padding: `${paddingXS}px ${paddingSM}px`
        },
        // Clear cell style
        [`${componentCls}-cell`]: {
          [`&:hover ${pickerCellInnerCls},
            &-selected ${pickerCellInnerCls},
            ${pickerCellInnerCls}`]: {
            background: "transparent !important"
          }
        },
        "&-row": {
          td: {
            transition: `background ${motionDurationMid}`,
            "&:first-child": {
              borderStartStartRadius: borderRadiusSM,
              borderEndStartRadius: borderRadiusSM
            },
            "&:last-child": {
              borderStartEndRadius: borderRadiusSM,
              borderEndEndRadius: borderRadiusSM
            }
          },
          "&:hover td": {
            background: controlItemBgHover
          },
          [`&-selected td,
            &-selected:hover td`]: {
            background: colorPrimary,
            [`&${componentCls}-cell-week`]: {
              color: new TinyColor(colorTextLightSolid).setAlpha(0.5).toHexString()
            },
            [`&${componentCls}-cell-today ${pickerCellInnerCls}::before`]: {
              borderColor: colorTextLightSolid
            },
            [pickerCellInnerCls]: {
              color: colorTextLightSolid
            }
          }
        }
      },
      // ====================== Date Panel ======================
      "&-date-panel": {
        [`${componentCls}-body`]: {
          padding: `${paddingXS}px ${paddingSM}px`
        },
        [`${componentCls}-content`]: {
          width: pickerPanelCellWidth * 7,
          th: {
            width: pickerPanelCellWidth
          }
        }
      },
      // ==================== Datetime Panel ====================
      "&-datetime-panel": {
        display: "flex",
        [`${componentCls}-time-panel`]: {
          borderInlineStart: `${lineWidth}px ${lineType} ${colorSplit}`
        },
        [`${componentCls}-date-panel,
          ${componentCls}-time-panel`]: {
          transition: `opacity ${motionDurationSlow}`
        },
        // Keyboard
        "&-active": {
          [`${componentCls}-date-panel,
            ${componentCls}-time-panel`]: {
            opacity: 0.3,
            "&-active": {
              opacity: 1
            }
          }
        }
      },
      // ====================== Time Panel ======================
      "&-time-panel": {
        width: "auto",
        minWidth: "auto",
        direction: "ltr",
        [`${componentCls}-content`]: {
          display: "flex",
          flex: "auto",
          height: pickerTimePanelColumnHeight
        },
        "&-column": {
          flex: "1 0 auto",
          width: pickerTimePanelColumnWidth,
          margin: `${paddingXXS}px 0`,
          padding: 0,
          overflowY: "hidden",
          textAlign: "start",
          listStyle: "none",
          transition: `background ${motionDurationMid}`,
          overflowX: "hidden",
          "&::after": {
            display: "block",
            height: pickerTimePanelColumnHeight - pickerTimePanelCellHeight,
            content: '""'
          },
          "&:not(:first-child)": {
            borderInlineStart: `${lineWidth}px ${lineType} ${colorSplit}`
          },
          "&-active": {
            background: new TinyColor(controlItemBgActive).setAlpha(0.2).toHexString()
          },
          "&:hover": {
            overflowY: "auto"
          },
          "> li": {
            margin: 0,
            padding: 0,
            [`&${componentCls}-time-panel-cell`]: {
              marginInline: marginXXS,
              [`${componentCls}-time-panel-cell-inner`]: {
                display: "block",
                width: pickerTimePanelColumnWidth - 2 * marginXXS,
                height: pickerTimePanelCellHeight,
                margin: 0,
                paddingBlock: 0,
                paddingInlineEnd: 0,
                paddingInlineStart: (pickerTimePanelColumnWidth - pickerTimePanelCellHeight) / 2,
                color: colorText,
                lineHeight: `${pickerTimePanelCellHeight}px`,
                borderRadius: borderRadiusSM,
                cursor: "pointer",
                transition: `background ${motionDurationMid}`,
                "&:hover": {
                  background: controlItemBgHover
                }
              },
              "&-selected": {
                [`${componentCls}-time-panel-cell-inner`]: {
                  background: controlItemBgActive
                }
              },
              "&-disabled": {
                [`${componentCls}-time-panel-cell-inner`]: {
                  color: colorTextDisabled,
                  background: "transparent",
                  cursor: "not-allowed"
                }
              }
            }
          }
        }
      },
      // https://github.com/ant-design/ant-design/issues/39227
      [`&-datetime-panel ${componentCls}-time-panel-column:after`]: {
        height: pickerTimePanelColumnHeight - pickerTimePanelCellHeight + paddingXXS * 2
      }
    }
  };
};
var genPickerStatusStyle = (token) => {
  const {
    componentCls,
    colorBgContainer,
    colorError,
    colorErrorOutline,
    colorWarning,
    colorWarningOutline
  } = token;
  return {
    [componentCls]: {
      [`&-status-error${componentCls}`]: {
        "&, &:not([disabled]):hover": {
          backgroundColor: colorBgContainer,
          borderColor: colorError
        },
        "&-focused, &:focus": _extends({}, genActiveStyle(merge(token, {
          inputBorderActiveColor: colorError,
          inputBorderHoverColor: colorError,
          controlOutline: colorErrorOutline
        }))),
        [`${componentCls}-active-bar`]: {
          background: colorError
        }
      },
      [`&-status-warning${componentCls}`]: {
        "&, &:not([disabled]):hover": {
          backgroundColor: colorBgContainer,
          borderColor: colorWarning
        },
        "&-focused, &:focus": _extends({}, genActiveStyle(merge(token, {
          inputBorderActiveColor: colorWarning,
          inputBorderHoverColor: colorWarning,
          controlOutline: colorWarningOutline
        }))),
        [`${componentCls}-active-bar`]: {
          background: colorWarning
        }
      }
    }
  };
};
var genPickerStyle = (token) => {
  const {
    componentCls,
    antCls,
    boxShadowPopoverArrow,
    controlHeight,
    fontSize,
    inputPaddingHorizontal,
    colorBgContainer,
    lineWidth,
    lineType,
    colorBorder,
    borderRadius,
    motionDurationMid,
    colorBgContainerDisabled,
    colorTextDisabled,
    colorTextPlaceholder,
    controlHeightLG,
    fontSizeLG,
    controlHeightSM,
    inputPaddingHorizontalSM,
    paddingXS,
    marginXS,
    colorTextDescription,
    lineWidthBold,
    lineHeight,
    colorPrimary,
    motionDurationSlow,
    zIndexPopup,
    paddingXXS,
    paddingSM,
    pickerTextHeight,
    controlItemBgActive,
    colorPrimaryBorder,
    sizePopupArrow,
    borderRadiusXS,
    borderRadiusOuter,
    colorBgElevated,
    borderRadiusLG,
    boxShadowSecondary,
    borderRadiusSM,
    colorSplit,
    controlItemBgHover,
    presetsWidth,
    presetsMaxWidth
  } = token;
  return [
    {
      [componentCls]: _extends(_extends(_extends({}, resetComponent(token)), genPikerPadding(token, controlHeight, fontSize, inputPaddingHorizontal)), {
        position: "relative",
        display: "inline-flex",
        alignItems: "center",
        background: colorBgContainer,
        lineHeight: 1,
        border: `${lineWidth}px ${lineType} ${colorBorder}`,
        borderRadius,
        transition: `border ${motionDurationMid}, box-shadow ${motionDurationMid}`,
        "&:hover, &-focused": _extends({}, genHoverStyle(token)),
        "&-focused": _extends({}, genActiveStyle(token)),
        [`&${componentCls}-disabled`]: {
          background: colorBgContainerDisabled,
          borderColor: colorBorder,
          cursor: "not-allowed",
          [`${componentCls}-suffix`]: {
            color: colorTextDisabled
          }
        },
        [`&${componentCls}-borderless`]: {
          backgroundColor: "transparent !important",
          borderColor: "transparent !important",
          boxShadow: "none !important"
        },
        // ======================== Input =========================
        [`${componentCls}-input`]: {
          position: "relative",
          display: "inline-flex",
          alignItems: "center",
          width: "100%",
          "> input": _extends(_extends({}, genBasicInputStyle(token)), {
            flex: "auto",
            // Fix Firefox flex not correct:
            // https://github.com/ant-design/ant-design/pull/20023#issuecomment-564389553
            minWidth: 1,
            height: "auto",
            padding: 0,
            background: "transparent",
            border: 0,
            "&:focus": {
              boxShadow: "none"
            },
            "&[disabled]": {
              background: "transparent"
            }
          }),
          "&:hover": {
            [`${componentCls}-clear`]: {
              opacity: 1
            }
          },
          "&-placeholder": {
            "> input": {
              color: colorTextPlaceholder
            }
          }
        },
        // Size
        "&-large": _extends(_extends({}, genPikerPadding(token, controlHeightLG, fontSizeLG, inputPaddingHorizontal)), {
          [`${componentCls}-input > input`]: {
            fontSize: fontSizeLG
          }
        }),
        "&-small": _extends({}, genPikerPadding(token, controlHeightSM, fontSize, inputPaddingHorizontalSM)),
        [`${componentCls}-suffix`]: {
          display: "flex",
          flex: "none",
          alignSelf: "center",
          marginInlineStart: paddingXS / 2,
          color: colorTextDisabled,
          lineHeight: 1,
          pointerEvents: "none",
          "> *": {
            verticalAlign: "top",
            "&:not(:last-child)": {
              marginInlineEnd: marginXS
            }
          }
        },
        [`${componentCls}-clear`]: {
          position: "absolute",
          top: "50%",
          insetInlineEnd: 0,
          color: colorTextDisabled,
          lineHeight: 1,
          background: colorBgContainer,
          transform: "translateY(-50%)",
          cursor: "pointer",
          opacity: 0,
          transition: `opacity ${motionDurationMid}, color ${motionDurationMid}`,
          "> *": {
            verticalAlign: "top"
          },
          "&:hover": {
            color: colorTextDescription
          }
        },
        [`${componentCls}-separator`]: {
          position: "relative",
          display: "inline-block",
          width: "1em",
          height: fontSizeLG,
          color: colorTextDisabled,
          fontSize: fontSizeLG,
          verticalAlign: "top",
          cursor: "default",
          [`${componentCls}-focused &`]: {
            color: colorTextDescription
          },
          [`${componentCls}-range-separator &`]: {
            [`${componentCls}-disabled &`]: {
              cursor: "not-allowed"
            }
          }
        },
        // ======================== Range =========================
        "&-range": {
          position: "relative",
          display: "inline-flex",
          // Clear
          [`${componentCls}-clear`]: {
            insetInlineEnd: inputPaddingHorizontal
          },
          "&:hover": {
            [`${componentCls}-clear`]: {
              opacity: 1
            }
          },
          // Active bar
          [`${componentCls}-active-bar`]: {
            bottom: -lineWidth,
            height: lineWidthBold,
            marginInlineStart: inputPaddingHorizontal,
            background: colorPrimary,
            opacity: 0,
            transition: `all ${motionDurationSlow} ease-out`,
            pointerEvents: "none"
          },
          [`&${componentCls}-focused`]: {
            [`${componentCls}-active-bar`]: {
              opacity: 1
            }
          },
          [`${componentCls}-range-separator`]: {
            alignItems: "center",
            padding: `0 ${paddingXS}px`,
            lineHeight: 1
          },
          [`&${componentCls}-small`]: {
            [`${componentCls}-clear`]: {
              insetInlineEnd: inputPaddingHorizontalSM
            },
            [`${componentCls}-active-bar`]: {
              marginInlineStart: inputPaddingHorizontalSM
            }
          }
        },
        // ======================= Dropdown =======================
        "&-dropdown": _extends(_extends(_extends({}, resetComponent(token)), genPanelStyle(token)), {
          position: "absolute",
          // Fix incorrect position of picker popup
          // https://github.com/ant-design/ant-design/issues/35590
          top: -9999,
          left: {
            _skip_check_: true,
            value: -9999
          },
          zIndex: zIndexPopup,
          [`&${componentCls}-dropdown-hidden`]: {
            display: "none"
          },
          [`&${componentCls}-dropdown-placement-bottomLeft`]: {
            [`${componentCls}-range-arrow`]: {
              top: 0,
              display: "block",
              transform: "translateY(-100%)"
            }
          },
          [`&${componentCls}-dropdown-placement-topLeft`]: {
            [`${componentCls}-range-arrow`]: {
              bottom: 0,
              display: "block",
              transform: "translateY(100%) rotate(180deg)"
            }
          },
          [`&${antCls}-slide-up-enter${antCls}-slide-up-enter-active${componentCls}-dropdown-placement-topLeft,
          &${antCls}-slide-up-enter${antCls}-slide-up-enter-active${componentCls}-dropdown-placement-topRight,
          &${antCls}-slide-up-appear${antCls}-slide-up-appear-active${componentCls}-dropdown-placement-topLeft,
          &${antCls}-slide-up-appear${antCls}-slide-up-appear-active${componentCls}-dropdown-placement-topRight`]: {
            animationName: slideDownIn
          },
          [`&${antCls}-slide-up-enter${antCls}-slide-up-enter-active${componentCls}-dropdown-placement-bottomLeft,
          &${antCls}-slide-up-enter${antCls}-slide-up-enter-active${componentCls}-dropdown-placement-bottomRight,
          &${antCls}-slide-up-appear${antCls}-slide-up-appear-active${componentCls}-dropdown-placement-bottomLeft,
          &${antCls}-slide-up-appear${antCls}-slide-up-appear-active${componentCls}-dropdown-placement-bottomRight`]: {
            animationName: slideUpIn
          },
          [`&${antCls}-slide-up-leave${antCls}-slide-up-leave-active${componentCls}-dropdown-placement-topLeft,
          &${antCls}-slide-up-leave${antCls}-slide-up-leave-active${componentCls}-dropdown-placement-topRight`]: {
            animationName: slideDownOut
          },
          [`&${antCls}-slide-up-leave${antCls}-slide-up-leave-active${componentCls}-dropdown-placement-bottomLeft,
          &${antCls}-slide-up-leave${antCls}-slide-up-leave-active${componentCls}-dropdown-placement-bottomRight`]: {
            animationName: slideUpOut
          },
          // Time picker with additional style
          [`${componentCls}-panel > ${componentCls}-time-panel`]: {
            paddingTop: paddingXXS
          },
          // ======================== Ranges ========================
          [`${componentCls}-ranges`]: {
            marginBottom: 0,
            padding: `${paddingXXS}px ${paddingSM}px`,
            overflow: "hidden",
            lineHeight: `${pickerTextHeight - 2 * lineWidth - paddingXS / 2}px`,
            textAlign: "start",
            listStyle: "none",
            display: "flex",
            justifyContent: "space-between",
            "> li": {
              display: "inline-block"
            },
            // https://github.com/ant-design/ant-design/issues/23687
            [`${componentCls}-preset > ${antCls}-tag-blue`]: {
              color: colorPrimary,
              background: controlItemBgActive,
              borderColor: colorPrimaryBorder,
              cursor: "pointer"
            },
            [`${componentCls}-ok`]: {
              marginInlineStart: "auto"
            }
          },
          [`${componentCls}-range-wrapper`]: {
            display: "flex",
            position: "relative"
          },
          [`${componentCls}-range-arrow`]: _extends({
            position: "absolute",
            zIndex: 1,
            display: "none",
            marginInlineStart: inputPaddingHorizontal * 1.5,
            transition: `left ${motionDurationSlow} ease-out`
          }, roundedArrow(sizePopupArrow, borderRadiusXS, borderRadiusOuter, colorBgElevated, boxShadowPopoverArrow)),
          [`${componentCls}-panel-container`]: {
            overflow: "hidden",
            verticalAlign: "top",
            background: colorBgElevated,
            borderRadius: borderRadiusLG,
            boxShadow: boxShadowSecondary,
            transition: `margin ${motionDurationSlow}`,
            // ======================== Layout ========================
            [`${componentCls}-panel-layout`]: {
              display: "flex",
              flexWrap: "nowrap",
              alignItems: "stretch"
            },
            // ======================== Preset ========================
            [`${componentCls}-presets`]: {
              display: "flex",
              flexDirection: "column",
              minWidth: presetsWidth,
              maxWidth: presetsMaxWidth,
              ul: {
                height: 0,
                flex: "auto",
                listStyle: "none",
                overflow: "auto",
                margin: 0,
                padding: paddingXS,
                borderInlineEnd: `${lineWidth}px ${lineType} ${colorSplit}`,
                li: _extends(_extends({}, textEllipsis), {
                  borderRadius: borderRadiusSM,
                  paddingInline: paddingXS,
                  paddingBlock: (controlHeightSM - Math.round(fontSize * lineHeight)) / 2,
                  cursor: "pointer",
                  transition: `all ${motionDurationSlow}`,
                  "+ li": {
                    marginTop: marginXS
                  },
                  "&:hover": {
                    background: controlItemBgHover
                  }
                })
              }
            },
            // ======================== Panels ========================
            [`${componentCls}-panels`]: {
              display: "inline-flex",
              flexWrap: "nowrap",
              direction: "ltr",
              [`${componentCls}-panel`]: {
                borderWidth: `0 0 ${lineWidth}px`
              },
              "&:last-child": {
                [`${componentCls}-panel`]: {
                  borderWidth: 0
                }
              }
            },
            [`${componentCls}-panel`]: {
              verticalAlign: "top",
              background: "transparent",
              borderRadius: 0,
              borderWidth: 0,
              [`${componentCls}-content,
            table`]: {
                textAlign: "center"
              },
              "&-focused": {
                borderColor: colorBorder
              }
            }
          }
        }),
        "&-dropdown-range": {
          padding: `${sizePopupArrow * 2 / 3}px 0`,
          "&-hidden": {
            display: "none"
          }
        },
        "&-rtl": {
          direction: "rtl",
          [`${componentCls}-separator`]: {
            transform: "rotate(180deg)"
          },
          [`${componentCls}-footer`]: {
            "&-extra": {
              direction: "rtl"
            }
          }
        }
      })
    },
    // Follow code may reuse in other components
    initSlideMotion(token, "slide-up"),
    initSlideMotion(token, "slide-down"),
    initMoveMotion(token, "move-up"),
    initMoveMotion(token, "move-down")
  ];
};
var initPickerPanelToken = (token) => {
  const pickerTimePanelCellHeight = 28;
  const {
    componentCls,
    controlHeightLG,
    controlHeightSM,
    colorPrimary,
    paddingXXS
  } = token;
  return {
    pickerCellCls: `${componentCls}-cell`,
    pickerCellInnerCls: `${componentCls}-cell-inner`,
    pickerTextHeight: controlHeightLG,
    pickerPanelCellWidth: controlHeightSM * 1.5,
    pickerPanelCellHeight: controlHeightSM,
    pickerDateHoverRangeBorderColor: new TinyColor(colorPrimary).lighten(20).toHexString(),
    pickerBasicCellHoverWithRangeColor: new TinyColor(colorPrimary).lighten(35).toHexString(),
    pickerPanelWithoutTimeCellHeight: controlHeightLG * 1.65,
    pickerYearMonthCellWidth: controlHeightLG * 1.5,
    pickerTimePanelColumnHeight: pickerTimePanelCellHeight * 8,
    pickerTimePanelColumnWidth: controlHeightLG * 1.4,
    pickerTimePanelCellHeight,
    pickerQuarterPanelContentHeight: controlHeightLG * 1.4,
    pickerCellPaddingVertical: paddingXXS,
    pickerCellBorderGap: 2,
    pickerControlIconSize: 7,
    pickerControlIconBorderWidth: 1.5
  };
};
var style_default2 = genComponentStyleHook("DatePicker", (token) => {
  const pickerToken = merge(initInputToken(token), initPickerPanelToken(token));
  return [
    genPickerStyle(pickerToken),
    genPickerStatusStyle(pickerToken),
    // =====================================================
    // ==             Space Compact                       ==
    // =====================================================
    genCompactItemStyle(token, {
      focusElCls: `${token.componentCls}-focused`
    })
  ];
}, (token) => ({
  presetsWidth: 120,
  presetsMaxWidth: 200,
  zIndexPopup: token.zIndexPopupBase + 50
}));

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/date-picker/generatePicker/generateSinglePicker.js
var __rest2 = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
function generateSinglePicker(generateConfig2, extraProps) {
  function getPicker(picker, displayName) {
    const comProps = _extends(_extends(_extends({}, commonProps()), datePickerProps()), extraProps);
    return defineComponent({
      compatConfig: {
        MODE: 3
      },
      name: displayName,
      inheritAttrs: false,
      props: comProps,
      slots: Object,
      setup(_props, _ref) {
        let {
          slots,
          expose,
          attrs,
          emit
        } = _ref;
        const props = _props;
        const formItemContext = useInjectFormItemContext();
        const formItemInputContext = FormItemInputContext.useInject();
        if (true) {
          devWarning_default(picker !== "quarter", displayName || "DatePicker", `DatePicker.${displayName} is legacy usage. Please use DatePicker[picker='${picker}'] directly.`);
          devWarning_default(!props.dropdownClassName, displayName || "DatePicker", "`dropdownClassName` is deprecated. Please use `popupClassName` instead.");
          devWarning_default(!(props.monthCellContentRender || slots.monthCellContentRender), displayName || "DatePicker", '`monthCellContentRender` is deprecated. Please use `monthCellRender"` instead.');
          devWarning_default(!attrs.getCalendarContainer, displayName || "DatePicker", '`getCalendarContainer` is deprecated. Please use `getPopupContainer"` instead.');
        }
        const {
          prefixCls,
          direction,
          getPopupContainer,
          size,
          rootPrefixCls,
          disabled
        } = useConfigInject_default("picker", props);
        const {
          compactSize,
          compactItemClassnames
        } = useCompactItemContext(prefixCls, direction);
        const mergedSize = computed(() => compactSize.value || size.value);
        const [wrapSSR, hashId] = style_default2(prefixCls);
        const pickerRef = ref();
        expose({
          focus: () => {
            var _a2;
            (_a2 = pickerRef.value) === null || _a2 === void 0 ? void 0 : _a2.focus();
          },
          blur: () => {
            var _a2;
            (_a2 = pickerRef.value) === null || _a2 === void 0 ? void 0 : _a2.blur();
          }
        });
        const maybeToString = (date) => {
          return props.valueFormat ? generateConfig2.toString(date, props.valueFormat) : date;
        };
        const onChange = (date, dateString) => {
          const value2 = maybeToString(date);
          emit("update:value", value2);
          emit("change", value2, dateString);
          formItemContext.onFieldChange();
        };
        const onOpenChange = (open) => {
          emit("update:open", open);
          emit("openChange", open);
        };
        const onFocus = (e) => {
          emit("focus", e);
        };
        const onBlur = (e) => {
          emit("blur", e);
          formItemContext.onFieldBlur();
        };
        const onPanelChange = (date, mode) => {
          const value2 = maybeToString(date);
          emit("panelChange", value2, mode);
        };
        const onOk = (date) => {
          const value2 = maybeToString(date);
          emit("ok", value2);
        };
        const [contextLocale] = useLocaleReceiver("DatePicker", en_US_default);
        const value = computed(() => {
          if (props.value) {
            return props.valueFormat ? generateConfig2.toDate(props.value, props.valueFormat) : props.value;
          }
          return props.value === "" ? void 0 : props.value;
        });
        const defaultValue = computed(() => {
          if (props.defaultValue) {
            return props.valueFormat ? generateConfig2.toDate(props.defaultValue, props.valueFormat) : props.defaultValue;
          }
          return props.defaultValue === "" ? void 0 : props.defaultValue;
        });
        const defaultPickerValue = computed(() => {
          if (props.defaultPickerValue) {
            return props.valueFormat ? generateConfig2.toDate(props.defaultPickerValue, props.valueFormat) : props.defaultPickerValue;
          }
          return props.defaultPickerValue === "" ? void 0 : props.defaultPickerValue;
        });
        return () => {
          var _a2, _b, _c, _d, _e, _f;
          const locale = _extends(_extends({}, contextLocale.value), props.locale);
          const p = _extends(_extends({}, props), attrs);
          const {
            bordered = true,
            placeholder,
            suffixIcon = (_a2 = slots.suffixIcon) === null || _a2 === void 0 ? void 0 : _a2.call(slots),
            showToday = true,
            transitionName,
            allowClear = true,
            dateRender = slots.dateRender,
            renderExtraFooter = slots.renderExtraFooter,
            monthCellRender = slots.monthCellRender || props.monthCellContentRender || slots.monthCellContentRender,
            clearIcon = (_b = slots.clearIcon) === null || _b === void 0 ? void 0 : _b.call(slots),
            id = formItemContext.id.value
          } = p, restProps = __rest2(p, ["bordered", "placeholder", "suffixIcon", "showToday", "transitionName", "allowClear", "dateRender", "renderExtraFooter", "monthCellRender", "clearIcon", "id"]);
          const showTime = p.showTime === "" ? true : p.showTime;
          const {
            format
          } = p;
          let additionalOverrideProps = {};
          if (picker) {
            additionalOverrideProps.picker = picker;
          }
          const mergedPicker = picker || p.picker || "date";
          additionalOverrideProps = _extends(_extends(_extends({}, additionalOverrideProps), showTime ? getTimeProps(_extends({
            format,
            picker: mergedPicker
          }, typeof showTime === "object" ? showTime : {})) : {}), mergedPicker === "time" ? getTimeProps(_extends(_extends({
            format
          }, restProps), {
            picker: mergedPicker
          })) : {});
          const pre = prefixCls.value;
          const suffixNode = createVNode(Fragment, null, [suffixIcon || (picker === "time" ? createVNode(ClockCircleOutlined_default, null, null) : createVNode(CalendarOutlined_default, null, null)), formItemInputContext.hasFeedback && formItemInputContext.feedbackIcon]);
          return wrapSSR(createVNode(vc_picker_default, _objectSpread2(_objectSpread2(_objectSpread2({
            "monthCellRender": monthCellRender,
            "dateRender": dateRender,
            "renderExtraFooter": renderExtraFooter,
            "ref": pickerRef,
            "placeholder": getPlaceholder(locale, mergedPicker, placeholder),
            "suffixIcon": suffixNode,
            "dropdownAlign": transPlacement2DropdownAlign(direction.value, props.placement),
            "clearIcon": clearIcon || createVNode(CloseCircleFilled_default, null, null),
            "allowClear": allowClear,
            "transitionName": transitionName || `${rootPrefixCls.value}-slide-up`
          }, restProps), additionalOverrideProps), {}, {
            "id": id,
            "picker": mergedPicker,
            "value": value.value,
            "defaultValue": defaultValue.value,
            "defaultPickerValue": defaultPickerValue.value,
            "showToday": showToday,
            "locale": locale.lang,
            "class": classNames_default({
              [`${pre}-${mergedSize.value}`]: mergedSize.value,
              [`${pre}-borderless`]: !bordered
            }, getStatusClassNames(pre, getMergedStatus(formItemInputContext.status, props.status), formItemInputContext.hasFeedback), attrs.class, hashId.value, compactItemClassnames.value),
            "disabled": disabled.value,
            "prefixCls": pre,
            "getPopupContainer": attrs.getCalendarContainer || getPopupContainer.value,
            "generateConfig": generateConfig2,
            "prevIcon": ((_c = slots.prevIcon) === null || _c === void 0 ? void 0 : _c.call(slots)) || createVNode("span", {
              "class": `${pre}-prev-icon`
            }, null),
            "nextIcon": ((_d = slots.nextIcon) === null || _d === void 0 ? void 0 : _d.call(slots)) || createVNode("span", {
              "class": `${pre}-next-icon`
            }, null),
            "superPrevIcon": ((_e = slots.superPrevIcon) === null || _e === void 0 ? void 0 : _e.call(slots)) || createVNode("span", {
              "class": `${pre}-super-prev-icon`
            }, null),
            "superNextIcon": ((_f = slots.superNextIcon) === null || _f === void 0 ? void 0 : _f.call(slots)) || createVNode("span", {
              "class": `${pre}-super-next-icon`
            }, null),
            "components": Components,
            "direction": direction.value,
            "dropdownClassName": classNames_default(hashId.value, props.popupClassName, props.dropdownClassName),
            "onChange": onChange,
            "onOpenChange": onOpenChange,
            "onFocus": onFocus,
            "onBlur": onBlur,
            "onPanelChange": onPanelChange,
            "onOk": onOk
          }), null));
        };
      }
    });
  }
  const DatePicker = getPicker(void 0, "ADatePicker");
  const WeekPicker = getPicker("week", "AWeekPicker");
  const MonthPicker = getPicker("month", "AMonthPicker");
  const YearPicker = getPicker("year", "AYearPicker");
  const TimePicker = getPicker("time", "TimePicker");
  const QuarterPicker = getPicker("quarter", "AQuarterPicker");
  return {
    DatePicker,
    WeekPicker,
    MonthPicker,
    YearPicker,
    TimePicker,
    QuarterPicker
  };
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/date-picker/generatePicker/generateRangePicker.js
var __rest3 = function(s, e) {
  var t = {};
  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
  if (s != null && typeof Object.getOwnPropertySymbols === "function") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
  }
  return t;
};
function generateRangePicker(generateConfig2, extraProps) {
  const RangePicker = defineComponent({
    compatConfig: {
      MODE: 3
    },
    name: "ARangePicker",
    inheritAttrs: false,
    props: _extends(_extends(_extends({}, commonProps()), rangePickerProps()), extraProps),
    slots: Object,
    setup(_props, _ref) {
      let {
        expose,
        slots,
        attrs,
        emit
      } = _ref;
      const props = _props;
      const formItemContext = useInjectFormItemContext();
      const formItemInputContext = FormItemInputContext.useInject();
      if (true) {
        devWarning_default(!props.dropdownClassName, "RangePicker", "`dropdownClassName` is deprecated. Please use `popupClassName` instead.");
        devWarning_default(!attrs.getCalendarContainer, "DatePicker", '`getCalendarContainer` is deprecated. Please use `getPopupContainer"` instead.');
      }
      const {
        prefixCls,
        direction,
        getPopupContainer,
        size,
        rootPrefixCls,
        disabled
      } = useConfigInject_default("picker", props);
      const {
        compactSize,
        compactItemClassnames
      } = useCompactItemContext(prefixCls, direction);
      const mergedSize = computed(() => compactSize.value || size.value);
      const [wrapSSR, hashId] = style_default2(prefixCls);
      const pickerRef = ref();
      expose({
        focus: () => {
          var _a2;
          (_a2 = pickerRef.value) === null || _a2 === void 0 ? void 0 : _a2.focus();
        },
        blur: () => {
          var _a2;
          (_a2 = pickerRef.value) === null || _a2 === void 0 ? void 0 : _a2.blur();
        }
      });
      const maybeToStrings = (dates) => {
        return props.valueFormat ? generateConfig2.toString(dates, props.valueFormat) : dates;
      };
      const onChange = (dates, dateStrings) => {
        const values = maybeToStrings(dates);
        emit("update:value", values);
        emit("change", values, dateStrings);
        formItemContext.onFieldChange();
      };
      const onOpenChange = (open) => {
        emit("update:open", open);
        emit("openChange", open);
      };
      const onFocus = (e) => {
        emit("focus", e);
      };
      const onBlur = (e) => {
        emit("blur", e);
        formItemContext.onFieldBlur();
      };
      const onPanelChange = (dates, modes) => {
        const values = maybeToStrings(dates);
        emit("panelChange", values, modes);
      };
      const onOk = (dates) => {
        const value2 = maybeToStrings(dates);
        emit("ok", value2);
      };
      const onCalendarChange = (dates, dateStrings, info) => {
        const values = maybeToStrings(dates);
        emit("calendarChange", values, dateStrings, info);
      };
      const [contextLocale] = useLocaleReceiver("DatePicker", en_US_default);
      const value = computed(() => {
        if (props.value) {
          return props.valueFormat ? generateConfig2.toDate(props.value, props.valueFormat) : props.value;
        }
        return props.value;
      });
      const defaultValue = computed(() => {
        if (props.defaultValue) {
          return props.valueFormat ? generateConfig2.toDate(props.defaultValue, props.valueFormat) : props.defaultValue;
        }
        return props.defaultValue;
      });
      const defaultPickerValue = computed(() => {
        if (props.defaultPickerValue) {
          return props.valueFormat ? generateConfig2.toDate(props.defaultPickerValue, props.valueFormat) : props.defaultPickerValue;
        }
        return props.defaultPickerValue;
      });
      return () => {
        var _a2, _b, _c, _d, _e, _f, _g;
        const locale = _extends(_extends({}, contextLocale.value), props.locale);
        const p = _extends(_extends({}, props), attrs);
        const {
          prefixCls: customizePrefixCls,
          bordered = true,
          placeholder,
          suffixIcon = (_a2 = slots.suffixIcon) === null || _a2 === void 0 ? void 0 : _a2.call(slots),
          picker = "date",
          transitionName,
          allowClear = true,
          dateRender = slots.dateRender,
          renderExtraFooter = slots.renderExtraFooter,
          separator = (_b = slots.separator) === null || _b === void 0 ? void 0 : _b.call(slots),
          clearIcon = (_c = slots.clearIcon) === null || _c === void 0 ? void 0 : _c.call(slots),
          id = formItemContext.id.value
        } = p, restProps = __rest3(p, ["prefixCls", "bordered", "placeholder", "suffixIcon", "picker", "transitionName", "allowClear", "dateRender", "renderExtraFooter", "separator", "clearIcon", "id"]);
        delete restProps["onUpdate:value"];
        delete restProps["onUpdate:open"];
        const {
          format,
          showTime
        } = p;
        let additionalOverrideProps = {};
        additionalOverrideProps = _extends(_extends(_extends({}, additionalOverrideProps), showTime ? getTimeProps(_extends({
          format,
          picker
        }, showTime)) : {}), picker === "time" ? getTimeProps(_extends(_extends({
          format
        }, omit_default(restProps, ["disabledTime"])), {
          picker
        })) : {});
        const pre = prefixCls.value;
        const suffixNode = createVNode(Fragment, null, [suffixIcon || (picker === "time" ? createVNode(ClockCircleOutlined_default, null, null) : createVNode(CalendarOutlined_default, null, null)), formItemInputContext.hasFeedback && formItemInputContext.feedbackIcon]);
        return wrapSSR(createVNode(RangePicker_default, _objectSpread2(_objectSpread2(_objectSpread2({
          "dateRender": dateRender,
          "renderExtraFooter": renderExtraFooter,
          "separator": separator || createVNode("span", {
            "aria-label": "to",
            "class": `${pre}-separator`
          }, [createVNode(SwapRightOutlined_default, null, null)]),
          "ref": pickerRef,
          "dropdownAlign": transPlacement2DropdownAlign(direction.value, props.placement),
          "placeholder": getRangePlaceholder(locale, picker, placeholder),
          "suffixIcon": suffixNode,
          "clearIcon": clearIcon || createVNode(CloseCircleFilled_default, null, null),
          "allowClear": allowClear,
          "transitionName": transitionName || `${rootPrefixCls.value}-slide-up`
        }, restProps), additionalOverrideProps), {}, {
          "disabled": disabled.value,
          "id": id,
          "value": value.value,
          "defaultValue": defaultValue.value,
          "defaultPickerValue": defaultPickerValue.value,
          "picker": picker,
          "class": classNames_default({
            [`${pre}-${mergedSize.value}`]: mergedSize.value,
            [`${pre}-borderless`]: !bordered
          }, getStatusClassNames(pre, getMergedStatus(formItemInputContext.status, props.status), formItemInputContext.hasFeedback), attrs.class, hashId.value, compactItemClassnames.value),
          "locale": locale.lang,
          "prefixCls": pre,
          "getPopupContainer": attrs.getCalendarContainer || getPopupContainer.value,
          "generateConfig": generateConfig2,
          "prevIcon": ((_d = slots.prevIcon) === null || _d === void 0 ? void 0 : _d.call(slots)) || createVNode("span", {
            "class": `${pre}-prev-icon`
          }, null),
          "nextIcon": ((_e = slots.nextIcon) === null || _e === void 0 ? void 0 : _e.call(slots)) || createVNode("span", {
            "class": `${pre}-next-icon`
          }, null),
          "superPrevIcon": ((_f = slots.superPrevIcon) === null || _f === void 0 ? void 0 : _f.call(slots)) || createVNode("span", {
            "class": `${pre}-super-prev-icon`
          }, null),
          "superNextIcon": ((_g = slots.superNextIcon) === null || _g === void 0 ? void 0 : _g.call(slots)) || createVNode("span", {
            "class": `${pre}-super-next-icon`
          }, null),
          "components": Components,
          "direction": direction.value,
          "dropdownClassName": classNames_default(hashId.value, props.popupClassName, props.dropdownClassName),
          "onChange": onChange,
          "onOpenChange": onOpenChange,
          "onFocus": onFocus,
          "onBlur": onBlur,
          "onPanelChange": onPanelChange,
          "onOk": onOk,
          "onCalendarChange": onCalendarChange
        }), null));
      };
    }
  });
  return RangePicker;
}

// ../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/date-picker/generatePicker/index.js
var Components = {
  button: PickerButton_default,
  rangeItem: PickerTag
};
function toArray2(list) {
  if (!list) {
    return [];
  }
  return Array.isArray(list) ? list : [list];
}
function getTimeProps(props) {
  const {
    format,
    picker,
    showHour,
    showMinute,
    showSecond,
    use12Hours
  } = props;
  const firstFormat = toArray2(format)[0];
  const showTimeObj = _extends({}, props);
  if (firstFormat && typeof firstFormat === "string") {
    if (!firstFormat.includes("s") && showSecond === void 0) {
      showTimeObj.showSecond = false;
    }
    if (!firstFormat.includes("m") && showMinute === void 0) {
      showTimeObj.showMinute = false;
    }
    if (!firstFormat.includes("H") && !firstFormat.includes("h") && showHour === void 0) {
      showTimeObj.showHour = false;
    }
    if ((firstFormat.includes("a") || firstFormat.includes("A")) && use12Hours === void 0) {
      showTimeObj.use12Hours = true;
    }
  }
  if (picker === "time") {
    return showTimeObj;
  }
  if (typeof firstFormat === "function") {
    delete showTimeObj.format;
  }
  return {
    showTime: showTimeObj
  };
}
function generatePicker(generateConfig2, extraProps) {
  const {
    DatePicker,
    WeekPicker,
    MonthPicker,
    YearPicker,
    TimePicker,
    QuarterPicker
  } = generateSinglePicker(generateConfig2, extraProps);
  const RangePicker = generateRangePicker(generateConfig2, extraProps);
  return {
    DatePicker,
    WeekPicker,
    MonthPicker,
    YearPicker,
    TimePicker,
    QuarterPicker,
    RangePicker
  };
}
var generatePicker_default = generatePicker;

export {
  dayjs_default,
  PickerPanel_default,
  tryOnScopeDispose,
  unrefElement,
  useSupported,
  defaultWindow,
  genPanelStyle,
  initPickerPanelToken,
  CheckableTag_default,
  tag_default,
  commonProps,
  datePickerProps,
  rangePickerProps,
  generatePicker_default
};
//# sourceMappingURL=chunk-IYMQZUOL.js.map
