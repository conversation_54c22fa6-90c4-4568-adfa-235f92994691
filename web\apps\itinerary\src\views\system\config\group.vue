<script setup lang="ts">
import { h, onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Page } from '@vben/common-ui';
import { 
  Button, 
  Card, 
  Form, 
  Input, 
  Space, 
  Table, 
  Tag, 
  message, 
  Popconfirm,
  Modal,
  Select,
  Switch,
  InputNumber,
  Textarea,
  Breadcrumb
} from 'ant-design-vue';
import { 
  getConfigsByGroup,
  getConfigGroupDetail,
  createConfig, 
  updateConfig, 
  deleteConfig,
  type ConfigItem,
  type ConfigListParams,
  type CreateConfigParams,
  type UpdateConfigParams,
  type ConfigType,
  type ConfigGroupDetail
} from '#/api/config';

// 路由相关
const route = useRoute();
const router = useRouter();
const groupKey = ref(route.params.groupKey as string);

// 分组信息
const groupInfo = ref<ConfigGroupDetail | null>(null);

// 查询表单
const searchForm = reactive<Omit<ConfigListParams, 'config_group'>>({
  config_key: '',
  config_name: '',
  config_type: undefined,
  page: 1,
  limit: 10,
});

// 表格数据
const loading = ref(false);
const dataSource = ref<ConfigItem[]>([]);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
});

// 弹窗相关
const modalVisible = ref(false);
const modalTitle = ref('');
const isEdit = ref(false);
const currentRecord = ref<ConfigItem | null>(null);

// 表单数据
const formData = reactive<CreateConfigParams>({
  config_key: '',
  config_name: '',
  config_value: '',
  config_type: 'string',
  config_group: groupKey.value as any,
  description: '',
  is_public: false,
  sort_order: 0,
});

// 配置类型选项
const configTypeOptions = [
  { value: 'string', label: '字符串' },
  { value: 'number', label: '数字' },
  { value: 'boolean', label: '布尔值' },
  { value: 'json', label: 'JSON' },
  { value: 'text', label: '文本' },
];

// 表格列配置
const columns = [
  { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
  { title: '配置键', dataIndex: 'config_key', key: 'config_key', width: 200 },
  { title: '配置名称', dataIndex: 'config_name', key: 'config_name', width: 200 },
  { title: '配置值', dataIndex: 'config_value', key: 'config_value', width: 200, ellipsis: true },
  { 
    title: '类型', 
    dataIndex: 'config_type', 
    key: 'config_type', 
    width: 100,
    customRender: ({ text }: any) => {
      const option = configTypeOptions.find(opt => opt.value === text);
      return h(Tag, { color: 'blue' }, () => option?.label || text);
    }
  },
  { 
    title: '公开', 
    dataIndex: 'is_public', 
    key: 'is_public', 
    width: 80,
    customRender: ({ text }: any) => h(Tag, { color: text ? 'success' : 'default' }, () => text ? '是' : '否')
  },
  { title: '排序', dataIndex: 'sort_order', key: 'sort_order', width: 80 },
  { title: '创建时间', dataIndex: 'created_at', key: 'created_at', width: 180 },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right' as const,
    customRender: ({ record }: { record: ConfigItem }) => {
      return h(Space, {}, () => [
        h(Button, { 
          type: 'primary', 
          size: 'small',
          onClick: () => handleEdit(record)
        }, () => '编辑'),
        h(Popconfirm, {
          title: '确认删除该配置？',
          onConfirm: () => handleDelete(record.id!),
        }, {
          default: () => h(Button, { 
            danger: true, 
            size: 'small' 
          }, () => '删除'),
        }),
      ]);
    },
  },
];

// 获取分组信息
async function fetchGroupInfo() {
  try {
    const res = await getConfigGroupDetail(groupKey.value);
    groupInfo.value = res;
  } catch (error) {
    message.error('获取分组信息失败');
  }
}

// 获取配置列表
async function fetchData() {
  loading.value = true;
  try {
    const params = {
      ...searchForm,
      page: pagination.current,
      limit: pagination.pageSize,
    };
    const res = await getConfigsByGroup(groupKey.value, params);
    dataSource.value = res.configs || [];
    pagination.total = res.total || 0;
  } catch (error) {
    message.error('获取配置列表失败');
  } finally {
    loading.value = false;
  }
}

// 搜索
function handleSearch() {
  pagination.current = 1;
  fetchData();
}

// 重置搜索
function handleReset() {
  Object.assign(searchForm, {
    config_key: '',
    config_name: '',
    config_type: undefined,
    page: 1,
    limit: 10,
  });
  pagination.current = 1;
  fetchData();
}

// 表格变化
function handleTableChange(pag: any) {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchData();
}

// 新增配置
function handleAdd() {
  modalTitle.value = '新增配置';
  isEdit.value = false;
  currentRecord.value = null;
  resetForm();
  modalVisible.value = true;
}

// 编辑配置
function handleEdit(record: ConfigItem) {
  modalTitle.value = '编辑配置';
  isEdit.value = true;
  currentRecord.value = record;
  Object.assign(formData, {
    config_key: record.config_key,
    group_id: record.group_id,
    config_name: record.config_name,
    config_value: record.config_value || '',
    config_type: record.config_type,
    config_group: record.config_group,
    description: record.description || '',
    is_public: record.is_public || false,
    sort_order: record.sort_order || 0,
  });
  modalVisible.value = true;
}

// 删除配置
async function handleDelete(id: string | number) {
  try {
    await deleteConfig(id);
    message.success('删除成功');
    fetchData();
  } catch (error) {
    message.error('删除失败');
  }
}

// 保存配置
async function handleSave() {
  try {
    if (isEdit.value && currentRecord.value) {
      await updateConfig(currentRecord.value.id!, formData);
      message.success('更新成功');
    } else {
      await createConfig(formData);
      message.success('创建成功');
    }
    modalVisible.value = false;
    fetchData();
  } catch (error) {
    // message.error(isEdit.value ? '更新失败' : '创建失败');
  }
}

// 取消弹窗
function handleCancel() {
  modalVisible.value = false;
  resetForm();
}

// 重置表单
function resetForm() {
  Object.assign(formData, {
    config_key: '',
    config_name: '',
    config_value: '',
    config_type: 'string',
    config_group: groupKey.value,
    description: '',
    is_public: false,
    sort_order: 0,
    group_id: groupInfo.value?.id,
  });
}

// 返回分组列表
function goBack() {
  router.push('/system/config');
}

onMounted(() => {
  fetchGroupInfo();
  fetchData();
});
</script>

<template>
  <div>
    <Page :title="`${groupInfo?.group_name || '配置分组'} - 配置管理`">
      <!-- 面包屑导航 -->
      <Card class="mb-4">
        <Breadcrumb>
          <Breadcrumb.Item>
            <Button type="link" @click="goBack">配置管理</Button>
          </Breadcrumb.Item>
          <Breadcrumb.Item>{{ groupInfo?.group_name || groupKey }}</Breadcrumb.Item>
        </Breadcrumb>
        
        <!-- 分组信息 -->
        <div v-if="groupInfo" class="mt-4 p-4 bg-gray-50 rounded">
          <div class="flex items-center space-x-4">
            <span class="text-2xl">{{ groupInfo.icon || '📁' }}</span>
            <div>
              <h3 class="text-lg font-semibold">{{ groupInfo.group_name }}</h3>
              <p class="text-gray-600 text-sm">{{ groupInfo.description || '暂无描述' }}</p>
            </div>
          </div>
        </div>
      </Card>

      <!-- 搜索表单 -->
      <Card class="mb-4">
        <Form layout="inline" :model="searchForm">
          <Form.Item label="配置键">
            <Input v-model:value="searchForm.config_key" placeholder="配置键" allow-clear />
          </Form.Item>
          <Form.Item label="配置名称">
            <Input v-model:value="searchForm.config_name" placeholder="配置名称" allow-clear />
          </Form.Item>
          <Form.Item label="配置类型">
            <Select 
              v-model:value="searchForm.config_type" 
              placeholder="选择类型" 
              allow-clear
              style="width: 120px"
            >
              <Select.Option v-for="option in configTypeOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </Select.Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" @click="handleSearch">搜索</Button>
              <Button @click="handleReset">重置</Button>
              <Button type="primary" @click="handleAdd">新增配置</Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <!-- 配置列表 -->
      <Card>
        <Table
          :columns="columns"
          :data-source="dataSource"
          :pagination="pagination"
          :loading="loading"
          :row-key="(record: ConfigItem) => record.id!"
          :scroll="{ x: 1400 }"
          @change="handleTableChange"
        />
      </Card>
    </Page>

    <!-- 配置弹窗 -->
    <Modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="600px"
      @ok="handleSave"
      @cancel="handleCancel"
    >
      <Form :model="formData" layout="vertical">
        <Form.Item label="配置键" required>
          <Input 
            v-model:value="formData.config_key" 
            placeholder="请输入配置键"
            :disabled="isEdit"
          />
        </Form.Item>
        <Form.Item label="配置名称" required>
          <Input v-model:value="formData.config_name" placeholder="请输入配置名称" />
        </Form.Item>
        <Form.Item label="配置值">
          <Textarea 
            v-if="formData.config_type === 'text' || formData.config_type === 'json'"
            v-model:value="formData.config_value" 
            placeholder="请输入配置值"
            :rows="4"
          />
          <InputNumber 
            v-else-if="formData.config_type === 'number'"
            v-model:value="formData.config_value" 
            placeholder="请输入数字"
            style="width: 100%"
          />
          <Switch 
            v-else-if="formData.config_type === 'boolean'"
            v-model:checked="formData.config_value"
          />
          <Input 
            v-else
            v-model:value="formData.config_value" 
            placeholder="请输入配置值"
          />
        </Form.Item>
        <Form.Item label="配置类型" required>
          <Select v-model:value="formData.config_type" placeholder="选择配置类型">
            <Select.Option v-for="option in configTypeOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="描述">
          <Textarea v-model:value="formData.description" placeholder="请输入描述" :rows="3" />
        </Form.Item>
        <Form.Item label="是否公开">
          <Switch v-model:checked="formData.is_public" />
        </Form.Item>
        <Form.Item label="排序">
          <InputNumber v-model:value="formData.sort_order" :min="0" style="width: 100%" />
        </Form.Item>
      </Form>
    </Modal>
  </div>
</template>
