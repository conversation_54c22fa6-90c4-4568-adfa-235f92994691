<script setup lang="ts">
import { onMounted, reactive, ref, h } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Page } from '@vben/common-ui';
import { Button, Card, Form, InputNumber, Select, Table, message } from 'ant-design-vue';
import { getLiveRoomProducts, addLiveRoomProducts, TopLiveRoomProducts, SortLiveRoomProducts, deleteLiveRoomProduct, type LiveRoomProduct } from '#/api/live-room';
import { getItineraryOptions } from '#/api/itinerary';

const route = useRoute();
const router = useRouter();
const roomId = route.params.id as string;

const loading = ref(false);
const products = ref<LiveRoomProduct[]>([]);

// 添加商品表单
const addForm = reactive({
  productId: undefined as string | undefined,
  special_price: undefined as number | undefined,
  discount_rate: undefined as number | undefined,
  stock_limit: 20,
  is_featured: 0,
  sort_order: 1,
});

const itineraryOptions = ref<{ label: string; value: string }[]>([]);

async function fetchProducts() {
  loading.value = true;
  try {
    const res = await getLiveRoomProducts(roomId);
    products.value = res.list || [];
  } catch (e) {
    message.error('获取商品列表失败');
  } finally {
    loading.value = false;
  }
}

async function fetchItineraryOptions(keyword = '') {
  const res = await getItineraryOptions({ keyword });
  itineraryOptions.value = (res.list || []).map((i) => ({ label: i.label || `${i.title} / ¥${i.price}` , value: String(i.id) }));
}

async function onAddProduct() {
  if (!addForm.productId) return message.warning('请选择线路');
  const payload: LiveRoomProduct = {
    product_id: addForm.productId,
    product_type: 'itinerary',
    special_price: addForm.special_price ?? '',
    discount_rate: addForm.discount_rate ?? '',
    stock_limit: addForm.stock_limit,
    is_featured: addForm.is_featured,
    sort_order: addForm.sort_order,
  };
  try {
    await addLiveRoomProducts(roomId, payload);
    message.success('添加成功');
    fetchProducts();
  } catch (e) {
    console.log('添加失败');
  }
}

async function handleFeatured(record: LiveRoomProduct) {
  console.log('record:', record);
  
  try {
    await TopLiveRoomProducts(roomId, record.productId);
    message.success('置顶成功');
    fetchProducts();
  } catch (e) {
    console.log('置顶失败');
  }
}

async function handleSortOrder(record: LiveRoomProduct, value: any) {
  try {
    await SortLiveRoomProducts(roomId, record.productId, value);
    message.success('排序更新成功');
    fetchProducts();
  } catch (e) {
    console.log('排序更新失败');
  }
}
async function handleDelete(record: LiveRoomProduct) {
  try {
    if (!confirm('确认删除吗？')) return;
    await deleteLiveRoomProduct(roomId, record.productId);
    message.success('删除成功');
    fetchProducts();
  } catch (e) {
    message.error('删除失败');
  }
}

const columns = ref([
                 { title: '产品ID', dataIndex: 'productId', key: 'productId', width:80 },
                 { title: '名称', dataIndex: ['itinerary','title'], key: 'itinerary.title' },
                 { title: '原价', dataIndex: ['itinerary', 'price'], key: 'itinerary.price', width: 120 },
                 { title: '类型', dataIndex: 'productType', key: 'productType', width: 120 },
                 { title: '专享价', dataIndex: 'specialPrice', key: 'specialPrice', width: 120 },
                 { title: '折扣', dataIndex: 'discountRate', key: 'discountRate', width: 120 },
                 { title: '限量', dataIndex: 'stockLimit', key: 'stockLimit', width: 120 },
                 { title: '置顶', dataIndex: 'isFeatured', key: 'isFeatured', width: 120 ,  
                    customRender: ({ record }: { record: any }) => {
      return h(
        Button, 
        {
          type: 'link',
          onClick: () => handleFeatured(record),
        },
        record.isFeatured == 1 ? '是' : '否'
      )
    },},
                 { title: '排序', dataIndex: 'sortOrder', key: 'sortOrder', width: 120 ,
                    customRender: ({ record }: { record: any }) => {
      return h(
        InputNumber, 
        {
          value: record.sortOrder,
          // 失去焦点时更新
          "onUpdate:value": (value) => handleSortOrder(record, value),
        }
      )
    },},
                 { title: '操作', width: 80 ,
                    customRender: ({ record }: { record: any }) => {
      return h(
        Button, 
        {
          type: 'link',
          onClick: () => handleDelete(record),
        },
        '删除'
      )
    },},
               
]);

onMounted(() => {
  fetchProducts();
  fetchItineraryOptions();
});
</script>

<template>
  <div>
    <Page title="直播间商品管理">
      <Card title="添加商品">
        <Form layout="inline">
          <Form.Item label="选择线路">
            <Select
              v-model:value="addForm.productId"
              show-search
              :filter-option="false"
              style="width: 360px;"
              :options="itineraryOptions"
              @search="fetchItineraryOptions"
            />
          </Form.Item>
          <Form.Item label="专享价">
            <InputNumber v-model:value="addForm.special_price" :min="0" />
          </Form.Item>
          <Form.Item label="折扣(%)">
            <InputNumber v-model:value="addForm.discount_rate" :min="0" :max="100" />
          </Form.Item>
          <Form.Item label="限量">
            <InputNumber v-model:value="addForm.stock_limit" :min="0" />
          </Form.Item>
          <Form.Item label="置顶">
            <Select v-model:value="addForm.is_featured" style="width: 120px" :options="[{ label: '否', value: 0 }, { label: '是', value: 1 }]" />
          </Form.Item>
          <Form.Item label="排序">
            <InputNumber v-model:value="addForm.sort_order" :min="1" />
          </Form.Item>
          <Form.Item>
            <Button type="primary" @click="onAddProduct">添加</Button>
            <Button class="ml-2" @click="() => router.back()">返回</Button>
          </Form.Item>
        </Form>
      </Card>

      <Card title="直播间商品列表" class="mt-4">
        <Table :data-source="products" :loading="loading" row-key="id"
        :columns="columns" />
      </Card>
    </Page>
  </div>
</template>

