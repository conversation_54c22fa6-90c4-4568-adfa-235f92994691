/**
 * 配置类型枚举
 */
export type ConfigType = 'string' | 'number' | 'boolean' | 'json' | 'text' | 'image' | 'richtext';

/**
 * 配置分组枚举
 */
export type ConfigGroup = 'system' | 'app' | 'user' | 'business';

/**
 * 配置项接口
 */
export interface ConfigItem {
  id?: string | number;
  config_key: string;
  group_id: number;
  config_name: string;
  config_value?: string;
  config_type: ConfigType;
  config_group: ConfigGroup;
  description?: string;
  is_public?: boolean;
  sort_order?: number;
  created_at?: string;
  updated_at?: string;
}

/**
 * 创建配置请求参数
 */
export interface CreateConfigParams {
  config_key: string;
  config_name: string;
  config_value?: string;
  config_type: ConfigType;
  config_group: ConfigGroup;
  description?: string;
  is_public?: boolean;
  sort_order?: number;
}

/**
 * 更新配置请求参数
 */
export interface UpdateConfigParams {
  config_name?: string;
  config_value?: string;
  config_type?: ConfigType;
  config_group?: ConfigGroup;
  description?: string;
  is_public?: boolean;
  sort_order?: number;
}

/**
 * 配置列表查询参数
 */
export interface ConfigListParams {
  page?: number;
  limit?: number;
  config_key?: string;
  config_name?: string;
  config_group?: ConfigGroup;
  config_type?: ConfigType;
  is_public?: boolean;
}

/**
 * 配置列表响应数据
 */
export interface ConfigListResult {
  configs: { system: ConfigItem[] };
  total: number;
  page: number;
  limit: number;
}

/**
 * 配置类型选项
 */
export interface ConfigTypeOption {
  value: ConfigType;
  label: string;
}

/**
 * 配置分组选项
 */
export interface ConfigGroupOption {
  value: ConfigGroup;
  label: string;
}

/**
 * 配置分组详情
 */
export interface ConfigGroupDetail {
  id?: string | number;
  group_key: ConfigGroup;
  group_name: string;
  description?: string;
  icon?: string;
  sort_order?: number;
  config_count?: number;
  created_at?: string;
  updated_at?: string;
}

/**
 * 创建配置分组请求参数
 */
export interface CreateConfigGroupParams {
  group_key: ConfigGroup;
  group_name: string;
  description?: string;
  icon?: string;
  sort_order?: number;
}

/**
 * 更新配置分组请求参数
 */
export interface UpdateConfigGroupParams {
  group_name?: string;
  description?: string;
  icon?: string;
  sort_order?: number;
}

/**
 * 配置分组列表响应数据
 */
export interface ConfigGroupListResult {
  groups: ConfigGroupDetail[];
  total: number;
}

/**
 * 公开配置响应数据
 */
export interface PublicConfigResult {
  [key: string]: any;
}
