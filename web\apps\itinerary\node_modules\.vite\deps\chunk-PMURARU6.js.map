{"version": 3, "sources": ["../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-mentions/src/util.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/hooks/useMenuContext.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/layout/injectionKey.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/hooks/useKeyPath.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/hooks/useDirectionStyle.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/MenuItem.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/placements.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/PopupTrigger.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/SubMenuList.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/InlineSubMenuList.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/SubMenu.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/ItemGroup.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/Divider.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/hooks/useItems.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/style/horizontal.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/style/rtl.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/style/theme.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/style/vertical.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/style/index.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/OverrideContext.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/src/Menu.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/menu/index.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-mentions/src/MentionsContext.js", "../../../../../node_modules/.pnpm/throttle-debounce@5.0.2/node_modules/throttle-debounce/throttle.js", "../../../../../node_modules/.pnpm/throttle-debounce@5.0.2/node_modules/throttle-debounce/debounce.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/spin/style/index.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/spin/Spin.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/spin/index.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-mentions/src/DropdownMenu.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-mentions/src/KeywordTrigger.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-mentions/src/mentionsProps.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-mentions/src/Mentions.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-mentions/src/Option.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/vc-mentions/index.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/mentions/style/index.js", "../../../../../node_modules/.pnpm/ant-design-vue@4.2.6_vue@3.5.17_typescript@5.8.3_/node_modules/ant-design-vue/es/mentions/index.js"], "sourcesContent": ["/**\n * Cut input selection into 2 part and return text before selection start\n */\nexport function getBeforeSelectionText(input) {\n  const {\n    selectionStart\n  } = input;\n  return input.value.slice(0, selectionStart);\n}\n/**\n * Find the last match prefix index\n */\nexport function getLastMeasureIndex(text) {\n  let prefix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  const prefixList = Array.isArray(prefix) ? prefix : [prefix];\n  return prefixList.reduce((lastMatch, prefixStr) => {\n    const lastIndex = text.lastIndexOf(prefixStr);\n    if (lastIndex > lastMatch.location) {\n      return {\n        location: lastIndex,\n        prefix: prefixStr\n      };\n    }\n    return lastMatch;\n  }, {\n    location: -1,\n    prefix: ''\n  });\n}\nfunction lower(char) {\n  return (char || '').toLowerCase();\n}\nfunction reduceText(text, targetText, split) {\n  const firstChar = text[0];\n  if (!firstChar || firstChar === split) {\n    return text;\n  }\n  // Reuse rest text as it can\n  let restText = text;\n  const targetTextLen = targetText.length;\n  for (let i = 0; i < targetTextLen; i += 1) {\n    if (lower(restText[i]) !== lower(targetText[i])) {\n      restText = restText.slice(i);\n      break;\n    } else if (i === targetTextLen - 1) {\n      restText = restText.slice(targetTextLen);\n    }\n  }\n  return restText;\n}\n/**\n * Paint targetText into current text:\n *  text: little@litest\n *  targetText: light\n *  => little @light test\n */\nexport function replaceWithMeasure(text, measureConfig) {\n  const {\n    measureLocation,\n    prefix,\n    targetText,\n    selectionStart,\n    split\n  } = measureConfig;\n  // Before text will append one space if have other text\n  let beforeMeasureText = text.slice(0, measureLocation);\n  if (beforeMeasureText[beforeMeasureText.length - split.length] === split) {\n    beforeMeasureText = beforeMeasureText.slice(0, beforeMeasureText.length - split.length);\n  }\n  if (beforeMeasureText) {\n    beforeMeasureText = `${beforeMeasureText}${split}`;\n  }\n  // Cut duplicate string with current targetText\n  let restText = reduceText(text.slice(selectionStart), targetText.slice(selectionStart - measureLocation - prefix.length), split);\n  if (restText.slice(0, split.length) === split) {\n    restText = restText.slice(split.length);\n  }\n  const connectedStartText = `${beforeMeasureText}${prefix}${targetText}${split}`;\n  return {\n    text: `${connectedStartText}${restText}`,\n    selectionLocation: connectedStartText.length\n  };\n}\nexport function setInputSelection(input, location) {\n  input.setSelectionRange(location, location);\n  /**\n   * Reset caret into view.\n   * Since this function always called by user control, it's safe to focus element.\n   */\n  input.blur();\n  input.focus();\n}\nexport function validateSearch(text, props) {\n  const {\n    split\n  } = props;\n  return !split || text.indexOf(split) === -1;\n}\nexport function filterOption(input, _ref) {\n  let {\n    value = ''\n  } = _ref;\n  const lowerCase = input.toLowerCase();\n  return value.toLowerCase().indexOf(lowerCase) !== -1;\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { defineComponent, inject, provide, toRef } from 'vue';\nconst MenuContextKey = Symbol('menuContextKey');\nconst useProvideMenu = props => {\n  provide(MenuContextKey, props);\n};\nconst useInjectMenu = () => {\n  return inject(MenuContextKey);\n};\nconst ForceRenderKey = Symbol('ForceRenderKey');\nexport const useProvideForceRender = forceRender => {\n  provide(ForceRenderKey, forceRender);\n};\nexport const useInjectForceRender = () => {\n  return inject(ForceRenderKey, false);\n};\nconst MenuFirstLevelContextKey = Symbol('menuFirstLevelContextKey');\nconst useProvideFirstLevel = firstLevel => {\n  provide(MenuFirstLevelContextKey, firstLevel);\n};\nconst useInjectFirstLevel = () => {\n  return inject(MenuFirstLevelContextKey, true);\n};\nconst MenuContextProvider = defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'MenuContextProvider',\n  inheritAttrs: false,\n  props: {\n    mode: {\n      type: String,\n      default: undefined\n    },\n    overflowDisabled: {\n      type: Boolean,\n      default: undefined\n    }\n  },\n  setup(props, _ref) {\n    let {\n      slots\n    } = _ref;\n    const menuContext = useInjectMenu();\n    const newContext = _extends({}, menuContext);\n    // 确保传入的属性不会动态增删\n    // 不需要 watch 变化\n    if (props.mode !== undefined) {\n      newContext.mode = toRef(props, 'mode');\n    }\n    if (props.overflowDisabled !== undefined) {\n      newContext.overflowDisabled = toRef(props, 'overflowDisabled');\n    }\n    useProvideMenu(newContext);\n    return () => {\n      var _a;\n      return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n    };\n  }\n});\nexport { useProvideMenu, MenuContextKey, useInjectMenu, MenuFirstLevelContextKey, useProvideFirstLevel, useInjectFirstLevel, MenuContextProvider };\nexport default useProvideMenu;", "export const SiderCollapsedKey = Symbol('siderCollapsed');\nexport const SiderHookProviderKey = Symbol('siderHookProvider');", "import { computed, inject, provide, defineComponent } from 'vue';\nexport const OVERFLOW_KEY = '$$__vc-menu-more__key';\nconst KeyPathContext = Symbol('KeyPathContext');\nconst useInjectKeyPath = () => {\n  return inject(KeyPathContext, {\n    parentEventKeys: computed(() => []),\n    parentKeys: computed(() => []),\n    parentInfo: {}\n  });\n};\nconst useProvideKeyPath = (eventKey, key, menuInfo) => {\n  const {\n    parentEventKeys,\n    parentKeys\n  } = useInjectKeyPath();\n  const eventKeys = computed(() => [...parentEventKeys.value, eventKey]);\n  const keys = computed(() => [...parentKeys.value, key]);\n  provide(KeyPathContext, {\n    parentEventKeys: eventKeys,\n    parentKeys: keys,\n    parentInfo: menuInfo\n  });\n  return keys;\n};\nconst measure = Symbol('measure');\nexport const PathContext = defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  setup(_props, _ref) {\n    let {\n      slots\n    } = _ref;\n    // 不需要响应式\n    provide(measure, true);\n    return () => {\n      var _a;\n      return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n    };\n  }\n});\nexport const useMeasure = () => {\n  return inject(measure, false);\n};\nexport { useProvideKeyPath, useInjectKeyPath, KeyPathContext };\nexport default useProvideKeyPath;", "import { computed } from 'vue';\nimport { useInjectMenu } from './useMenuContext';\nexport default function useDirectionStyle(level) {\n  const {\n    mode,\n    rtl,\n    inlineIndent\n  } = useInjectMenu();\n  return computed(() => mode.value !== 'inline' ? null : rtl.value ? {\n    paddingRight: `${level.value * inlineIndent.value}px`\n  } : {\n    paddingLeft: `${level.value * inlineIndent.value}px`\n  });\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { createVNode as _createVNode } from \"vue\";\nimport { flattenChildren, isValidElement } from '../../_util/props-util';\nimport PropTypes from '../../_util/vue-types';\nimport { computed, defineComponent, getCurrentInstance, onBeforeUnmount, shallowRef, watch } from 'vue';\nimport { useInjectKeyPath, useMeasure } from './hooks/useKeyPath';\nimport { useInjectFirstLevel, useInjectMenu } from './hooks/useMenuContext';\nimport { cloneElement } from '../../_util/vnode';\nimport Tooltip from '../../tooltip';\nimport KeyCode from '../../_util/KeyCode';\nimport useDirectionStyle from './hooks/useDirectionStyle';\nimport Overflow from '../../vc-overflow';\nimport devWarning from '../../vc-util/devWarning';\nimport { objectType } from '../../_util/type';\nlet indexGuid = 0;\nexport const menuItemProps = () => ({\n  id: String,\n  role: String,\n  disabled: Boolean,\n  danger: Boolean,\n  title: {\n    type: [String, Boolean],\n    default: undefined\n  },\n  icon: PropTypes.any,\n  onMouseenter: Function,\n  onMouseleave: Function,\n  onClick: Function,\n  onKeydown: Function,\n  onFocus: Function,\n  // Internal user prop\n  originItemValue: objectType()\n});\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'AMenuItem',\n  inheritAttrs: false,\n  props: menuItemProps(),\n  slots: Object,\n  setup(props, _ref) {\n    let {\n      slots,\n      emit,\n      attrs\n    } = _ref;\n    const instance = getCurrentInstance();\n    const isMeasure = useMeasure();\n    const key = typeof instance.vnode.key === 'symbol' ? String(instance.vnode.key) : instance.vnode.key;\n    devWarning(typeof instance.vnode.key !== 'symbol', 'MenuItem', `MenuItem \\`:key=\"${String(key)}\"\\` not support Symbol type`);\n    const eventKey = `menu_item_${++indexGuid}_$$_${key}`;\n    const {\n      parentEventKeys,\n      parentKeys\n    } = useInjectKeyPath();\n    const {\n      prefixCls,\n      activeKeys,\n      disabled,\n      changeActiveKeys,\n      rtl,\n      inlineCollapsed,\n      siderCollapsed,\n      onItemClick,\n      selectedKeys,\n      registerMenuInfo,\n      unRegisterMenuInfo\n    } = useInjectMenu();\n    const firstLevel = useInjectFirstLevel();\n    const isActive = shallowRef(false);\n    const keysPath = computed(() => {\n      return [...parentKeys.value, key];\n    });\n    // const keysPath = computed(() => [...parentEventKeys.value, eventKey]);\n    const menuInfo = {\n      eventKey,\n      key,\n      parentEventKeys,\n      parentKeys,\n      isLeaf: true\n    };\n    registerMenuInfo(eventKey, menuInfo);\n    onBeforeUnmount(() => {\n      unRegisterMenuInfo(eventKey);\n    });\n    watch(activeKeys, () => {\n      isActive.value = !!activeKeys.value.find(val => val === key);\n    }, {\n      immediate: true\n    });\n    const mergedDisabled = computed(() => disabled.value || props.disabled);\n    const selected = computed(() => selectedKeys.value.includes(key));\n    const classNames = computed(() => {\n      const itemCls = `${prefixCls.value}-item`;\n      return {\n        [`${itemCls}`]: true,\n        [`${itemCls}-danger`]: props.danger,\n        [`${itemCls}-active`]: isActive.value,\n        [`${itemCls}-selected`]: selected.value,\n        [`${itemCls}-disabled`]: mergedDisabled.value\n      };\n    });\n    const getEventInfo = e => {\n      return {\n        key,\n        eventKey,\n        keyPath: keysPath.value,\n        eventKeyPath: [...parentEventKeys.value, eventKey],\n        domEvent: e,\n        item: _extends(_extends({}, props), attrs)\n      };\n    };\n    // ============================ Events ============================\n    const onInternalClick = e => {\n      if (mergedDisabled.value) {\n        return;\n      }\n      const info = getEventInfo(e);\n      emit('click', e);\n      onItemClick(info);\n    };\n    const onMouseEnter = event => {\n      if (!mergedDisabled.value) {\n        changeActiveKeys(keysPath.value);\n        emit('mouseenter', event);\n      }\n    };\n    const onMouseLeave = event => {\n      if (!mergedDisabled.value) {\n        changeActiveKeys([]);\n        emit('mouseleave', event);\n      }\n    };\n    const onInternalKeyDown = e => {\n      emit('keydown', e);\n      if (e.which === KeyCode.ENTER) {\n        const info = getEventInfo(e);\n        // Legacy. Key will also trigger click event\n        emit('click', e);\n        onItemClick(info);\n      }\n    };\n    /**\n     * Used for accessibility. Helper will focus element without key board.\n     * We should manually trigger an active\n     */\n    const onInternalFocus = e => {\n      changeActiveKeys(keysPath.value);\n      emit('focus', e);\n    };\n    const renderItemChildren = (icon, children) => {\n      const wrapNode = _createVNode(\"span\", {\n        \"class\": `${prefixCls.value}-title-content`\n      }, [children]);\n      // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span\n      // ref: https://github.com/ant-design/ant-design/pull/23456\n      if (!icon || isValidElement(children) && children.type === 'span') {\n        if (children && inlineCollapsed.value && firstLevel && typeof children === 'string') {\n          return _createVNode(\"div\", {\n            \"class\": `${prefixCls.value}-inline-collapsed-noicon`\n          }, [children.charAt(0)]);\n        }\n      }\n      return wrapNode;\n    };\n    // ========================== DirectionStyle ==========================\n    const directionStyle = useDirectionStyle(computed(() => keysPath.value.length));\n    return () => {\n      var _a, _b, _c, _d, _e;\n      if (isMeasure) return null;\n      const title = (_a = props.title) !== null && _a !== void 0 ? _a : (_b = slots.title) === null || _b === void 0 ? void 0 : _b.call(slots);\n      const children = flattenChildren((_c = slots.default) === null || _c === void 0 ? void 0 : _c.call(slots));\n      const childrenLength = children.length;\n      let tooltipTitle = title;\n      if (typeof title === 'undefined') {\n        tooltipTitle = firstLevel && childrenLength ? children : '';\n      } else if (title === false) {\n        tooltipTitle = '';\n      }\n      const tooltipProps = {\n        title: tooltipTitle\n      };\n      if (!siderCollapsed.value && !inlineCollapsed.value) {\n        tooltipProps.title = null;\n        // Reset `visible` to fix control mode tooltip display not correct\n        // ref: https://github.com/ant-design/ant-design/issues/16742\n        tooltipProps.open = false;\n      }\n      // ============================ Render ============================\n      const optionRoleProps = {};\n      if (props.role === 'option') {\n        optionRoleProps['aria-selected'] = selected.value;\n      }\n      const icon = (_d = props.icon) !== null && _d !== void 0 ? _d : (_e = slots.icon) === null || _e === void 0 ? void 0 : _e.call(slots, props);\n      return _createVNode(Tooltip, _objectSpread(_objectSpread({}, tooltipProps), {}, {\n        \"placement\": rtl.value ? 'left' : 'right',\n        \"overlayClassName\": `${prefixCls.value}-inline-collapsed-tooltip`\n      }), {\n        default: () => [_createVNode(Overflow.Item, _objectSpread(_objectSpread(_objectSpread({\n          \"component\": \"li\"\n        }, attrs), {}, {\n          \"id\": props.id,\n          \"style\": _extends(_extends({}, attrs.style || {}), directionStyle.value),\n          \"class\": [classNames.value, {\n            [`${attrs.class}`]: !!attrs.class,\n            [`${prefixCls.value}-item-only-child`]: (icon ? childrenLength + 1 : childrenLength) === 1\n          }],\n          \"role\": props.role || 'menuitem',\n          \"tabindex\": props.disabled ? null : -1,\n          \"data-menu-id\": key,\n          \"aria-disabled\": props.disabled\n        }, optionRoleProps), {}, {\n          \"onMouseenter\": onMouseEnter,\n          \"onMouseleave\": onMouseLeave,\n          \"onClick\": onInternalClick,\n          \"onKeydown\": onInternalKeyDown,\n          \"onFocus\": onInternalFocus,\n          \"title\": typeof title === 'string' ? title : undefined\n        }), {\n          default: () => [cloneElement(typeof icon === 'function' ? icon(props.originItemValue) : icon, {\n            class: `${prefixCls.value}-item-icon`\n          }, false), renderItemChildren(icon, children)]\n        })]\n      });\n    };\n  }\n});", "const autoAdjustOverflow = {\n  adjustX: 1,\n  adjustY: 1\n};\nexport const placements = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -7]\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 7]\n  },\n  leftTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [-4, 0]\n  },\n  rightTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [4, 0]\n  }\n};\nexport const placementsRtl = {\n  topLeft: {\n    points: ['bl', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, -7]\n  },\n  bottomLeft: {\n    points: ['tl', 'bl'],\n    overflow: autoAdjustOverflow,\n    offset: [0, 7]\n  },\n  rightTop: {\n    points: ['tr', 'tl'],\n    overflow: autoAdjustOverflow,\n    offset: [-4, 0]\n  },\n  leftTop: {\n    points: ['tl', 'tr'],\n    overflow: autoAdjustOverflow,\n    offset: [4, 0]\n  }\n};\nexport default placements;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { createVNode as _createVNode, resolveDirective as _resolveDirective } from \"vue\";\nimport Trigger from '../../vc-trigger';\nimport { computed, defineComponent, onBeforeUnmount, shallowRef, watch } from 'vue';\nimport { useInjectForceRender, useInjectMenu } from './hooks/useMenuContext';\nimport { placements, placementsRtl } from './placements';\nimport raf from '../../_util/raf';\nimport classNames from '../../_util/classNames';\nimport { getTransitionProps } from '../../_util/transition';\nconst popupPlacementMap = {\n  horizontal: 'bottomLeft',\n  vertical: 'rightTop',\n  'vertical-left': 'rightTop',\n  'vertical-right': 'leftTop'\n};\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'PopupTrigger',\n  inheritAttrs: false,\n  props: {\n    prefixCls: String,\n    mode: String,\n    visible: Boolean,\n    // popup: React.ReactNode;\n    popupClassName: String,\n    popupOffset: Array,\n    disabled: Boolean,\n    onVisibleChange: Function\n  },\n  slots: Object,\n  emits: ['visibleChange'],\n  setup(props, _ref) {\n    let {\n      slots,\n      emit\n    } = _ref;\n    const innerVisible = shallowRef(false);\n    const {\n      getPopupContainer,\n      rtl,\n      subMenuOpenDelay,\n      subMenuCloseDelay,\n      builtinPlacements,\n      triggerSubMenuAction,\n      forceSubMenuRender,\n      motion,\n      defaultMotions,\n      rootClassName\n    } = useInjectMenu();\n    const forceRender = useInjectForceRender();\n    const placement = computed(() => rtl.value ? _extends(_extends({}, placementsRtl), builtinPlacements.value) : _extends(_extends({}, placements), builtinPlacements.value));\n    const popupPlacement = computed(() => popupPlacementMap[props.mode]);\n    const visibleRef = shallowRef();\n    watch(() => props.visible, visible => {\n      raf.cancel(visibleRef.value);\n      visibleRef.value = raf(() => {\n        innerVisible.value = visible;\n      });\n    }, {\n      immediate: true\n    });\n    onBeforeUnmount(() => {\n      raf.cancel(visibleRef.value);\n    });\n    const onVisibleChange = visible => {\n      emit('visibleChange', visible);\n    };\n    const mergedMotion = computed(() => {\n      var _a, _b;\n      const m = motion.value || ((_a = defaultMotions.value) === null || _a === void 0 ? void 0 : _a[props.mode]) || ((_b = defaultMotions.value) === null || _b === void 0 ? void 0 : _b.other);\n      const res = typeof m === 'function' ? m() : m;\n      return res ? getTransitionProps(res.name, {\n        css: true\n      }) : undefined;\n    });\n    return () => {\n      const {\n        prefixCls,\n        popupClassName,\n        mode,\n        popupOffset,\n        disabled\n      } = props;\n      return _createVNode(Trigger, {\n        \"prefixCls\": prefixCls,\n        \"popupClassName\": classNames(`${prefixCls}-popup`, {\n          [`${prefixCls}-rtl`]: rtl.value\n        }, popupClassName, rootClassName.value),\n        \"stretch\": mode === 'horizontal' ? 'minWidth' : null,\n        \"getPopupContainer\": getPopupContainer.value,\n        \"builtinPlacements\": placement.value,\n        \"popupPlacement\": popupPlacement.value,\n        \"popupVisible\": innerVisible.value,\n        \"popupAlign\": popupOffset && {\n          offset: popupOffset\n        },\n        \"action\": disabled ? [] : [triggerSubMenuAction.value],\n        \"mouseEnterDelay\": subMenuOpenDelay.value,\n        \"mouseLeaveDelay\": subMenuCloseDelay.value,\n        \"onPopupVisibleChange\": onVisibleChange,\n        \"forceRender\": forceRender || forceSubMenuRender.value,\n        \"popupAnimation\": mergedMotion.value\n      }, {\n        popup: slots.popup,\n        default: slots.default\n      });\n    };\n  }\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { createVNode as _createVNode } from \"vue\";\nimport classNames from '../../_util/classNames';\nimport { useInjectMenu } from './hooks/useMenuContext';\nconst InternalSubMenuList = (_props, _ref) => {\n  let {\n    slots,\n    attrs\n  } = _ref;\n  var _a;\n  const {\n    prefixCls,\n    mode\n  } = useInjectMenu();\n  return _createVNode(\"ul\", _objectSpread(_objectSpread({}, attrs), {}, {\n    \"class\": classNames(prefixCls.value, `${prefixCls.value}-sub`, `${prefixCls.value}-${mode.value === 'inline' ? 'inline' : 'vertical'}`),\n    \"data-menu-list\": true\n  }), [(_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)]);\n};\nInternalSubMenuList.displayName = 'SubMenuList';\nexport default InternalSubMenuList;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { withDirectives as _withDirectives, createVNode as _createVNode, vShow as _vShow } from \"vue\";\nimport { computed, Transition, defineComponent, ref, watch } from 'vue';\nimport { useInjectMenu, MenuContextProvider } from './hooks/useMenuContext';\nimport SubMenuList from './SubMenuList';\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'InlineSubMenuList',\n  inheritAttrs: false,\n  props: {\n    id: String,\n    open: Boolean,\n    keyPath: Array\n  },\n  setup(props, _ref) {\n    let {\n      slots\n    } = _ref;\n    const fixedMode = computed(() => 'inline');\n    const {\n      motion,\n      mode,\n      defaultMotions\n    } = useInjectMenu();\n    const sameModeRef = computed(() => mode.value === fixedMode.value);\n    const destroy = ref(!sameModeRef.value);\n    const mergedOpen = computed(() => sameModeRef.value ? props.open : false);\n    // ================================= Effect =================================\n    // Reset destroy state when mode change back\n    watch(mode, () => {\n      if (sameModeRef.value) {\n        destroy.value = false;\n      }\n    }, {\n      flush: 'post'\n    });\n    const mergedMotion = computed(() => {\n      var _a, _b;\n      const m = motion.value || ((_a = defaultMotions.value) === null || _a === void 0 ? void 0 : _a[fixedMode.value]) || ((_b = defaultMotions.value) === null || _b === void 0 ? void 0 : _b.other);\n      const res = typeof m === 'function' ? m() : m;\n      return _extends(_extends({}, res), {\n        appear: props.keyPath.length <= 1\n      });\n    });\n    return () => {\n      var _a;\n      if (destroy.value) {\n        return null;\n      }\n      return _createVNode(MenuContextProvider, {\n        \"mode\": fixedMode.value\n      }, {\n        default: () => [_createVNode(Transition, mergedMotion.value, {\n          default: () => [_withDirectives(_createVNode(SubMenuList, {\n            \"id\": props.id\n          }, {\n            default: () => [(_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)]\n          }), [[_vShow, mergedOpen.value]])]\n        })]\n      });\n    };\n  }\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { resolveDirective as _resolveDirective, Fragment as _Fragment, createVNode as _createVNode } from \"vue\";\nimport PropTypes from '../../_util/vue-types';\nimport { computed, defineComponent, getCurrentInstance, shallowRef, watch, onBeforeUnmount } from 'vue';\nimport useProvideKeyPath, { useInjectKeyPath, useMeasure } from './hooks/useKeyPath';\nimport { useInjectMenu, useProvideFirstLevel, MenuContextProvider, useProvideForceRender, useInjectForceRender } from './hooks/useMenuContext';\nimport { getPropsSlot, isValidElement } from '../../_util/props-util';\nimport classNames from '../../_util/classNames';\nimport useDirectionStyle from './hooks/useDirectionStyle';\nimport PopupTrigger from './PopupTrigger';\nimport SubMenuList from './SubMenuList';\nimport InlineSubMenuList from './InlineSubMenuList';\nimport { cloneElement } from '../../_util/vnode';\nimport Overflow from '../../vc-overflow';\nimport devWarning from '../../vc-util/devWarning';\nimport isValid from '../../_util/isValid';\nimport { objectType } from '../../_util/type';\nlet indexGuid = 0;\nexport const subMenuProps = () => ({\n  icon: PropTypes.any,\n  title: PropTypes.any,\n  disabled: Boolean,\n  level: Number,\n  popupClassName: String,\n  popupOffset: Array,\n  internalPopupClose: Boolean,\n  eventKey: String,\n  expandIcon: Function,\n  theme: String,\n  onMouseenter: Function,\n  onMouseleave: Function,\n  onTitleClick: Function,\n  // Internal user prop\n  originItemValue: objectType()\n});\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'ASubMenu',\n  inheritAttrs: false,\n  props: subMenuProps(),\n  slots: Object,\n  setup(props, _ref) {\n    let {\n      slots,\n      attrs,\n      emit\n    } = _ref;\n    var _a, _b;\n    useProvideFirstLevel(false);\n    const isMeasure = useMeasure();\n    const instance = getCurrentInstance();\n    const vnodeKey = typeof instance.vnode.key === 'symbol' ? String(instance.vnode.key) : instance.vnode.key;\n    devWarning(typeof instance.vnode.key !== 'symbol', 'SubMenu', `SubMenu \\`:key=\"${String(vnodeKey)}\"\\` not support Symbol type`);\n    const key = isValid(vnodeKey) ? vnodeKey : `sub_menu_${++indexGuid}_$$_not_set_key`;\n    const eventKey = (_a = props.eventKey) !== null && _a !== void 0 ? _a : isValid(vnodeKey) ? `sub_menu_${++indexGuid}_$$_${vnodeKey}` : key;\n    const {\n      parentEventKeys,\n      parentInfo,\n      parentKeys\n    } = useInjectKeyPath();\n    const keysPath = computed(() => [...parentKeys.value, key]);\n    const childrenEventKeys = shallowRef([]);\n    const menuInfo = {\n      eventKey,\n      key,\n      parentEventKeys,\n      childrenEventKeys,\n      parentKeys\n    };\n    (_b = parentInfo.childrenEventKeys) === null || _b === void 0 ? void 0 : _b.value.push(eventKey);\n    onBeforeUnmount(() => {\n      var _a;\n      if (parentInfo.childrenEventKeys) {\n        parentInfo.childrenEventKeys.value = (_a = parentInfo.childrenEventKeys) === null || _a === void 0 ? void 0 : _a.value.filter(k => k != eventKey);\n      }\n    });\n    useProvideKeyPath(eventKey, key, menuInfo);\n    const {\n      prefixCls,\n      activeKeys,\n      disabled: contextDisabled,\n      changeActiveKeys,\n      mode,\n      inlineCollapsed,\n      openKeys,\n      overflowDisabled,\n      onOpenChange,\n      registerMenuInfo,\n      unRegisterMenuInfo,\n      selectedSubMenuKeys,\n      expandIcon: menuExpandIcon,\n      theme\n    } = useInjectMenu();\n    const hasKey = vnodeKey !== undefined && vnodeKey !== null;\n    // If not set key, use forceRender = true for children\n    // 如果没有 key，强制 render 子元素\n    const forceRender = !isMeasure && (useInjectForceRender() || !hasKey);\n    useProvideForceRender(forceRender);\n    if (isMeasure && hasKey || !isMeasure && !hasKey || forceRender) {\n      registerMenuInfo(eventKey, menuInfo);\n      onBeforeUnmount(() => {\n        unRegisterMenuInfo(eventKey);\n      });\n    }\n    const subMenuPrefixCls = computed(() => `${prefixCls.value}-submenu`);\n    const mergedDisabled = computed(() => contextDisabled.value || props.disabled);\n    const elementRef = shallowRef();\n    const popupRef = shallowRef();\n    // // ================================ Icon ================================\n    // const mergedItemIcon = itemIcon || contextItemIcon;\n    // const mergedExpandIcon = expandIcon || contextExpandIcon;\n    // ================================ Open ================================\n    const originOpen = computed(() => openKeys.value.includes(key));\n    const open = computed(() => !overflowDisabled.value && originOpen.value);\n    // =============================== Select ===============================\n    const childrenSelected = computed(() => {\n      return selectedSubMenuKeys.value.includes(key);\n    });\n    const isActive = shallowRef(false);\n    watch(activeKeys, () => {\n      isActive.value = !!activeKeys.value.find(val => val === key);\n    }, {\n      immediate: true\n    });\n    // =============================== Events ===============================\n    // >>>> Title click\n    const onInternalTitleClick = e => {\n      // Skip if disabled\n      if (mergedDisabled.value) {\n        return;\n      }\n      emit('titleClick', e, key);\n      // Trigger open by click when mode is `inline`\n      if (mode.value === 'inline') {\n        onOpenChange(key, !originOpen.value);\n      }\n    };\n    const onMouseEnter = event => {\n      if (!mergedDisabled.value) {\n        changeActiveKeys(keysPath.value);\n        emit('mouseenter', event);\n      }\n    };\n    const onMouseLeave = event => {\n      if (!mergedDisabled.value) {\n        changeActiveKeys([]);\n        emit('mouseleave', event);\n      }\n    };\n    // ========================== DirectionStyle ==========================\n    const directionStyle = useDirectionStyle(computed(() => keysPath.value.length));\n    // >>>>> Visible change\n    const onPopupVisibleChange = newVisible => {\n      if (mode.value !== 'inline') {\n        onOpenChange(key, newVisible);\n      }\n    };\n    /**\n     * Used for accessibility. Helper will focus element without key board.\n     * We should manually trigger an active\n     */\n    const onInternalFocus = () => {\n      changeActiveKeys(keysPath.value);\n    };\n    // =============================== Render ===============================\n    const popupId = eventKey && `${eventKey}-popup`;\n    const popupClassName = computed(() => classNames(prefixCls.value, `${prefixCls.value}-${props.theme || theme.value}`, props.popupClassName));\n    const renderTitle = (title, icon) => {\n      if (!icon) {\n        return inlineCollapsed.value && !parentKeys.value.length && title && typeof title === 'string' ? _createVNode(\"div\", {\n          \"class\": `${prefixCls.value}-inline-collapsed-noicon`\n        }, [title.charAt(0)]) : _createVNode(\"span\", {\n          \"class\": `${prefixCls.value}-title-content`\n        }, [title]);\n      }\n      // inline-collapsed.md demo 依赖 span 来隐藏文字,有 icon 属性，则内部包裹一个 span\n      // ref: https://github.com/ant-design/ant-design/pull/23456\n      const titleIsSpan = isValidElement(title) && title.type === 'span';\n      return _createVNode(_Fragment, null, [cloneElement(typeof icon === 'function' ? icon(props.originItemValue) : icon, {\n        class: `${prefixCls.value}-item-icon`\n      }, false), titleIsSpan ? title : _createVNode(\"span\", {\n        \"class\": `${prefixCls.value}-title-content`\n      }, [title])]);\n    };\n    // Cache mode if it change to `inline` which do not have popup motion\n    const triggerModeRef = computed(() => {\n      return mode.value !== 'inline' && keysPath.value.length > 1 ? 'vertical' : mode.value;\n    });\n    const renderMode = computed(() => mode.value === 'horizontal' ? 'vertical' : mode.value);\n    const subMenuTriggerModeRef = computed(() => triggerModeRef.value === 'horizontal' ? 'vertical' : triggerModeRef.value);\n    const baseTitleNode = () => {\n      var _a, _b;\n      const subMenuPrefixClsValue = subMenuPrefixCls.value;\n      const icon = (_a = props.icon) !== null && _a !== void 0 ? _a : (_b = slots.icon) === null || _b === void 0 ? void 0 : _b.call(slots, props);\n      const expandIcon = props.expandIcon || slots.expandIcon || menuExpandIcon.value;\n      const title = renderTitle(getPropsSlot(slots, props, 'title'), icon);\n      return _createVNode(\"div\", {\n        \"style\": directionStyle.value,\n        \"class\": `${subMenuPrefixClsValue}-title`,\n        \"tabindex\": mergedDisabled.value ? null : -1,\n        \"ref\": elementRef,\n        \"title\": typeof title === 'string' ? title : null,\n        \"data-menu-id\": key,\n        \"aria-expanded\": open.value,\n        \"aria-haspopup\": true,\n        \"aria-controls\": popupId,\n        \"aria-disabled\": mergedDisabled.value,\n        \"onClick\": onInternalTitleClick,\n        \"onFocus\": onInternalFocus\n      }, [title, mode.value !== 'horizontal' && expandIcon ? expandIcon(_extends(_extends({}, props), {\n        isOpen: open.value\n      })) : _createVNode(\"i\", {\n        \"class\": `${subMenuPrefixClsValue}-arrow`\n      }, null)]);\n    };\n    return () => {\n      var _a;\n      if (isMeasure) {\n        if (!hasKey) {\n          return null;\n        }\n        return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n      }\n      const subMenuPrefixClsValue = subMenuPrefixCls.value;\n      let titleNode = () => null;\n      if (!overflowDisabled.value && mode.value !== 'inline') {\n        const popupOffset = mode.value === 'horizontal' ? [0, 8] : [10, 0];\n        titleNode = () => _createVNode(PopupTrigger, {\n          \"mode\": triggerModeRef.value,\n          \"prefixCls\": subMenuPrefixClsValue,\n          \"visible\": !props.internalPopupClose && open.value,\n          \"popupClassName\": popupClassName.value,\n          \"popupOffset\": props.popupOffset || popupOffset,\n          \"disabled\": mergedDisabled.value,\n          \"onVisibleChange\": onPopupVisibleChange\n        }, {\n          default: () => [baseTitleNode()],\n          popup: () => _createVNode(MenuContextProvider, {\n            \"mode\": subMenuTriggerModeRef.value\n          }, {\n            default: () => [_createVNode(SubMenuList, {\n              \"id\": popupId,\n              \"ref\": popupRef\n            }, {\n              default: slots.default\n            })]\n          })\n        });\n      } else {\n        // 包裹一层，保持结构一致，防止动画丢失\n        // https://github.com/vueComponent/ant-design-vue/issues/4325\n        titleNode = () => _createVNode(PopupTrigger, null, {\n          default: baseTitleNode\n        });\n      }\n      return _createVNode(MenuContextProvider, {\n        \"mode\": renderMode.value\n      }, {\n        default: () => [_createVNode(Overflow.Item, _objectSpread(_objectSpread({\n          \"component\": \"li\"\n        }, attrs), {}, {\n          \"role\": \"none\",\n          \"class\": classNames(subMenuPrefixClsValue, `${subMenuPrefixClsValue}-${mode.value}`, attrs.class, {\n            [`${subMenuPrefixClsValue}-open`]: open.value,\n            [`${subMenuPrefixClsValue}-active`]: isActive.value,\n            [`${subMenuPrefixClsValue}-selected`]: childrenSelected.value,\n            [`${subMenuPrefixClsValue}-disabled`]: mergedDisabled.value\n          }),\n          \"onMouseenter\": onMouseEnter,\n          \"onMouseleave\": onMouseLeave,\n          \"data-submenu-id\": key\n        }), {\n          default: () => {\n            return _createVNode(_Fragment, null, [titleNode(), !overflowDisabled.value && _createVNode(InlineSubMenuList, {\n              \"id\": popupId,\n              \"open\": open.value,\n              \"keyPath\": keysPath.value\n            }, {\n              default: slots.default\n            })]);\n          }\n        })]\n      });\n    };\n  }\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { createVNode as _createVNode } from \"vue\";\nimport { getPropsSlot } from '../../_util/props-util';\nimport { computed, defineComponent } from 'vue';\nimport PropTypes from '../../_util/vue-types';\nimport { useInjectMenu } from './hooks/useMenuContext';\nimport { useMeasure } from './hooks/useKeyPath';\nimport { objectType } from '../../_util/type';\nexport const menuItemGroupProps = () => ({\n  title: PropTypes.any,\n  // Internal user prop\n  originItemValue: objectType()\n});\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'AMenuItemGroup',\n  inheritAttrs: false,\n  props: menuItemGroupProps(),\n  slots: Object,\n  setup(props, _ref) {\n    let {\n      slots,\n      attrs\n    } = _ref;\n    const {\n      prefixCls\n    } = useInjectMenu();\n    const groupPrefixCls = computed(() => `${prefixCls.value}-item-group`);\n    const isMeasure = useMeasure();\n    return () => {\n      var _a, _b;\n      if (isMeasure) return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n      return _createVNode(\"li\", _objectSpread(_objectSpread({}, attrs), {}, {\n        \"onClick\": e => e.stopPropagation(),\n        \"class\": groupPrefixCls.value\n      }), [_createVNode(\"div\", {\n        \"title\": typeof props.title === 'string' ? props.title : undefined,\n        \"class\": `${groupPrefixCls.value}-title`\n      }, [getPropsSlot(slots, props, 'title')]), _createVNode(\"ul\", {\n        \"class\": `${groupPrefixCls.value}-list`\n      }, [(_b = slots.default) === null || _b === void 0 ? void 0 : _b.call(slots)])]);\n    };\n  }\n});", "import { createVNode as _createVNode } from \"vue\";\nimport { computed, defineComponent } from 'vue';\nimport { useInjectMenu } from './hooks/useMenuContext';\nexport const menuDividerProps = () => ({\n  prefixCls: String,\n  dashed: Boolean\n});\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'AMenuDivider',\n  props: menuDividerProps(),\n  setup(props) {\n    const {\n      prefixCls\n    } = useInjectMenu();\n    const cls = computed(() => {\n      return {\n        [`${prefixCls.value}-item-divider`]: true,\n        [`${prefixCls.value}-item-divider-dashed`]: !!props.dashed\n      };\n    });\n    return () => {\n      return _createVNode(\"li\", {\n        \"class\": cls.value\n      }, null);\n    };\n  }\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { createVNode as _createVNode } from \"vue\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport SubMenu from '../SubMenu';\nimport ItemGroup from '../ItemGroup';\nimport MenuDivider from '../Divider';\nimport MenuItem from '../MenuItem';\nimport { ref, shallowRef, watch } from 'vue';\nfunction convertItemsToNodes(list, store, parentMenuInfo) {\n  return (list || []).map((opt, index) => {\n    if (opt && typeof opt === 'object') {\n      const _a = opt,\n        {\n          label,\n          children,\n          key,\n          type\n        } = _a,\n        restProps = __rest(_a, [\"label\", \"children\", \"key\", \"type\"]);\n      const mergedKey = key !== null && key !== void 0 ? key : `tmp-${index}`;\n      // 此处 eventKey === key, 移除 children 后可以移除 eventKey\n      const parentKeys = parentMenuInfo ? parentMenuInfo.parentKeys.slice() : [];\n      const childrenEventKeys = [];\n      // if\n      const menuInfo = {\n        eventKey: mergedKey,\n        key: mergedKey,\n        parentEventKeys: ref(parentKeys),\n        parentKeys: ref(parentKeys),\n        childrenEventKeys: ref(childrenEventKeys),\n        isLeaf: false\n      };\n      // MenuItemGroup & SubMenuItem\n      if (children || type === 'group') {\n        if (type === 'group') {\n          const childrenNodes = convertItemsToNodes(children, store, parentMenuInfo);\n          // Group\n          return _createVNode(ItemGroup, _objectSpread(_objectSpread({\n            \"key\": mergedKey\n          }, restProps), {}, {\n            \"title\": label,\n            \"originItemValue\": opt\n          }), {\n            default: () => [childrenNodes]\n          });\n        }\n        store.set(mergedKey, menuInfo);\n        if (parentMenuInfo) {\n          parentMenuInfo.childrenEventKeys.push(mergedKey);\n        }\n        // Sub Menu\n        const childrenNodes = convertItemsToNodes(children, store, {\n          childrenEventKeys,\n          parentKeys: [].concat(parentKeys, mergedKey)\n        });\n        return _createVNode(SubMenu, _objectSpread(_objectSpread({\n          \"key\": mergedKey\n        }, restProps), {}, {\n          \"title\": label,\n          \"originItemValue\": opt\n        }), {\n          default: () => [childrenNodes]\n        });\n      }\n      // MenuItem & Divider\n      if (type === 'divider') {\n        return _createVNode(MenuDivider, _objectSpread({\n          \"key\": mergedKey\n        }, restProps), null);\n      }\n      menuInfo.isLeaf = true;\n      store.set(mergedKey, menuInfo);\n      return _createVNode(MenuItem, _objectSpread(_objectSpread({\n        \"key\": mergedKey\n      }, restProps), {}, {\n        \"originItemValue\": opt\n      }), {\n        default: () => [label]\n      });\n    }\n    return null;\n  }).filter(opt => opt);\n}\n// FIXME: Move logic here in v4\n/**\n * We simply convert `items` to VueNode for reuse origin component logic. But we need move all the\n * logic from component into this hooks when in v4\n */\nexport default function useItems(props) {\n  const itemsNodes = shallowRef([]);\n  const hasItmes = shallowRef(false);\n  const store = shallowRef(new Map());\n  watch(() => props.items, () => {\n    const newStore = new Map();\n    hasItmes.value = false;\n    if (props.items) {\n      hasItmes.value = true;\n      itemsNodes.value = convertItemsToNodes(props.items, newStore);\n    } else {\n      itemsNodes.value = undefined;\n    }\n    store.value = newStore;\n  }, {\n    immediate: true,\n    deep: true\n  });\n  return {\n    itemsNodes,\n    store,\n    hasItmes\n  };\n}", "const getHorizontalStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    menuHorizontalHeight,\n    colorSplit,\n    lineWidth,\n    lineType,\n    menuItemPaddingInline\n  } = token;\n  return {\n    [`${componentCls}-horizontal`]: {\n      lineHeight: `${menuHorizontalHeight}px`,\n      border: 0,\n      borderBottom: `${lineWidth}px ${lineType} ${colorSplit}`,\n      boxShadow: 'none',\n      '&::after': {\n        display: 'block',\n        clear: 'both',\n        height: 0,\n        content: '\"\\\\20\"'\n      },\n      // ======================= Item =======================\n      [`${componentCls}-item, ${componentCls}-submenu`]: {\n        position: 'relative',\n        display: 'inline-block',\n        verticalAlign: 'bottom',\n        paddingInline: menuItemPaddingInline\n      },\n      [`> ${componentCls}-item:hover,\n        > ${componentCls}-item-active,\n        > ${componentCls}-submenu ${componentCls}-submenu-title:hover`]: {\n        backgroundColor: 'transparent'\n      },\n      [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n        transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`].join(',')\n      },\n      // ===================== Sub Menu =====================\n      [`${componentCls}-submenu-arrow`]: {\n        display: 'none'\n      }\n    }\n  };\n};\nexport default getHorizontalStyle;", "const getRTLStyle = _ref => {\n  let {\n    componentCls,\n    menuArrowOffset\n  } = _ref;\n  return {\n    [`${componentCls}-rtl`]: {\n      direction: 'rtl'\n    },\n    [`${componentCls}-submenu-rtl`]: {\n      transformOrigin: '100% 0'\n    },\n    // Vertical Arrow\n    [`${componentCls}-rtl${componentCls}-vertical,\n    ${componentCls}-submenu-rtl ${componentCls}-vertical`]: {\n      [`${componentCls}-submenu-arrow`]: {\n        '&::before': {\n          transform: `rotate(-45deg) translateY(-${menuArrowOffset})`\n        },\n        '&::after': {\n          transform: `rotate(45deg) translateY(${menuArrowOffset})`\n        }\n      }\n    }\n  };\n};\nexport default getRTLStyle;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { genFocusOutline } from '../../style';\nconst accessibilityFocus = token => _extends({}, genFocusOutline(token));\nconst getThemeStyle = (token, themeSuffix) => {\n  const {\n    componentCls,\n    colorItemText,\n    colorItemTextSelected,\n    colorGroupTitle,\n    colorItemBg,\n    colorSubItemBg,\n    colorItemBgSelected,\n    colorActiveBarHeight,\n    colorActiveBarWidth,\n    colorActiveBarBorderSize,\n    motionDurationSlow,\n    motionEaseInOut,\n    motionEaseOut,\n    menuItemPaddingInline,\n    motionDurationMid,\n    colorItemTextHover,\n    lineType,\n    colorSplit,\n    // Disabled\n    colorItemTextDisabled,\n    // Danger\n    colorDangerItemText,\n    colorDangerItemTextHover,\n    colorDangerItemTextSelected,\n    colorDangerItemBgActive,\n    colorDangerItemBgSelected,\n    colorItemBgHover,\n    menuSubMenuBg,\n    // Horizontal\n    colorItemTextSelectedHorizontal,\n    colorItemBgSelectedHorizontal\n  } = token;\n  return {\n    [`${componentCls}-${themeSuffix}`]: {\n      color: colorItemText,\n      background: colorItemBg,\n      [`&${componentCls}-root:focus-visible`]: _extends({}, accessibilityFocus(token)),\n      // ======================== Item ========================\n      [`${componentCls}-item-group-title`]: {\n        color: colorGroupTitle\n      },\n      [`${componentCls}-submenu-selected`]: {\n        [`> ${componentCls}-submenu-title`]: {\n          color: colorItemTextSelected\n        }\n      },\n      // Disabled\n      [`${componentCls}-item-disabled, ${componentCls}-submenu-disabled`]: {\n        color: `${colorItemTextDisabled} !important`\n      },\n      // Hover\n      [`${componentCls}-item:hover, ${componentCls}-submenu-title:hover`]: {\n        [`&:not(${componentCls}-item-selected):not(${componentCls}-submenu-selected)`]: {\n          color: colorItemTextHover\n        }\n      },\n      [`&:not(${componentCls}-horizontal)`]: {\n        [`${componentCls}-item:not(${componentCls}-item-selected)`]: {\n          '&:hover': {\n            backgroundColor: colorItemBgHover\n          },\n          '&:active': {\n            backgroundColor: colorItemBgSelected\n          }\n        },\n        [`${componentCls}-submenu-title`]: {\n          '&:hover': {\n            backgroundColor: colorItemBgHover\n          },\n          '&:active': {\n            backgroundColor: colorItemBgSelected\n          }\n        }\n      },\n      // Danger - only Item has\n      [`${componentCls}-item-danger`]: {\n        color: colorDangerItemText,\n        [`&${componentCls}-item:hover`]: {\n          [`&:not(${componentCls}-item-selected):not(${componentCls}-submenu-selected)`]: {\n            color: colorDangerItemTextHover\n          }\n        },\n        [`&${componentCls}-item:active`]: {\n          background: colorDangerItemBgActive\n        }\n      },\n      [`${componentCls}-item a`]: {\n        '&, &:hover': {\n          color: 'inherit'\n        }\n      },\n      [`${componentCls}-item-selected`]: {\n        color: colorItemTextSelected,\n        // Danger\n        [`&${componentCls}-item-danger`]: {\n          color: colorDangerItemTextSelected\n        },\n        [`a, a:hover`]: {\n          color: 'inherit'\n        }\n      },\n      [`& ${componentCls}-item-selected`]: {\n        backgroundColor: colorItemBgSelected,\n        // Danger\n        [`&${componentCls}-item-danger`]: {\n          backgroundColor: colorDangerItemBgSelected\n        }\n      },\n      [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n        [`&:not(${componentCls}-item-disabled):focus-visible`]: _extends({}, accessibilityFocus(token))\n      },\n      [`&${componentCls}-submenu > ${componentCls}`]: {\n        backgroundColor: menuSubMenuBg\n      },\n      [`&${componentCls}-popup > ${componentCls}`]: {\n        backgroundColor: colorItemBg\n      },\n      // ====================== Horizontal ======================\n      [`&${componentCls}-horizontal`]: _extends(_extends({}, themeSuffix === 'dark' ? {\n        borderBottom: 0\n      } : {}), {\n        [`> ${componentCls}-item, > ${componentCls}-submenu`]: {\n          top: colorActiveBarBorderSize,\n          marginTop: -colorActiveBarBorderSize,\n          marginBottom: 0,\n          borderRadius: 0,\n          '&::after': {\n            position: 'absolute',\n            insetInline: menuItemPaddingInline,\n            bottom: 0,\n            borderBottom: `${colorActiveBarHeight}px solid transparent`,\n            transition: `border-color ${motionDurationSlow} ${motionEaseInOut}`,\n            content: '\"\"'\n          },\n          [`&:hover, &-active, &-open`]: {\n            '&::after': {\n              borderBottomWidth: colorActiveBarHeight,\n              borderBottomColor: colorItemTextSelectedHorizontal\n            }\n          },\n          [`&-selected`]: {\n            color: colorItemTextSelectedHorizontal,\n            backgroundColor: colorItemBgSelectedHorizontal,\n            '&::after': {\n              borderBottomWidth: colorActiveBarHeight,\n              borderBottomColor: colorItemTextSelectedHorizontal\n            }\n          }\n        }\n      }),\n      // ================== Inline & Vertical ===================\n      //\n      [`&${componentCls}-root`]: {\n        [`&${componentCls}-inline, &${componentCls}-vertical`]: {\n          borderInlineEnd: `${colorActiveBarBorderSize}px ${lineType} ${colorSplit}`\n        }\n      },\n      // ======================== Inline ========================\n      [`&${componentCls}-inline`]: {\n        // Sub\n        [`${componentCls}-sub${componentCls}-inline`]: {\n          background: colorSubItemBg\n        },\n        // Item\n        [`${componentCls}-item, ${componentCls}-submenu-title`]: colorActiveBarBorderSize && colorActiveBarWidth ? {\n          width: `calc(100% + ${colorActiveBarBorderSize}px)`\n        } : {},\n        [`${componentCls}-item`]: {\n          position: 'relative',\n          '&::after': {\n            position: 'absolute',\n            insetBlock: 0,\n            insetInlineEnd: 0,\n            borderInlineEnd: `${colorActiveBarWidth}px solid ${colorItemTextSelected}`,\n            transform: 'scaleY(0.0001)',\n            opacity: 0,\n            transition: [`transform ${motionDurationMid} ${motionEaseOut}`, `opacity ${motionDurationMid} ${motionEaseOut}`].join(','),\n            content: '\"\"'\n          },\n          // Danger\n          [`&${componentCls}-item-danger`]: {\n            '&::after': {\n              borderInlineEndColor: colorDangerItemTextSelected\n            }\n          }\n        },\n        [`${componentCls}-selected, ${componentCls}-item-selected`]: {\n          '&::after': {\n            transform: 'scaleY(1)',\n            opacity: 1,\n            transition: [`transform ${motionDurationMid} ${motionEaseInOut}`, `opacity ${motionDurationMid} ${motionEaseInOut}`].join(',')\n          }\n        }\n      }\n    }\n  };\n};\nexport default getThemeStyle;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { textEllipsis } from '../../style';\nconst getVerticalInlineStyle = token => {\n  const {\n    componentCls,\n    menuItemHeight,\n    itemMarginInline,\n    padding,\n    menuArrowSize,\n    marginXS,\n    marginXXS\n  } = token;\n  const paddingWithArrow = padding + menuArrowSize + marginXS;\n  return {\n    [`${componentCls}-item`]: {\n      position: 'relative'\n    },\n    [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n      height: menuItemHeight,\n      lineHeight: `${menuItemHeight}px`,\n      paddingInline: padding,\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      marginInline: itemMarginInline,\n      marginBlock: marginXXS,\n      width: `calc(100% - ${itemMarginInline * 2}px)`\n    },\n    // disable margin collapsed\n    [`${componentCls}-submenu`]: {\n      paddingBottom: 0.02\n    },\n    [`> ${componentCls}-item,\n            > ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n      height: menuItemHeight,\n      lineHeight: `${menuItemHeight}px`\n    },\n    [`${componentCls}-item-group-list ${componentCls}-submenu-title,\n            ${componentCls}-submenu-title`]: {\n      paddingInlineEnd: paddingWithArrow\n    }\n  };\n};\nconst getVerticalStyle = token => {\n  const {\n    componentCls,\n    iconCls,\n    menuItemHeight,\n    colorTextLightSolid,\n    dropdownWidth,\n    controlHeightLG,\n    motionDurationMid,\n    motionEaseOut,\n    paddingXL,\n    fontSizeSM,\n    fontSizeLG,\n    motionDurationSlow,\n    paddingXS,\n    boxShadowSecondary\n  } = token;\n  const inlineItemStyle = {\n    height: menuItemHeight,\n    lineHeight: `${menuItemHeight}px`,\n    listStylePosition: 'inside',\n    listStyleType: 'disc'\n  };\n  return [{\n    [componentCls]: {\n      [`&-inline, &-vertical`]: _extends({\n        [`&${componentCls}-root`]: {\n          boxShadow: 'none'\n        }\n      }, getVerticalInlineStyle(token))\n    },\n    [`${componentCls}-submenu-popup`]: {\n      [`${componentCls}-vertical`]: _extends(_extends({}, getVerticalInlineStyle(token)), {\n        boxShadow: boxShadowSecondary\n      })\n    }\n  },\n  // Vertical only\n  {\n    [`${componentCls}-submenu-popup ${componentCls}-vertical${componentCls}-sub`]: {\n      minWidth: dropdownWidth,\n      maxHeight: `calc(100vh - ${controlHeightLG * 2.5}px)`,\n      padding: '0',\n      overflow: 'hidden',\n      borderInlineEnd: 0,\n      // https://github.com/ant-design/ant-design/issues/22244\n      // https://github.com/ant-design/ant-design/issues/26812\n      \"&:not([class*='-active'])\": {\n        overflowX: 'hidden',\n        overflowY: 'auto'\n      }\n    }\n  },\n  // Inline Only\n  {\n    [`${componentCls}-inline`]: {\n      width: '100%',\n      // Motion enhance for first level\n      [`&${componentCls}-root`]: {\n        [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n          display: 'flex',\n          alignItems: 'center',\n          transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`, `padding ${motionDurationMid} ${motionEaseOut}`].join(','),\n          [`> ${componentCls}-title-content`]: {\n            flex: 'auto',\n            minWidth: 0,\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          },\n          '> *': {\n            flex: 'none'\n          }\n        }\n      },\n      // >>>>> Sub\n      [`${componentCls}-sub${componentCls}-inline`]: {\n        padding: 0,\n        border: 0,\n        borderRadius: 0,\n        boxShadow: 'none',\n        [`& > ${componentCls}-submenu > ${componentCls}-submenu-title`]: inlineItemStyle,\n        [`& ${componentCls}-item-group-title`]: {\n          paddingInlineStart: paddingXL\n        }\n      },\n      // >>>>> Item\n      [`${componentCls}-item`]: inlineItemStyle\n    }\n  },\n  // Inline Collapse Only\n  {\n    [`${componentCls}-inline-collapsed`]: {\n      width: menuItemHeight * 2,\n      [`&${componentCls}-root`]: {\n        [`${componentCls}-item, ${componentCls}-submenu ${componentCls}-submenu-title`]: {\n          [`> ${componentCls}-inline-collapsed-noicon`]: {\n            fontSize: fontSizeLG,\n            textAlign: 'center'\n          }\n        }\n      },\n      [`> ${componentCls}-item,\n          > ${componentCls}-item-group > ${componentCls}-item-group-list > ${componentCls}-item,\n          > ${componentCls}-item-group > ${componentCls}-item-group-list > ${componentCls}-submenu > ${componentCls}-submenu-title,\n          > ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n        insetInlineStart: 0,\n        paddingInline: `calc(50% - ${fontSizeSM}px)`,\n        textOverflow: 'clip',\n        [`\n            ${componentCls}-submenu-arrow,\n            ${componentCls}-submenu-expand-icon\n          `]: {\n          opacity: 0\n        },\n        [`${componentCls}-item-icon, ${iconCls}`]: {\n          margin: 0,\n          fontSize: fontSizeLG,\n          lineHeight: `${menuItemHeight}px`,\n          '+ span': {\n            display: 'inline-block',\n            opacity: 0\n          }\n        }\n      },\n      [`${componentCls}-item-icon, ${iconCls}`]: {\n        display: 'inline-block'\n      },\n      '&-tooltip': {\n        pointerEvents: 'none',\n        [`${componentCls}-item-icon, ${iconCls}`]: {\n          display: 'none'\n        },\n        'a, a:hover': {\n          color: colorTextLightSolid\n        }\n      },\n      [`${componentCls}-item-group-title`]: _extends(_extends({}, textEllipsis), {\n        paddingInline: paddingXS\n      })\n    }\n  }];\n};\nexport default getVerticalStyle;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { TinyColor } from '@ctrl/tinycolor';\nimport { genCollapseMotion, initSlideMotion, initZoomMotion } from '../../style/motion';\nimport { genComponentStyleHook, mergeToken } from '../../theme/internal';\nimport getHorizontalStyle from './horizontal';\nimport getRTLStyle from './rtl';\nimport getThemeStyle from './theme';\nimport getVerticalStyle from './vertical';\nimport { clearFix, resetComponent, resetIcon } from '../../style';\nimport 'vue';\nconst genMenuItemStyle = token => {\n  const {\n    componentCls,\n    fontSize,\n    motionDurationSlow,\n    motionDurationMid,\n    motionEaseInOut,\n    motionEaseOut,\n    iconCls,\n    controlHeightSM\n  } = token;\n  return {\n    // >>>>> Item\n    [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n      position: 'relative',\n      display: 'block',\n      margin: 0,\n      whiteSpace: 'nowrap',\n      cursor: 'pointer',\n      transition: [`border-color ${motionDurationSlow}`, `background ${motionDurationSlow}`, `padding ${motionDurationSlow} ${motionEaseInOut}`].join(','),\n      [`${componentCls}-item-icon, ${iconCls}`]: {\n        minWidth: fontSize,\n        fontSize,\n        transition: [`font-size ${motionDurationMid} ${motionEaseOut}`, `margin ${motionDurationSlow} ${motionEaseInOut}`, `color ${motionDurationSlow}`].join(','),\n        '+ span': {\n          marginInlineStart: controlHeightSM - fontSize,\n          opacity: 1,\n          transition: [`opacity ${motionDurationSlow} ${motionEaseInOut}`, `margin ${motionDurationSlow}`, `color ${motionDurationSlow}`].join(',')\n        }\n      },\n      [`${componentCls}-item-icon`]: _extends({}, resetIcon()),\n      [`&${componentCls}-item-only-child`]: {\n        [`> ${iconCls}, > ${componentCls}-item-icon`]: {\n          marginInlineEnd: 0\n        }\n      }\n    },\n    // Disabled state sets text to gray and nukes hover/tab effects\n    [`${componentCls}-item-disabled, ${componentCls}-submenu-disabled`]: {\n      background: 'none !important',\n      cursor: 'not-allowed',\n      '&::after': {\n        borderColor: 'transparent !important'\n      },\n      a: {\n        color: 'inherit !important'\n      },\n      [`> ${componentCls}-submenu-title`]: {\n        color: 'inherit !important',\n        cursor: 'not-allowed'\n      }\n    }\n  };\n};\nconst genSubMenuArrowStyle = token => {\n  const {\n    componentCls,\n    motionDurationSlow,\n    motionEaseInOut,\n    borderRadius,\n    menuArrowSize,\n    menuArrowOffset\n  } = token;\n  return {\n    [`${componentCls}-submenu`]: {\n      [`&-expand-icon, &-arrow`]: {\n        position: 'absolute',\n        top: '50%',\n        insetInlineEnd: token.margin,\n        width: menuArrowSize,\n        color: 'currentcolor',\n        transform: 'translateY(-50%)',\n        transition: `transform ${motionDurationSlow} ${motionEaseInOut}, opacity ${motionDurationSlow}`\n      },\n      '&-arrow': {\n        // →\n        '&::before, &::after': {\n          position: 'absolute',\n          width: menuArrowSize * 0.6,\n          height: menuArrowSize * 0.15,\n          backgroundColor: 'currentcolor',\n          borderRadius,\n          transition: [`background ${motionDurationSlow} ${motionEaseInOut}`, `transform ${motionDurationSlow} ${motionEaseInOut}`, `top ${motionDurationSlow} ${motionEaseInOut}`, `color ${motionDurationSlow} ${motionEaseInOut}`].join(','),\n          content: '\"\"'\n        },\n        '&::before': {\n          transform: `rotate(45deg) translateY(-${menuArrowOffset})`\n        },\n        '&::after': {\n          transform: `rotate(-45deg) translateY(${menuArrowOffset})`\n        }\n      }\n    }\n  };\n};\n// =============================== Base ===============================\nconst getBaseStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    fontSize,\n    motionDurationSlow,\n    motionDurationMid,\n    motionEaseInOut,\n    lineHeight,\n    paddingXS,\n    padding,\n    colorSplit,\n    lineWidth,\n    zIndexPopup,\n    borderRadiusLG,\n    radiusSubMenuItem,\n    menuArrowSize,\n    menuArrowOffset,\n    lineType,\n    menuPanelMaskInset\n  } = token;\n  return [\n  // Misc\n  {\n    '': {\n      [`${componentCls}`]: _extends(_extends({}, clearFix()), {\n        // Hidden\n        [`&-hidden`]: {\n          display: 'none'\n        }\n      })\n    },\n    [`${componentCls}-submenu-hidden`]: {\n      display: 'none'\n    }\n  }, {\n    [componentCls]: _extends(_extends(_extends(_extends(_extends(_extends(_extends({}, resetComponent(token)), clearFix()), {\n      marginBottom: 0,\n      paddingInlineStart: 0,\n      // Override default ul/ol\n      fontSize,\n      lineHeight: 0,\n      listStyle: 'none',\n      outline: 'none',\n      transition: `width ${motionDurationSlow} cubic-bezier(0.2, 0, 0, 1) 0s`,\n      [`ul, ol`]: {\n        margin: 0,\n        padding: 0,\n        listStyle: 'none'\n      },\n      // Overflow ellipsis\n      [`&-overflow`]: {\n        display: 'flex',\n        [`${componentCls}-item`]: {\n          flex: 'none'\n        }\n      },\n      [`${componentCls}-item, ${componentCls}-submenu, ${componentCls}-submenu-title`]: {\n        borderRadius: token.radiusItem\n      },\n      [`${componentCls}-item-group-title`]: {\n        padding: `${paddingXS}px ${padding}px`,\n        fontSize,\n        lineHeight,\n        transition: `all ${motionDurationSlow}`\n      },\n      [`&-horizontal ${componentCls}-submenu`]: {\n        transition: [`border-color ${motionDurationSlow} ${motionEaseInOut}`, `background ${motionDurationSlow} ${motionEaseInOut}`].join(',')\n      },\n      [`${componentCls}-submenu, ${componentCls}-submenu-inline`]: {\n        transition: [`border-color ${motionDurationSlow} ${motionEaseInOut}`, `background ${motionDurationSlow} ${motionEaseInOut}`, `padding ${motionDurationMid} ${motionEaseInOut}`].join(',')\n      },\n      [`${componentCls}-submenu ${componentCls}-sub`]: {\n        cursor: 'initial',\n        transition: [`background ${motionDurationSlow} ${motionEaseInOut}`, `padding ${motionDurationSlow} ${motionEaseInOut}`].join(',')\n      },\n      [`${componentCls}-title-content`]: {\n        transition: `color ${motionDurationSlow}`\n      },\n      [`${componentCls}-item a`]: {\n        '&::before': {\n          position: 'absolute',\n          inset: 0,\n          backgroundColor: 'transparent',\n          content: '\"\"'\n        }\n      },\n      // Removed a Badge related style seems it's safe\n      // https://github.com/ant-design/ant-design/issues/19809\n      // >>>>> Divider\n      [`${componentCls}-item-divider`]: {\n        overflow: 'hidden',\n        lineHeight: 0,\n        borderColor: colorSplit,\n        borderStyle: lineType,\n        borderWidth: 0,\n        borderTopWidth: lineWidth,\n        marginBlock: lineWidth,\n        padding: 0,\n        '&-dashed': {\n          borderStyle: 'dashed'\n        }\n      }\n    }), genMenuItemStyle(token)), {\n      [`${componentCls}-item-group`]: {\n        [`${componentCls}-item-group-list`]: {\n          margin: 0,\n          padding: 0,\n          [`${componentCls}-item, ${componentCls}-submenu-title`]: {\n            paddingInline: `${fontSize * 2}px ${padding}px`\n          }\n        }\n      },\n      // ======================= Sub Menu =======================\n      '&-submenu': {\n        '&-popup': {\n          position: 'absolute',\n          zIndex: zIndexPopup,\n          background: 'transparent',\n          borderRadius: borderRadiusLG,\n          boxShadow: 'none',\n          transformOrigin: '0 0',\n          // https://github.com/ant-design/ant-design/issues/13955\n          '&::before': {\n            position: 'absolute',\n            inset: `${menuPanelMaskInset}px 0 0`,\n            zIndex: -1,\n            width: '100%',\n            height: '100%',\n            opacity: 0,\n            content: '\"\"'\n          }\n        },\n        // https://github.com/ant-design/ant-design/issues/13955\n        '&-placement-rightTop::before': {\n          top: 0,\n          insetInlineStart: menuPanelMaskInset\n        },\n        [`> ${componentCls}`]: _extends(_extends(_extends({\n          borderRadius: borderRadiusLG\n        }, genMenuItemStyle(token)), genSubMenuArrowStyle(token)), {\n          [`${componentCls}-item, ${componentCls}-submenu > ${componentCls}-submenu-title`]: {\n            borderRadius: radiusSubMenuItem\n          },\n          [`${componentCls}-submenu-title::after`]: {\n            transition: `transform ${motionDurationSlow} ${motionEaseInOut}`\n          }\n        })\n      }\n    }), genSubMenuArrowStyle(token)), {\n      [`&-inline-collapsed ${componentCls}-submenu-arrow,\n        &-inline ${componentCls}-submenu-arrow`]: {\n        // ↓\n        '&::before': {\n          transform: `rotate(-45deg) translateX(${menuArrowOffset})`\n        },\n        '&::after': {\n          transform: `rotate(45deg) translateX(-${menuArrowOffset})`\n        }\n      },\n      [`${componentCls}-submenu-open${componentCls}-submenu-inline > ${componentCls}-submenu-title > ${componentCls}-submenu-arrow`]: {\n        // ↑\n        transform: `translateY(-${menuArrowSize * 0.2}px)`,\n        '&::after': {\n          transform: `rotate(-45deg) translateX(-${menuArrowOffset})`\n        },\n        '&::before': {\n          transform: `rotate(45deg) translateX(${menuArrowOffset})`\n        }\n      }\n    })\n  },\n  // Integration with header element so menu items have the same height\n  {\n    [`${antCls}-layout-header`]: {\n      [componentCls]: {\n        lineHeight: 'inherit'\n      }\n    }\n  }];\n};\n// ============================== Export ==============================\nexport default ((prefixCls, injectStyle) => {\n  const useOriginHook = genComponentStyleHook('Menu', (token, _ref) => {\n    let {\n      overrideComponentToken\n    } = _ref;\n    // Dropdown will handle menu style self. We do not need to handle this.\n    if ((injectStyle === null || injectStyle === void 0 ? void 0 : injectStyle.value) === false) {\n      return [];\n    }\n    const {\n      colorBgElevated,\n      colorPrimary,\n      colorError,\n      colorErrorHover,\n      colorTextLightSolid\n    } = token;\n    const {\n      controlHeightLG,\n      fontSize\n    } = token;\n    const menuArrowSize = fontSize / 7 * 5;\n    // Menu Token\n    const menuToken = mergeToken(token, {\n      menuItemHeight: controlHeightLG,\n      menuItemPaddingInline: token.margin,\n      menuArrowSize,\n      menuHorizontalHeight: controlHeightLG * 1.15,\n      menuArrowOffset: `${menuArrowSize * 0.25}px`,\n      menuPanelMaskInset: -7,\n      menuSubMenuBg: colorBgElevated\n    });\n    const colorTextDark = new TinyColor(colorTextLightSolid).setAlpha(0.65).toRgbString();\n    const menuDarkToken = mergeToken(menuToken, {\n      colorItemText: colorTextDark,\n      colorItemTextHover: colorTextLightSolid,\n      colorGroupTitle: colorTextDark,\n      colorItemTextSelected: colorTextLightSolid,\n      colorItemBg: '#001529',\n      colorSubItemBg: '#000c17',\n      colorItemBgActive: 'transparent',\n      colorItemBgSelected: colorPrimary,\n      colorActiveBarWidth: 0,\n      colorActiveBarHeight: 0,\n      colorActiveBarBorderSize: 0,\n      // Disabled\n      colorItemTextDisabled: new TinyColor(colorTextLightSolid).setAlpha(0.25).toRgbString(),\n      // Danger\n      colorDangerItemText: colorError,\n      colorDangerItemTextHover: colorErrorHover,\n      colorDangerItemTextSelected: colorTextLightSolid,\n      colorDangerItemBgActive: colorError,\n      colorDangerItemBgSelected: colorError,\n      menuSubMenuBg: '#001529',\n      // Horizontal\n      colorItemTextSelectedHorizontal: colorTextLightSolid,\n      colorItemBgSelectedHorizontal: colorPrimary\n    }, _extends({}, overrideComponentToken));\n    return [\n    // Basic\n    getBaseStyle(menuToken),\n    // Horizontal\n    getHorizontalStyle(menuToken),\n    // Vertical\n    getVerticalStyle(menuToken),\n    // Theme\n    getThemeStyle(menuToken, 'light'), getThemeStyle(menuDarkToken, 'dark'),\n    // RTL\n    getRTLStyle(menuToken),\n    // Motion\n    genCollapseMotion(menuToken), initSlideMotion(menuToken, 'slide-up'), initSlideMotion(menuToken, 'slide-down'), initZoomMotion(menuToken, 'zoom-big')];\n  }, token => {\n    const {\n      colorPrimary,\n      colorError,\n      colorTextDisabled,\n      colorErrorBg,\n      colorText,\n      colorTextDescription,\n      colorBgContainer,\n      colorFillAlter,\n      colorFillContent,\n      lineWidth,\n      lineWidthBold,\n      controlItemBgActive,\n      colorBgTextHover\n    } = token;\n    return {\n      dropdownWidth: 160,\n      zIndexPopup: token.zIndexPopupBase + 50,\n      radiusItem: token.borderRadiusLG,\n      radiusSubMenuItem: token.borderRadiusSM,\n      colorItemText: colorText,\n      colorItemTextHover: colorText,\n      colorItemTextHoverHorizontal: colorPrimary,\n      colorGroupTitle: colorTextDescription,\n      colorItemTextSelected: colorPrimary,\n      colorItemTextSelectedHorizontal: colorPrimary,\n      colorItemBg: colorBgContainer,\n      colorItemBgHover: colorBgTextHover,\n      colorItemBgActive: colorFillContent,\n      colorSubItemBg: colorFillAlter,\n      colorItemBgSelected: controlItemBgActive,\n      colorItemBgSelectedHorizontal: 'transparent',\n      colorActiveBarWidth: 0,\n      colorActiveBarHeight: lineWidthBold,\n      colorActiveBarBorderSize: lineWidth,\n      // Disabled\n      colorItemTextDisabled: colorTextDisabled,\n      // Danger\n      colorDangerItemText: colorError,\n      colorDangerItemTextHover: colorError,\n      colorDangerItemTextSelected: colorError,\n      colorDangerItemBgActive: colorErrorBg,\n      colorDangerItemBgSelected: colorErrorBg,\n      itemMarginInline: token.marginXXS\n    };\n  });\n  return useOriginHook(prefixCls);\n});", "import { provide, computed, inject } from 'vue';\nexport const OverrideContextKey = Symbol('OverrideContextKey');\nexport const useInjectOverride = () => {\n  return inject(OverrideContextKey, undefined);\n};\nexport const useProvideOverride = props => {\n  var _a, _b, _c;\n  const {\n    prefixCls,\n    mode,\n    selectable,\n    validator,\n    onClick,\n    expandIcon\n  } = useInjectOverride() || {};\n  provide(OverrideContextKey, {\n    prefixCls: computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.prefixCls) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : prefixCls === null || prefixCls === void 0 ? void 0 : prefixCls.value;\n    }),\n    mode: computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.mode) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : mode === null || mode === void 0 ? void 0 : mode.value;\n    }),\n    selectable: computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.selectable) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : selectable === null || selectable === void 0 ? void 0 : selectable.value;\n    }),\n    validator: (_a = props.validator) !== null && _a !== void 0 ? _a : validator,\n    onClick: (_b = props.onClick) !== null && _b !== void 0 ? _b : onClick,\n    expandIcon: (_c = props.expandIcon) !== null && _c !== void 0 ? _c : expandIcon === null || expandIcon === void 0 ? void 0 : expandIcon.value\n  });\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { Fragment as _Fragment, createVNode as _createVNode, resolveDirective as _resolveDirective } from \"vue\";\nimport { shallowRef, Teleport, computed, defineComponent, ref, inject, watchEffect, watch, onMounted, unref } from 'vue';\nimport shallowEqual from '../../_util/shallowequal';\nimport useProvideMenu, { MenuContextProvider, useProvideFirstLevel } from './hooks/useMenuContext';\nimport useConfigInject from '../../config-provider/hooks/useConfigInject';\nimport devWarning from '../../vc-util/devWarning';\nimport uniq from 'lodash-es/uniq';\nimport { SiderCollapsedKey } from '../../layout/injectionKey';\nimport { flattenChildren } from '../../_util/props-util';\nimport Overflow from '../../vc-overflow';\nimport MenuItem from './MenuItem';\nimport SubMenu from './SubMenu';\nimport EllipsisOutlined from \"@ant-design/icons-vue/es/icons/EllipsisOutlined\";\nimport { cloneElement } from '../../_util/vnode';\nimport { OVERFLOW_KEY, PathContext } from './hooks/useKeyPath';\nimport collapseMotion from '../../_util/collapseMotion';\nimport useItems from './hooks/useItems';\nimport useStyle from '../style';\nimport { useInjectOverride } from './OverrideContext';\nexport const menuProps = () => ({\n  id: String,\n  prefixCls: String,\n  // donot use items, now only support inner use\n  items: Array,\n  disabled: Boolean,\n  inlineCollapsed: Boolean,\n  disabledOverflow: Boolean,\n  forceSubMenuRender: Boolean,\n  openKeys: Array,\n  selectedKeys: Array,\n  activeKey: String,\n  selectable: {\n    type: Boolean,\n    default: true\n  },\n  multiple: {\n    type: Boolean,\n    default: false\n  },\n  tabindex: {\n    type: [Number, String]\n  },\n  motion: Object,\n  role: String,\n  theme: {\n    type: String,\n    default: 'light'\n  },\n  mode: {\n    type: String,\n    default: 'vertical'\n  },\n  inlineIndent: {\n    type: Number,\n    default: 24\n  },\n  subMenuOpenDelay: {\n    type: Number,\n    default: 0\n  },\n  subMenuCloseDelay: {\n    type: Number,\n    default: 0.1\n  },\n  builtinPlacements: {\n    type: Object\n  },\n  triggerSubMenuAction: {\n    type: String,\n    default: 'hover'\n  },\n  getPopupContainer: Function,\n  expandIcon: Function,\n  onOpenChange: Function,\n  onSelect: Function,\n  onDeselect: Function,\n  onClick: [Function, Array],\n  onFocus: Function,\n  onBlur: Function,\n  onMousedown: Function,\n  'onUpdate:openKeys': Function,\n  'onUpdate:selectedKeys': Function,\n  'onUpdate:activeKey': Function\n});\nconst EMPTY_LIST = [];\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'AMenu',\n  inheritAttrs: false,\n  props: menuProps(),\n  slots: Object,\n  setup(props, _ref) {\n    let {\n      slots,\n      emit,\n      attrs\n    } = _ref;\n    const {\n      direction,\n      getPrefixCls\n    } = useConfigInject('menu', props);\n    const override = useInjectOverride();\n    const prefixCls = computed(() => {\n      var _a;\n      return getPrefixCls('menu', props.prefixCls || ((_a = override === null || override === void 0 ? void 0 : override.prefixCls) === null || _a === void 0 ? void 0 : _a.value));\n    });\n    const [wrapSSR, hashId] = useStyle(prefixCls, computed(() => {\n      return !override;\n    }));\n    const store = shallowRef(new Map());\n    const siderCollapsed = inject(SiderCollapsedKey, ref(undefined));\n    const inlineCollapsed = computed(() => {\n      if (siderCollapsed.value !== undefined) {\n        return siderCollapsed.value;\n      }\n      return props.inlineCollapsed;\n    });\n    const {\n      itemsNodes\n    } = useItems(props);\n    const isMounted = shallowRef(false);\n    onMounted(() => {\n      isMounted.value = true;\n    });\n    watchEffect(() => {\n      devWarning(!(props.inlineCollapsed === true && props.mode !== 'inline'), 'Menu', '`inlineCollapsed` should only be used when `mode` is inline.');\n      devWarning(!(siderCollapsed.value !== undefined && props.inlineCollapsed === true), 'Menu', '`inlineCollapsed` not control Menu under Sider. Should set `collapsed` on Sider instead.');\n      // devWarning(\n      //   !!props.items && !slots.default,\n      //   'Menu',\n      //   '`children` will be removed in next major version. Please use `items` instead.',\n      // );\n    });\n    const activeKeys = ref([]);\n    const mergedSelectedKeys = ref([]);\n    const keyMapStore = ref({});\n    watch(store, () => {\n      const newKeyMapStore = {};\n      for (const menuInfo of store.value.values()) {\n        newKeyMapStore[menuInfo.key] = menuInfo;\n      }\n      keyMapStore.value = newKeyMapStore;\n    }, {\n      flush: 'post'\n    });\n    watchEffect(() => {\n      if (props.activeKey !== undefined) {\n        let keys = [];\n        const menuInfo = props.activeKey ? keyMapStore.value[props.activeKey] : undefined;\n        if (menuInfo && props.activeKey !== undefined) {\n          keys = uniq([].concat(unref(menuInfo.parentKeys), props.activeKey));\n        } else {\n          keys = [];\n        }\n        if (!shallowEqual(activeKeys.value, keys)) {\n          activeKeys.value = keys;\n        }\n      }\n    });\n    watch(() => props.selectedKeys, selectedKeys => {\n      if (selectedKeys) {\n        mergedSelectedKeys.value = selectedKeys.slice();\n      }\n    }, {\n      immediate: true,\n      deep: true\n    });\n    const selectedSubMenuKeys = ref([]);\n    watch([keyMapStore, mergedSelectedKeys], () => {\n      let subMenuParentKeys = [];\n      mergedSelectedKeys.value.forEach(key => {\n        const menuInfo = keyMapStore.value[key];\n        if (menuInfo) {\n          subMenuParentKeys = subMenuParentKeys.concat(unref(menuInfo.parentKeys));\n        }\n      });\n      subMenuParentKeys = uniq(subMenuParentKeys);\n      if (!shallowEqual(selectedSubMenuKeys.value, subMenuParentKeys)) {\n        selectedSubMenuKeys.value = subMenuParentKeys;\n      }\n    }, {\n      immediate: true\n    });\n    // >>>>> Trigger select\n    const triggerSelection = info => {\n      if (props.selectable) {\n        // Insert or Remove\n        const {\n          key: targetKey\n        } = info;\n        const exist = mergedSelectedKeys.value.includes(targetKey);\n        let newSelectedKeys;\n        if (props.multiple) {\n          if (exist) {\n            newSelectedKeys = mergedSelectedKeys.value.filter(key => key !== targetKey);\n          } else {\n            newSelectedKeys = [...mergedSelectedKeys.value, targetKey];\n          }\n        } else {\n          newSelectedKeys = [targetKey];\n        }\n        // Trigger event\n        const selectInfo = _extends(_extends({}, info), {\n          selectedKeys: newSelectedKeys\n        });\n        if (!shallowEqual(newSelectedKeys, mergedSelectedKeys.value)) {\n          if (props.selectedKeys === undefined) {\n            mergedSelectedKeys.value = newSelectedKeys;\n          }\n          emit('update:selectedKeys', newSelectedKeys);\n          if (exist && props.multiple) {\n            emit('deselect', selectInfo);\n          } else {\n            emit('select', selectInfo);\n          }\n        }\n      }\n      // Whatever selectable, always close it\n      if (mergedMode.value !== 'inline' && !props.multiple && mergedOpenKeys.value.length) {\n        triggerOpenKeys(EMPTY_LIST);\n      }\n    };\n    const mergedOpenKeys = ref([]);\n    watch(() => props.openKeys, function () {\n      let openKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : mergedOpenKeys.value;\n      if (!shallowEqual(mergedOpenKeys.value, openKeys)) {\n        mergedOpenKeys.value = openKeys.slice();\n      }\n    }, {\n      immediate: true,\n      deep: true\n    });\n    let timeout;\n    const changeActiveKeys = keys => {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        if (props.activeKey === undefined) {\n          activeKeys.value = keys;\n        }\n        emit('update:activeKey', keys[keys.length - 1]);\n      });\n    };\n    const disabled = computed(() => !!props.disabled);\n    const isRtl = computed(() => direction.value === 'rtl');\n    const mergedMode = ref('vertical');\n    const mergedInlineCollapsed = shallowRef(false);\n    watchEffect(() => {\n      var _a;\n      if ((props.mode === 'inline' || props.mode === 'vertical') && inlineCollapsed.value) {\n        mergedMode.value = 'vertical';\n        mergedInlineCollapsed.value = inlineCollapsed.value;\n      } else {\n        mergedMode.value = props.mode;\n        mergedInlineCollapsed.value = false;\n      }\n      if ((_a = override === null || override === void 0 ? void 0 : override.mode) === null || _a === void 0 ? void 0 : _a.value) {\n        mergedMode.value = override.mode.value;\n      }\n    });\n    const isInlineMode = computed(() => mergedMode.value === 'inline');\n    const triggerOpenKeys = keys => {\n      mergedOpenKeys.value = keys;\n      emit('update:openKeys', keys);\n      emit('openChange', keys);\n    };\n    // >>>>> Cache & Reset open keys when inlineCollapsed changed\n    const inlineCacheOpenKeys = ref(mergedOpenKeys.value);\n    const mountRef = shallowRef(false);\n    // Cache\n    watch(mergedOpenKeys, () => {\n      if (isInlineMode.value) {\n        inlineCacheOpenKeys.value = mergedOpenKeys.value;\n      }\n    }, {\n      immediate: true\n    });\n    // Restore\n    watch(isInlineMode, () => {\n      if (!mountRef.value) {\n        mountRef.value = true;\n        return;\n      }\n      if (isInlineMode.value) {\n        mergedOpenKeys.value = inlineCacheOpenKeys.value;\n      } else {\n        // Trigger open event in case its in control\n        triggerOpenKeys(EMPTY_LIST);\n      }\n    }, {\n      immediate: true\n    });\n    const className = computed(() => {\n      return {\n        [`${prefixCls.value}`]: true,\n        [`${prefixCls.value}-root`]: true,\n        [`${prefixCls.value}-${mergedMode.value}`]: true,\n        [`${prefixCls.value}-inline-collapsed`]: mergedInlineCollapsed.value,\n        [`${prefixCls.value}-rtl`]: isRtl.value,\n        [`${prefixCls.value}-${props.theme}`]: true\n      };\n    });\n    const rootPrefixCls = computed(() => getPrefixCls());\n    const defaultMotions = computed(() => ({\n      horizontal: {\n        name: `${rootPrefixCls.value}-slide-up`\n      },\n      inline: collapseMotion(`${rootPrefixCls.value}-motion-collapse`),\n      other: {\n        name: `${rootPrefixCls.value}-zoom-big`\n      }\n    }));\n    useProvideFirstLevel(true);\n    const getChildrenKeys = function () {\n      let eventKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n      const keys = [];\n      const storeValue = store.value;\n      eventKeys.forEach(eventKey => {\n        const {\n          key,\n          childrenEventKeys\n        } = storeValue.get(eventKey);\n        keys.push(key, ...getChildrenKeys(unref(childrenEventKeys)));\n      });\n      return keys;\n    };\n    // ========================= Open =========================\n    /**\n     * Click for item. SubMenu do not have selection status\n     */\n    const onInternalClick = info => {\n      var _a;\n      emit('click', info);\n      triggerSelection(info);\n      (_a = override === null || override === void 0 ? void 0 : override.onClick) === null || _a === void 0 ? void 0 : _a.call(override);\n    };\n    const onInternalOpenChange = (key, open) => {\n      var _a;\n      const childrenEventKeys = ((_a = keyMapStore.value[key]) === null || _a === void 0 ? void 0 : _a.childrenEventKeys) || [];\n      let newOpenKeys = mergedOpenKeys.value.filter(k => k !== key);\n      if (open) {\n        newOpenKeys.push(key);\n      } else if (mergedMode.value !== 'inline') {\n        // We need find all related popup to close\n        const subPathKeys = getChildrenKeys(unref(childrenEventKeys));\n        newOpenKeys = uniq(newOpenKeys.filter(k => !subPathKeys.includes(k)));\n      }\n      if (!shallowEqual(mergedOpenKeys, newOpenKeys)) {\n        triggerOpenKeys(newOpenKeys);\n      }\n    };\n    const registerMenuInfo = (key, info) => {\n      store.value.set(key, info);\n      store.value = new Map(store.value);\n    };\n    const unRegisterMenuInfo = key => {\n      store.value.delete(key);\n      store.value = new Map(store.value);\n    };\n    const lastVisibleIndex = ref(0);\n    const expandIcon = computed(() => {\n      var _a;\n      return props.expandIcon || slots.expandIcon || ((_a = override === null || override === void 0 ? void 0 : override.expandIcon) === null || _a === void 0 ? void 0 : _a.value) ? opt => {\n        let icon = props.expandIcon || slots.expandIcon;\n        icon = typeof icon === 'function' ? icon(opt) : icon;\n        return cloneElement(icon, {\n          class: `${prefixCls.value}-submenu-expand-icon`\n        }, false);\n      } : null;\n    });\n    useProvideMenu({\n      prefixCls,\n      activeKeys,\n      openKeys: mergedOpenKeys,\n      selectedKeys: mergedSelectedKeys,\n      changeActiveKeys,\n      disabled,\n      rtl: isRtl,\n      mode: mergedMode,\n      inlineIndent: computed(() => props.inlineIndent),\n      subMenuCloseDelay: computed(() => props.subMenuCloseDelay),\n      subMenuOpenDelay: computed(() => props.subMenuOpenDelay),\n      builtinPlacements: computed(() => props.builtinPlacements),\n      triggerSubMenuAction: computed(() => props.triggerSubMenuAction),\n      getPopupContainer: computed(() => props.getPopupContainer),\n      inlineCollapsed: mergedInlineCollapsed,\n      theme: computed(() => props.theme),\n      siderCollapsed,\n      defaultMotions: computed(() => isMounted.value ? defaultMotions.value : null),\n      motion: computed(() => isMounted.value ? props.motion : null),\n      overflowDisabled: shallowRef(undefined),\n      onOpenChange: onInternalOpenChange,\n      onItemClick: onInternalClick,\n      registerMenuInfo,\n      unRegisterMenuInfo,\n      selectedSubMenuKeys,\n      expandIcon,\n      forceSubMenuRender: computed(() => props.forceSubMenuRender),\n      rootClassName: hashId\n    });\n    const getChildrenList = () => {\n      var _a;\n      return itemsNodes.value || flattenChildren((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots));\n    };\n    return () => {\n      var _a;\n      const childList = getChildrenList();\n      const allVisible = lastVisibleIndex.value >= childList.length - 1 || mergedMode.value !== 'horizontal' || props.disabledOverflow;\n      // >>>>> Children\n      const getWrapperList = childList => {\n        return mergedMode.value !== 'horizontal' || props.disabledOverflow ? childList :\n        // Need wrap for overflow dropdown that do not response for open\n        childList.map((child, index) => // Always wrap provider to avoid sub node re-mount\n        _createVNode(MenuContextProvider, {\n          \"key\": child.key,\n          \"overflowDisabled\": index > lastVisibleIndex.value\n        }, {\n          default: () => child\n        }));\n      };\n      const overflowedIndicator = ((_a = slots.overflowedIndicator) === null || _a === void 0 ? void 0 : _a.call(slots)) || _createVNode(EllipsisOutlined, null, null);\n      return wrapSSR(_createVNode(Overflow, _objectSpread(_objectSpread({}, attrs), {}, {\n        \"onMousedown\": props.onMousedown,\n        \"prefixCls\": `${prefixCls.value}-overflow`,\n        \"component\": \"ul\",\n        \"itemComponent\": MenuItem,\n        \"class\": [className.value, attrs.class, hashId.value],\n        \"role\": \"menu\",\n        \"id\": props.id,\n        \"data\": getWrapperList(childList),\n        \"renderRawItem\": node => node,\n        \"renderRawRest\": omitItems => {\n          // We use origin list since wrapped list use context to prevent open\n          const len = omitItems.length;\n          const originOmitItems = len ? childList.slice(-len) : null;\n          return _createVNode(_Fragment, null, [_createVNode(SubMenu, {\n            \"eventKey\": OVERFLOW_KEY,\n            \"key\": OVERFLOW_KEY,\n            \"title\": overflowedIndicator,\n            \"disabled\": allVisible,\n            \"internalPopupClose\": len === 0\n          }, {\n            default: () => originOmitItems\n          }), _createVNode(PathContext, null, {\n            default: () => [_createVNode(SubMenu, {\n              \"eventKey\": OVERFLOW_KEY,\n              \"key\": OVERFLOW_KEY,\n              \"title\": overflowedIndicator,\n              \"disabled\": allVisible,\n              \"internalPopupClose\": len === 0\n            }, {\n              default: () => originOmitItems\n            })]\n          })]);\n        },\n        \"maxCount\": mergedMode.value !== 'horizontal' || props.disabledOverflow ? Overflow.INVALIDATE : Overflow.RESPONSIVE,\n        \"ssr\": \"full\",\n        \"data-menu-list\": true,\n        \"onVisibleChange\": newLastIndex => {\n          lastVisibleIndex.value = newLastIndex;\n        }\n      }), {\n        default: () => [_createVNode(Teleport, {\n          \"to\": \"body\"\n        }, {\n          default: () => [_createVNode(\"div\", {\n            \"style\": {\n              display: 'none'\n            },\n            \"aria-hidden\": true\n          }, [_createVNode(PathContext, null, {\n            default: () => [getWrapperList(getChildrenList())]\n          })])]\n        })]\n      }));\n    };\n  }\n});", "import Menu from './src/Menu';\nimport MenuItem from './src/MenuItem';\nimport SubMenu from './src/SubMenu';\nimport ItemGroup from './src/ItemGroup';\nimport Divider from './src/Divider';\n/* istanbul ignore next */\nMenu.install = function (app) {\n  app.component(Menu.name, Menu);\n  app.component(MenuItem.name, MenuItem);\n  app.component(SubMenu.name, SubMenu);\n  app.component(Divider.name, Divider);\n  app.component(ItemGroup.name, ItemGroup);\n  return app;\n};\nMenu.Item = MenuItem;\nMenu.Divider = Divider;\nMenu.SubMenu = SubMenu;\nMenu.ItemGroup = ItemGroup;\nexport { SubMenu, MenuItem as Item, MenuItem, ItemGroup, ItemGroup as MenuItemGroup, Divider, Divider as MenuDivider };\nexport default Menu;", "const MentionsContextKey = Symbol('MentionsContextKey');\nexport default MentionsContextKey;", "/* eslint-disable no-undefined,no-param-reassign,no-shadow */\n\n/**\n * Throttle execution of a function. Especially useful for rate limiting\n * execution of handlers on events like resize and scroll.\n *\n * @param {number} delay -                  A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher)\n *                                            are most useful.\n * @param {Function} callback -               A function to be executed after delay milliseconds. The `this` context and all arguments are passed through,\n *                                            as-is, to `callback` when the throttled-function is executed.\n * @param {object} [options] -              An object to configure options.\n * @param {boolean} [options.noTrailing] -   Optional, defaults to false. If noTrailing is true, callback will only execute every `delay` milliseconds\n *                                            while the throttled-function is being called. If noTrailing is false or unspecified, callback will be executed\n *                                            one final time after the last throttled-function call. (After the throttled-function has not been called for\n *                                            `delay` milliseconds, the internal counter is reset).\n * @param {boolean} [options.noLeading] -   Optional, defaults to false. If noLeading is false, the first throttled-function call will execute callback\n *                                            immediately. If noLeading is true, the first the callback execution will be skipped. It should be noted that\n *                                            callback will never executed if both noLeading = true and noTrailing = true.\n * @param {boolean} [options.debounceMode] - If `debounceMode` is true (at begin), schedule `clear` to execute after `delay` ms. If `debounceMode` is\n *                                            false (at end), schedule `callback` to execute after `delay` ms.\n *\n * @returns {Function} A new, throttled, function.\n */\nexport default function (delay, callback, options) {\n\tconst {\n\t\tnoTrailing = false,\n\t\tnoLeading = false,\n\t\tdebounceMode = undefined\n\t} = options || {};\n\t/*\n\t * After wrapper has stopped being called, this timeout ensures that\n\t * `callback` is executed at the proper times in `throttle` and `end`\n\t * debounce modes.\n\t */\n\tlet timeoutID;\n\tlet cancelled = false;\n\n\t// Keep track of the last time `callback` was executed.\n\tlet lastExec = 0;\n\n\t// Function to clear existing timeout\n\tfunction clearExistingTimeout() {\n\t\tif (timeoutID) {\n\t\t\tclearTimeout(timeoutID);\n\t\t}\n\t}\n\n\t// Function to cancel next exec\n\tfunction cancel(options) {\n\t\tconst { upcomingOnly = false } = options || {};\n\t\tclearExistingTimeout();\n\t\tcancelled = !upcomingOnly;\n\t}\n\n\t/*\n\t * The `wrapper` function encapsulates all of the throttling / debouncing\n\t * functionality and when executed will limit the rate at which `callback`\n\t * is executed.\n\t */\n\tfunction wrapper(...arguments_) {\n\t\tlet self = this;\n\t\tlet elapsed = Date.now() - lastExec;\n\n\t\tif (cancelled) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Execute `callback` and update the `lastExec` timestamp.\n\t\tfunction exec() {\n\t\t\tlastExec = Date.now();\n\t\t\tcallback.apply(self, arguments_);\n\t\t}\n\n\t\t/*\n\t\t * If `debounceMode` is true (at begin) this is used to clear the flag\n\t\t * to allow future `callback` executions.\n\t\t */\n\t\tfunction clear() {\n\t\t\ttimeoutID = undefined;\n\t\t}\n\n\t\tif (!noLeading && debounceMode && !timeoutID) {\n\t\t\t/*\n\t\t\t * Since `wrapper` is being called for the first time and\n\t\t\t * `debounceMode` is true (at begin), execute `callback`\n\t\t\t * and noLeading != true.\n\t\t\t */\n\t\t\texec();\n\t\t}\n\n\t\tclearExistingTimeout();\n\n\t\tif (debounceMode === undefined && elapsed > delay) {\n\t\t\tif (noLeading) {\n\t\t\t\t/*\n\t\t\t\t * In throttle mode with noLeading, if `delay` time has\n\t\t\t\t * been exceeded, update `lastExec` and schedule `callback`\n\t\t\t\t * to execute after `delay` ms.\n\t\t\t\t */\n\t\t\t\tlastExec = Date.now();\n\t\t\t\tif (!noTrailing) {\n\t\t\t\t\ttimeoutID = setTimeout(debounceMode ? clear : exec, delay);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t/*\n\t\t\t\t * In throttle mode without noLeading, if `delay` time has been exceeded, execute\n\t\t\t\t * `callback`.\n\t\t\t\t */\n\t\t\t\texec();\n\t\t\t}\n\t\t} else if (noTrailing !== true) {\n\t\t\t/*\n\t\t\t * In trailing throttle mode, since `delay` time has not been\n\t\t\t * exceeded, schedule `callback` to execute `delay` ms after most\n\t\t\t * recent execution.\n\t\t\t *\n\t\t\t * If `debounceMode` is true (at begin), schedule `clear` to execute\n\t\t\t * after `delay` ms.\n\t\t\t *\n\t\t\t * If `debounceMode` is false (at end), schedule `callback` to\n\t\t\t * execute after `delay` ms.\n\t\t\t */\n\t\t\ttimeoutID = setTimeout(\n\t\t\t\tdebounceMode ? clear : exec,\n\t\t\t\tdebounceMode === undefined ? delay - elapsed : delay\n\t\t\t);\n\t\t}\n\t}\n\n\twrapper.cancel = cancel;\n\n\t// Return the wrapper function.\n\treturn wrapper;\n}\n", "/* eslint-disable no-undefined */\n\nimport throttle from './throttle.js';\n\n/**\n * Debounce execution of a function. Debouncing, unlike throttling,\n * guarantees that a function is only executed a single time, either at the\n * very beginning of a series of calls, or at the very end.\n *\n * @param {number} delay -               A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param {Function} callback -          A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                        to `callback` when the debounced-function is executed.\n * @param {object} [options] -           An object to configure options.\n * @param {boolean} [options.atBegin] -  Optional, defaults to false. If atBegin is false or unspecified, callback will only be executed `delay` milliseconds\n *                                        after the last debounced-function call. If atBegin is true, callback will be executed only at the first debounced-function call.\n *                                        (After the throttled-function has not been called for `delay` milliseconds, the internal counter is reset).\n *\n * @returns {Function} A new, debounced function.\n */\nexport default function (delay, callback, options) {\n\tconst { atBegin = false } = options || {};\n\treturn throttle(delay, callback, { debounceMode: atBegin !== false });\n}\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { Keyframes } from '../../_util/cssinjs';\nimport { genComponentStyleHook, mergeToken } from '../../theme/internal';\nimport { resetComponent } from '../../style';\nconst antSpinMove = new Keyframes('antSpinMove', {\n  to: {\n    opacity: 1\n  }\n});\nconst antRotate = new Keyframes('antRotate', {\n  to: {\n    transform: 'rotate(405deg)'\n  }\n});\nconst genSpinStyle = token => ({\n  [`${token.componentCls}`]: _extends(_extends({}, resetComponent(token)), {\n    position: 'absolute',\n    display: 'none',\n    color: token.colorPrimary,\n    textAlign: 'center',\n    verticalAlign: 'middle',\n    opacity: 0,\n    transition: `transform ${token.motionDurationSlow} ${token.motionEaseInOutCirc}`,\n    '&-spinning': {\n      position: 'static',\n      display: 'inline-block',\n      opacity: 1\n    },\n    '&-nested-loading': {\n      position: 'relative',\n      [`> div > ${token.componentCls}`]: {\n        position: 'absolute',\n        top: 0,\n        insetInlineStart: 0,\n        zIndex: 4,\n        display: 'block',\n        width: '100%',\n        height: '100%',\n        maxHeight: token.contentHeight,\n        [`${token.componentCls}-dot`]: {\n          position: 'absolute',\n          top: '50%',\n          insetInlineStart: '50%',\n          margin: -token.spinDotSize / 2\n        },\n        [`${token.componentCls}-text`]: {\n          position: 'absolute',\n          top: '50%',\n          width: '100%',\n          paddingTop: (token.spinDotSize - token.fontSize) / 2 + 2,\n          textShadow: `0 1px 2px ${token.colorBgContainer}` // FIXME: shadow\n        },\n        [`&${token.componentCls}-show-text ${token.componentCls}-dot`]: {\n          marginTop: -(token.spinDotSize / 2) - 10\n        },\n        '&-sm': {\n          [`${token.componentCls}-dot`]: {\n            margin: -token.spinDotSizeSM / 2\n          },\n          [`${token.componentCls}-text`]: {\n            paddingTop: (token.spinDotSizeSM - token.fontSize) / 2 + 2\n          },\n          [`&${token.componentCls}-show-text ${token.componentCls}-dot`]: {\n            marginTop: -(token.spinDotSizeSM / 2) - 10\n          }\n        },\n        '&-lg': {\n          [`${token.componentCls}-dot`]: {\n            margin: -(token.spinDotSizeLG / 2)\n          },\n          [`${token.componentCls}-text`]: {\n            paddingTop: (token.spinDotSizeLG - token.fontSize) / 2 + 2\n          },\n          [`&${token.componentCls}-show-text ${token.componentCls}-dot`]: {\n            marginTop: -(token.spinDotSizeLG / 2) - 10\n          }\n        }\n      },\n      [`${token.componentCls}-container`]: {\n        position: 'relative',\n        transition: `opacity ${token.motionDurationSlow}`,\n        '&::after': {\n          position: 'absolute',\n          top: 0,\n          insetInlineEnd: 0,\n          bottom: 0,\n          insetInlineStart: 0,\n          zIndex: 10,\n          width: '100%',\n          height: '100%',\n          background: token.colorBgContainer,\n          opacity: 0,\n          transition: `all ${token.motionDurationSlow}`,\n          content: '\"\"',\n          pointerEvents: 'none'\n        }\n      },\n      [`${token.componentCls}-blur`]: {\n        clear: 'both',\n        opacity: 0.5,\n        userSelect: 'none',\n        pointerEvents: 'none',\n        [`&::after`]: {\n          opacity: 0.4,\n          pointerEvents: 'auto'\n        }\n      }\n    },\n    // tip\n    // ------------------------------\n    [`&-tip`]: {\n      color: token.spinDotDefault\n    },\n    // dots\n    // ------------------------------\n    [`${token.componentCls}-dot`]: {\n      position: 'relative',\n      display: 'inline-block',\n      fontSize: token.spinDotSize,\n      width: '1em',\n      height: '1em',\n      '&-item': {\n        position: 'absolute',\n        display: 'block',\n        width: (token.spinDotSize - token.marginXXS / 2) / 2,\n        height: (token.spinDotSize - token.marginXXS / 2) / 2,\n        backgroundColor: token.colorPrimary,\n        borderRadius: '100%',\n        transform: 'scale(0.75)',\n        transformOrigin: '50% 50%',\n        opacity: 0.3,\n        animationName: antSpinMove,\n        animationDuration: '1s',\n        animationIterationCount: 'infinite',\n        animationTimingFunction: 'linear',\n        animationDirection: 'alternate',\n        '&:nth-child(1)': {\n          top: 0,\n          insetInlineStart: 0\n        },\n        '&:nth-child(2)': {\n          top: 0,\n          insetInlineEnd: 0,\n          animationDelay: '0.4s'\n        },\n        '&:nth-child(3)': {\n          insetInlineEnd: 0,\n          bottom: 0,\n          animationDelay: '0.8s'\n        },\n        '&:nth-child(4)': {\n          bottom: 0,\n          insetInlineStart: 0,\n          animationDelay: '1.2s'\n        }\n      },\n      '&-spin': {\n        transform: 'rotate(45deg)',\n        animationName: antRotate,\n        animationDuration: '1.2s',\n        animationIterationCount: 'infinite',\n        animationTimingFunction: 'linear'\n      }\n    },\n    // Sizes\n    // ------------------------------\n    // small\n    [`&-sm ${token.componentCls}-dot`]: {\n      fontSize: token.spinDotSizeSM,\n      i: {\n        width: (token.spinDotSizeSM - token.marginXXS / 2) / 2,\n        height: (token.spinDotSizeSM - token.marginXXS / 2) / 2\n      }\n    },\n    // large\n    [`&-lg ${token.componentCls}-dot`]: {\n      fontSize: token.spinDotSizeLG,\n      i: {\n        width: (token.spinDotSizeLG - token.marginXXS) / 2,\n        height: (token.spinDotSizeLG - token.marginXXS) / 2\n      }\n    },\n    [`&${token.componentCls}-show-text ${token.componentCls}-text`]: {\n      display: 'block'\n    }\n  })\n});\n// ============================== Export ==============================\nexport default genComponentStyleHook('Spin', token => {\n  const spinToken = mergeToken(token, {\n    spinDotDefault: token.colorTextDescription,\n    spinDotSize: token.controlHeightLG / 2,\n    spinDotSizeSM: token.controlHeightLG * 0.35,\n    spinDotSizeLG: token.controlHeight\n  });\n  return [genSpinStyle(spinToken)];\n}, {\n  contentHeight: 400\n});", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { createVNode as _createVNode } from \"vue\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { onBeforeUnmount, cloneVNode, isVNode, defineComponent, shallowRef, watch } from 'vue';\nimport { debounce } from 'throttle-debounce';\nimport PropTypes from '../_util/vue-types';\nimport { filterEmpty, getPropsSlot } from '../_util/props-util';\nimport initDefaultProps from '../_util/props-util/initDefaultProps';\nimport useStyle from './style';\nimport useConfigInject from '../config-provider/hooks/useConfigInject';\nexport const spinProps = () => ({\n  prefixCls: String,\n  spinning: {\n    type: Boolean,\n    default: undefined\n  },\n  size: String,\n  wrapperClassName: String,\n  tip: PropTypes.any,\n  delay: Number,\n  indicator: PropTypes.any\n});\n// Render indicator\nlet defaultIndicator = null;\nfunction shouldDelay(spinning, delay) {\n  return !!spinning && !!delay && !isNaN(Number(delay));\n}\nexport function setDefaultIndicator(Content) {\n  const Indicator = Content.indicator;\n  defaultIndicator = typeof Indicator === 'function' ? Indicator : () => _createVNode(Indicator, null, null);\n}\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'ASpin',\n  inheritAttrs: false,\n  props: initDefaultProps(spinProps(), {\n    size: 'default',\n    spinning: true,\n    wrapperClassName: ''\n  }),\n  setup(props, _ref) {\n    let {\n      attrs,\n      slots\n    } = _ref;\n    const {\n      prefixCls,\n      size,\n      direction\n    } = useConfigInject('spin', props);\n    const [wrapSSR, hashId] = useStyle(prefixCls);\n    const sSpinning = shallowRef(props.spinning && !shouldDelay(props.spinning, props.delay));\n    let updateSpinning;\n    watch([() => props.spinning, () => props.delay], () => {\n      updateSpinning === null || updateSpinning === void 0 ? void 0 : updateSpinning.cancel();\n      updateSpinning = debounce(props.delay, () => {\n        sSpinning.value = props.spinning;\n      });\n      updateSpinning === null || updateSpinning === void 0 ? void 0 : updateSpinning();\n    }, {\n      immediate: true,\n      flush: 'post'\n    });\n    onBeforeUnmount(() => {\n      updateSpinning === null || updateSpinning === void 0 ? void 0 : updateSpinning.cancel();\n    });\n    return () => {\n      var _a, _b;\n      const {\n          class: cls\n        } = attrs,\n        divProps = __rest(attrs, [\"class\"]);\n      const {\n        tip = (_a = slots.tip) === null || _a === void 0 ? void 0 : _a.call(slots)\n      } = props;\n      const children = (_b = slots.default) === null || _b === void 0 ? void 0 : _b.call(slots);\n      const spinClassName = {\n        [hashId.value]: true,\n        [prefixCls.value]: true,\n        [`${prefixCls.value}-sm`]: size.value === 'small',\n        [`${prefixCls.value}-lg`]: size.value === 'large',\n        [`${prefixCls.value}-spinning`]: sSpinning.value,\n        [`${prefixCls.value}-show-text`]: !!tip,\n        [`${prefixCls.value}-rtl`]: direction.value === 'rtl',\n        [cls]: !!cls\n      };\n      function renderIndicator(prefixCls) {\n        const dotClassName = `${prefixCls}-dot`;\n        let indicator = getPropsSlot(slots, props, 'indicator');\n        // should not be render default indicator when indicator value is null\n        if (indicator === null) {\n          return null;\n        }\n        if (Array.isArray(indicator)) {\n          indicator = indicator.length === 1 ? indicator[0] : indicator;\n        }\n        if (isVNode(indicator)) {\n          return cloneVNode(indicator, {\n            class: dotClassName\n          });\n        }\n        if (defaultIndicator && isVNode(defaultIndicator())) {\n          return cloneVNode(defaultIndicator(), {\n            class: dotClassName\n          });\n        }\n        return _createVNode(\"span\", {\n          \"class\": `${dotClassName} ${prefixCls}-dot-spin`\n        }, [_createVNode(\"i\", {\n          \"class\": `${prefixCls}-dot-item`\n        }, null), _createVNode(\"i\", {\n          \"class\": `${prefixCls}-dot-item`\n        }, null), _createVNode(\"i\", {\n          \"class\": `${prefixCls}-dot-item`\n        }, null), _createVNode(\"i\", {\n          \"class\": `${prefixCls}-dot-item`\n        }, null)]);\n      }\n      const spinElement = _createVNode(\"div\", _objectSpread(_objectSpread({}, divProps), {}, {\n        \"class\": spinClassName,\n        \"aria-live\": \"polite\",\n        \"aria-busy\": sSpinning.value\n      }), [renderIndicator(prefixCls.value), tip ? _createVNode(\"div\", {\n        \"class\": `${prefixCls.value}-text`\n      }, [tip]) : null]);\n      if (children && filterEmpty(children).length) {\n        const containerClassName = {\n          [`${prefixCls.value}-container`]: true,\n          [`${prefixCls.value}-blur`]: sSpinning.value\n        };\n        return wrapSSR(_createVNode(\"div\", {\n          \"class\": [`${prefixCls.value}-nested-loading`, props.wrapperClassName, hashId.value]\n        }, [sSpinning.value && _createVNode(\"div\", {\n          \"key\": \"loading\"\n        }, [spinElement]), _createVNode(\"div\", {\n          \"class\": containerClassName,\n          \"key\": \"container\"\n        }, [children])]));\n      }\n      return wrapSSR(spinElement);\n    };\n  }\n});", "import Spin, { setDefaultIndicator } from './Spin';\nexport { spinProps } from './Spin';\nSpin.setDefaultIndicator = setDefaultIndicator;\n/* istanbul ignore next */\nSpin.install = function (app) {\n  app.component(Spin.name, Spin);\n  return app;\n};\nexport default Spin;", "import { createVNode as _createVNode } from \"vue\";\nimport Menu, { Item as MenuItem } from '../../menu';\nimport { onBeforeUnmount, defineComponent, inject, shallowRef } from 'vue';\nimport MentionsContextKey from './MentionsContext';\nimport Spin from '../../spin';\nfunction noop() {}\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'DropdownMenu',\n  props: {\n    prefixCls: String,\n    options: {\n      type: Array,\n      default: () => []\n    }\n  },\n  setup(props, _ref) {\n    let {\n      slots\n    } = _ref;\n    const {\n      activeIndex,\n      setActiveIndex,\n      selectOption,\n      onFocus = noop,\n      loading\n    } = inject(MentionsContextKey, {\n      activeIndex: shallowRef(),\n      loading: shallowRef(false)\n    });\n    let timeoutId;\n    const onMousedown = e => {\n      clearTimeout(timeoutId);\n      timeoutId = setTimeout(() => {\n        onFocus(e);\n      });\n    };\n    onBeforeUnmount(() => {\n      clearTimeout(timeoutId);\n    });\n    return () => {\n      var _a;\n      const {\n        prefixCls,\n        options\n      } = props;\n      const activeOption = options[activeIndex.value] || {};\n      return _createVNode(Menu, {\n        \"prefixCls\": `${prefixCls}-menu`,\n        \"activeKey\": activeOption.value,\n        \"onSelect\": _ref2 => {\n          let {\n            key\n          } = _ref2;\n          const option = options.find(_ref3 => {\n            let {\n              value\n            } = _ref3;\n            return value === key;\n          });\n          selectOption(option);\n        },\n        \"onMousedown\": onMousedown\n      }, {\n        default: () => [!loading.value && options.map((option, index) => {\n          var _a, _b;\n          const {\n            value,\n            disabled,\n            label = option.value,\n            class: className,\n            style\n          } = option;\n          return _createVNode(MenuItem, {\n            \"key\": value,\n            \"disabled\": disabled,\n            \"onMouseenter\": () => {\n              setActiveIndex(index);\n            },\n            \"class\": className,\n            \"style\": style\n          }, {\n            default: () => [(_b = (_a = slots.option) === null || _a === void 0 ? void 0 : _a.call(slots, option)) !== null && _b !== void 0 ? _b : typeof label === 'function' ? label(option) : label]\n          });\n        }), !loading.value && options.length === 0 ? _createVNode(MenuItem, {\n          \"key\": \"notFoundContent\",\n          \"disabled\": true\n        }, {\n          default: () => [(_a = slots.notFoundContent) === null || _a === void 0 ? void 0 : _a.call(slots)]\n        }) : null, loading.value && _createVNode(MenuItem, {\n          \"key\": \"loading\",\n          \"disabled\": true\n        }, {\n          default: () => [_createVNode(Spin, {\n            \"size\": \"small\"\n          }, null)]\n        })]\n      });\n    };\n  }\n});", "import { createVNode as _createVNode, resolveDirective as _resolveDirective } from \"vue\";\nimport Trigger from '../../vc-trigger';\nimport DropdownMenu from './DropdownMenu';\nimport { computed, defineComponent } from 'vue';\nconst BUILT_IN_PLACEMENTS = {\n  bottomRight: {\n    points: ['tl', 'br'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 0,\n      adjustY: 1\n    }\n  },\n  bottomLeft: {\n    points: ['tr', 'bl'],\n    offset: [0, 4],\n    overflow: {\n      adjustX: 0,\n      adjustY: 1\n    }\n  },\n  topRight: {\n    points: ['bl', 'tr'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 0,\n      adjustY: 1\n    }\n  },\n  topLeft: {\n    points: ['br', 'tl'],\n    offset: [0, -4],\n    overflow: {\n      adjustX: 0,\n      adjustY: 1\n    }\n  }\n};\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'KeywordTrigger',\n  props: {\n    loading: {\n      type: Boolean,\n      default: undefined\n    },\n    options: {\n      type: Array,\n      default: () => []\n    },\n    prefixCls: String,\n    placement: String,\n    visible: {\n      type: Boolean,\n      default: undefined\n    },\n    transitionName: String,\n    getPopupContainer: Function,\n    direction: String,\n    dropdownClassName: String\n  },\n  setup(props, _ref) {\n    let {\n      slots\n    } = _ref;\n    const getDropdownPrefix = () => {\n      return `${props.prefixCls}-dropdown`;\n    };\n    const getDropdownElement = () => {\n      const {\n        options\n      } = props;\n      return _createVNode(DropdownMenu, {\n        \"prefixCls\": getDropdownPrefix(),\n        \"options\": options\n      }, {\n        notFoundContent: slots.notFoundContent,\n        option: slots.option\n      });\n    };\n    const popupPlacement = computed(() => {\n      const {\n        placement,\n        direction\n      } = props;\n      let popupPlacement = 'topRight';\n      if (direction === 'rtl') {\n        popupPlacement = placement === 'top' ? 'topLeft' : 'bottomLeft';\n      } else {\n        popupPlacement = placement === 'top' ? 'topRight' : 'bottomRight';\n      }\n      return popupPlacement;\n    });\n    return () => {\n      const {\n        visible,\n        transitionName,\n        getPopupContainer\n      } = props;\n      return _createVNode(Trigger, {\n        \"prefixCls\": getDropdownPrefix(),\n        \"popupVisible\": visible,\n        \"popup\": getDropdownElement(),\n        \"popupClassName\": props.dropdownClassName,\n        \"popupPlacement\": popupPlacement.value,\n        \"popupTransitionName\": transitionName,\n        \"builtinPlacements\": BUILT_IN_PLACEMENTS,\n        \"getPopupContainer\": getPopupContainer\n      }, {\n        default: slots.default\n      });\n    };\n  }\n});", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport PropTypes from '../../_util/vue-types';\nimport { initDefaultProps } from '../../_util/props-util';\nimport { filterOption as defaultFilterOption, validateSearch as defaultValidateSearch } from './util';\nimport { arrayType, tuple } from '../../_util/type';\nexport const PlaceMent = tuple('top', 'bottom');\nexport const mentionsProps = {\n  autofocus: {\n    type: Boolean,\n    default: undefined\n  },\n  prefix: PropTypes.oneOfType([PropTypes.string, PropTypes.arrayOf(PropTypes.string)]),\n  prefixCls: String,\n  value: String,\n  disabled: {\n    type: Boolean,\n    default: undefined\n  },\n  split: String,\n  transitionName: String,\n  placement: PropTypes.oneOf(PlaceMent),\n  character: PropTypes.any,\n  characterRender: Function,\n  filterOption: {\n    type: [<PERSON>olean, Function]\n  },\n  validateSearch: Function,\n  getPopupContainer: {\n    type: Function\n  },\n  options: arrayType(),\n  loading: {\n    type: <PERSON>olean,\n    default: undefined\n  },\n  rows: [Number, String],\n  direction: {\n    type: String\n  }\n};\nexport const vcMentionsProps = _extends(_extends({}, mentionsProps), {\n  dropdownClassName: String\n});\nexport const defaultProps = {\n  prefix: '@',\n  split: ' ',\n  rows: 1,\n  validateSearch: defaultValidateSearch,\n  filterOption: () => defaultFilterOption\n};\nexport default initDefaultProps(vcMentionsProps, defaultProps);", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { resolveDirective as _resolveDirective, createVNode as _createVNode } from \"vue\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { toRef, watchEffect, defineComponent, provide, ref, reactive, onUpdated, nextTick, computed } from 'vue';\nimport classNames from '../../_util/classNames';\nimport KeyCode from '../../_util/KeyCode';\nimport { initDefaultProps } from '../../_util/props-util';\nimport { getBeforeSelectionText, getLastMeasureIndex, replaceWithMeasure, setInputSelection } from './util';\nimport KeywordTrigger from './KeywordTrigger';\nimport { vcMentionsProps, defaultProps } from './mentionsProps';\nimport MentionsContextKey from './MentionsContext';\nimport omit from '../../_util/omit';\nimport BaseInput from '../../_util/BaseInput';\nfunction noop() {}\nexport default defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'Mentions',\n  inheritAttrs: false,\n  props: initDefaultProps(vcMentionsProps, defaultProps),\n  emits: ['change', 'select', 'search', 'focus', 'blur', 'pressenter'],\n  setup(props, _ref) {\n    let {\n      emit,\n      attrs,\n      expose,\n      slots\n    } = _ref;\n    const measure = ref(null);\n    const textarea = ref(null);\n    const focusId = ref();\n    const state = reactive({\n      value: props.value || '',\n      measuring: false,\n      measureLocation: 0,\n      measureText: null,\n      measurePrefix: '',\n      activeIndex: 0,\n      isFocus: false\n    });\n    watchEffect(() => {\n      state.value = props.value;\n    });\n    const triggerChange = val => {\n      emit('change', val);\n    };\n    const onChange = _ref2 => {\n      let {\n        target: {\n          value\n        }\n      } = _ref2;\n      triggerChange(value);\n    };\n    const startMeasure = (measureText, measurePrefix, measureLocation) => {\n      _extends(state, {\n        measuring: true,\n        measureText,\n        measurePrefix,\n        measureLocation,\n        activeIndex: 0\n      });\n    };\n    const stopMeasure = callback => {\n      _extends(state, {\n        measuring: false,\n        measureLocation: 0,\n        measureText: null\n      });\n      callback === null || callback === void 0 ? void 0 : callback();\n    };\n    const onKeyDown = event => {\n      const {\n        which\n      } = event;\n      // Skip if not measuring\n      if (!state.measuring) {\n        return;\n      }\n      if (which === KeyCode.UP || which === KeyCode.DOWN) {\n        // Control arrow function\n        const optionLen = options.value.length;\n        const offset = which === KeyCode.UP ? -1 : 1;\n        const newActiveIndex = (state.activeIndex + offset + optionLen) % optionLen;\n        state.activeIndex = newActiveIndex;\n        event.preventDefault();\n      } else if (which === KeyCode.ESC) {\n        stopMeasure();\n      } else if (which === KeyCode.ENTER) {\n        // Measure hit\n        event.preventDefault();\n        if (!options.value.length) {\n          stopMeasure();\n          return;\n        }\n        const option = options.value[state.activeIndex];\n        selectOption(option);\n      }\n    };\n    const onKeyUp = event => {\n      const {\n        key,\n        which\n      } = event;\n      const {\n        measureText: prevMeasureText,\n        measuring\n      } = state;\n      const {\n        prefix,\n        validateSearch\n      } = props;\n      const target = event.target;\n      if (target.composing) {\n        return;\n      }\n      const selectionStartText = getBeforeSelectionText(target);\n      const {\n        location: measureIndex,\n        prefix: measurePrefix\n      } = getLastMeasureIndex(selectionStartText, prefix);\n      // Skip if match the white key list\n      if ([KeyCode.ESC, KeyCode.UP, KeyCode.DOWN, KeyCode.ENTER].indexOf(which) !== -1) {\n        return;\n      }\n      if (measureIndex !== -1) {\n        const measureText = selectionStartText.slice(measureIndex + measurePrefix.length);\n        const validateMeasure = validateSearch(measureText, props);\n        const matchOption = !!getOptions(measureText).length;\n        if (validateMeasure) {\n          if (key === measurePrefix || key === 'Shift' || measuring || measureText !== prevMeasureText && matchOption) {\n            startMeasure(measureText, measurePrefix, measureIndex);\n          }\n        } else if (measuring) {\n          // Stop if measureText is invalidate\n          stopMeasure();\n        }\n        /**\n         * We will trigger `onSearch` to developer since they may use for async update.\n         * If met `space` means user finished searching.\n         */\n        if (validateMeasure) {\n          emit('search', measureText, measurePrefix);\n        }\n      } else if (measuring) {\n        stopMeasure();\n      }\n    };\n    const onPressEnter = event => {\n      if (!state.measuring) {\n        emit('pressenter', event);\n      }\n    };\n    const onInputFocus = event => {\n      onFocus(event);\n    };\n    const onInputBlur = event => {\n      onBlur(event);\n    };\n    const onFocus = event => {\n      clearTimeout(focusId.value);\n      const {\n        isFocus\n      } = state;\n      if (!isFocus && event) {\n        emit('focus', event);\n      }\n      state.isFocus = true;\n    };\n    const onBlur = event => {\n      focusId.value = setTimeout(() => {\n        state.isFocus = false;\n        stopMeasure();\n        emit('blur', event);\n      }, 100);\n    };\n    const selectOption = option => {\n      const {\n        split\n      } = props;\n      const {\n        value: mentionValue = ''\n      } = option;\n      const {\n        text,\n        selectionLocation\n      } = replaceWithMeasure(state.value, {\n        measureLocation: state.measureLocation,\n        targetText: mentionValue,\n        prefix: state.measurePrefix,\n        selectionStart: textarea.value.getSelectionStart(),\n        split\n      });\n      triggerChange(text);\n      stopMeasure(() => {\n        // We need restore the selection position\n        setInputSelection(textarea.value.input, selectionLocation);\n      });\n      emit('select', option, state.measurePrefix);\n    };\n    const setActiveIndex = activeIndex => {\n      state.activeIndex = activeIndex;\n    };\n    const getOptions = measureText => {\n      const targetMeasureText = measureText || state.measureText || '';\n      const {\n        filterOption\n      } = props;\n      const list = props.options.filter(option => {\n        /** Return all result if `filterOption` is false. */\n        if (!!filterOption === false) {\n          return true;\n        }\n        return filterOption(targetMeasureText, option);\n      });\n      return list;\n    };\n    const options = computed(() => {\n      return getOptions();\n    });\n    const focus = () => {\n      textarea.value.focus();\n    };\n    const blur = () => {\n      textarea.value.blur();\n    };\n    expose({\n      blur,\n      focus\n    });\n    provide(MentionsContextKey, {\n      activeIndex: toRef(state, 'activeIndex'),\n      setActiveIndex,\n      selectOption,\n      onFocus,\n      onBlur,\n      loading: toRef(props, 'loading')\n    });\n    onUpdated(() => {\n      nextTick(() => {\n        if (state.measuring) {\n          measure.value.scrollTop = textarea.value.getScrollTop();\n        }\n      });\n    });\n    return () => {\n      const {\n        measureLocation,\n        measurePrefix,\n        measuring\n      } = state;\n      const {\n          prefixCls,\n          placement,\n          transitionName,\n          getPopupContainer,\n          direction\n        } = props,\n        restProps = __rest(props, [\"prefixCls\", \"placement\", \"transitionName\", \"getPopupContainer\", \"direction\"]);\n      const {\n          class: className,\n          style\n        } = attrs,\n        otherAttrs = __rest(attrs, [\"class\", \"style\"]);\n      const inputProps = omit(restProps, ['value', 'prefix', 'split', 'validateSearch', 'filterOption', 'options', 'loading']);\n      const textareaProps = _extends(_extends(_extends({}, inputProps), otherAttrs), {\n        onChange: noop,\n        onSelect: noop,\n        value: state.value,\n        onInput: onChange,\n        onBlur: onInputBlur,\n        onKeydown: onKeyDown,\n        onKeyup: onKeyUp,\n        onFocus: onInputFocus,\n        onPressenter: onPressEnter\n      });\n      return _createVNode(\"div\", {\n        \"class\": classNames(prefixCls, className),\n        \"style\": style\n      }, [_createVNode(BaseInput, _objectSpread(_objectSpread({}, textareaProps), {}, {\n        \"ref\": textarea,\n        \"tag\": \"textarea\"\n      }), null), measuring && _createVNode(\"div\", {\n        \"ref\": measure,\n        \"class\": `${prefixCls}-measure`\n      }, [state.value.slice(0, measureLocation), _createVNode(KeywordTrigger, {\n        \"prefixCls\": prefixCls,\n        \"transitionName\": transitionName,\n        \"dropdownClassName\": props.dropdownClassName,\n        \"placement\": placement,\n        \"options\": measuring ? options.value : [],\n        \"visible\": true,\n        \"direction\": direction,\n        \"getPopupContainer\": getPopupContainer\n      }, {\n        default: () => [_createVNode(\"span\", null, [measurePrefix])],\n        notFoundContent: slots.notFoundContent,\n        option: slots.option\n      }), state.value.slice(measureLocation + measurePrefix.length)])]);\n    };\n  }\n});", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { objectType, anyType } from '../../_util/type';\nimport { defineComponent } from 'vue';\nexport const baseOptionsProps = {\n  value: String,\n  disabled: Boolean,\n  payload: objectType()\n};\nexport const optionProps = _extends(_extends({}, baseOptionsProps), {\n  label: anyType([])\n});\nexport const optionOptions = {\n  name: 'Option',\n  props: optionProps,\n  render(_props, _ref) {\n    let {\n      slots\n    } = _ref;\n    var _a;\n    return (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots);\n  }\n};\nexport default defineComponent(_extends({\n  compatConfig: {\n    MODE: 3\n  }\n}, optionOptions));", "// base rc-mentions .6.2\nimport Mentions from './src/Mentions';\nimport Option from './src/Option';\nexport { Option };\nexport default Mentions;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { genActiveStyle, genBasicInputStyle, genDisabledStyle, genPlaceholderStyle, genStatusStyle, initInputToken } from '../../input/style';\nimport { genComponentStyleHook } from '../../theme/internal';\nimport { resetComponent, textEllipsis } from '../../style';\nconst genMentionsStyle = token => {\n  const {\n    componentCls,\n    colorTextDisabled,\n    controlItemBgHover,\n    controlPaddingHorizontal,\n    colorText,\n    motionDurationSlow,\n    lineHeight,\n    controlHeight,\n    inputPaddingHorizontal,\n    inputPaddingVertical,\n    fontSize,\n    colorBgElevated,\n    borderRadiusLG,\n    boxShadowSecondary\n  } = token;\n  const itemPaddingVertical = Math.round((token.controlHeight - token.fontSize * token.lineHeight) / 2);\n  return {\n    [componentCls]: _extends(_extends(_extends(_extends(_extends({}, resetComponent(token)), genBasicInputStyle(token)), {\n      position: 'relative',\n      display: 'inline-block',\n      height: 'auto',\n      padding: 0,\n      overflow: 'hidden',\n      lineHeight,\n      whiteSpace: 'pre-wrap',\n      verticalAlign: 'bottom'\n    }), genStatusStyle(token, componentCls)), {\n      '&-disabled': {\n        '> textarea': _extends({}, genDisabledStyle(token))\n      },\n      '&-focused': _extends({}, genActiveStyle(token)),\n      [`&-affix-wrapper ${componentCls}-suffix`]: {\n        position: 'absolute',\n        top: 0,\n        insetInlineEnd: inputPaddingHorizontal,\n        bottom: 0,\n        zIndex: 1,\n        display: 'inline-flex',\n        alignItems: 'center',\n        margin: 'auto'\n      },\n      // ================= Input Area =================\n      [`> textarea, ${componentCls}-measure`]: {\n        color: colorText,\n        boxSizing: 'border-box',\n        minHeight: controlHeight - 2,\n        margin: 0,\n        padding: `${inputPaddingVertical}px ${inputPaddingHorizontal}px`,\n        overflow: 'inherit',\n        overflowX: 'hidden',\n        overflowY: 'auto',\n        fontWeight: 'inherit',\n        fontSize: 'inherit',\n        fontFamily: 'inherit',\n        fontStyle: 'inherit',\n        fontVariant: 'inherit',\n        fontSizeAdjust: 'inherit',\n        fontStretch: 'inherit',\n        lineHeight: 'inherit',\n        direction: 'inherit',\n        letterSpacing: 'inherit',\n        whiteSpace: 'inherit',\n        textAlign: 'inherit',\n        verticalAlign: 'top',\n        wordWrap: 'break-word',\n        wordBreak: 'inherit',\n        tabSize: 'inherit'\n      },\n      '> textarea': _extends({\n        width: '100%',\n        border: 'none',\n        outline: 'none',\n        resize: 'none',\n        backgroundColor: 'inherit'\n      }, genPlaceholderStyle(token.colorTextPlaceholder)),\n      [`${componentCls}-measure`]: {\n        position: 'absolute',\n        top: 0,\n        insetInlineEnd: 0,\n        bottom: 0,\n        insetInlineStart: 0,\n        zIndex: -1,\n        color: 'transparent',\n        pointerEvents: 'none',\n        '> span': {\n          display: 'inline-block',\n          minHeight: '1em'\n        }\n      },\n      // ================== Dropdown ==================\n      '&-dropdown': _extends(_extends({}, resetComponent(token)), {\n        position: 'absolute',\n        top: -9999,\n        insetInlineStart: -9999,\n        zIndex: token.zIndexPopup,\n        boxSizing: 'border-box',\n        fontSize,\n        fontVariant: 'initial',\n        backgroundColor: colorBgElevated,\n        borderRadius: borderRadiusLG,\n        outline: 'none',\n        boxShadow: boxShadowSecondary,\n        '&-hidden': {\n          display: 'none'\n        },\n        [`${componentCls}-dropdown-menu`]: {\n          maxHeight: token.dropdownHeight,\n          marginBottom: 0,\n          paddingInlineStart: 0,\n          overflow: 'auto',\n          listStyle: 'none',\n          outline: 'none',\n          '&-item': _extends(_extends({}, textEllipsis), {\n            position: 'relative',\n            display: 'block',\n            minWidth: token.controlItemWidth,\n            padding: `${itemPaddingVertical}px ${controlPaddingHorizontal}px`,\n            color: colorText,\n            fontWeight: 'normal',\n            lineHeight,\n            cursor: 'pointer',\n            transition: `background ${motionDurationSlow} ease`,\n            '&:hover': {\n              backgroundColor: controlItemBgHover\n            },\n            '&:first-child': {\n              borderStartStartRadius: borderRadiusLG,\n              borderStartEndRadius: borderRadiusLG,\n              borderEndStartRadius: 0,\n              borderEndEndRadius: 0\n            },\n            '&:last-child': {\n              borderStartStartRadius: 0,\n              borderStartEndRadius: 0,\n              borderEndStartRadius: borderRadiusLG,\n              borderEndEndRadius: borderRadiusLG\n            },\n            '&-disabled': {\n              color: colorTextDisabled,\n              cursor: 'not-allowed',\n              '&:hover': {\n                color: colorTextDisabled,\n                backgroundColor: controlItemBgHover,\n                cursor: 'not-allowed'\n              }\n            },\n            '&-selected': {\n              color: colorText,\n              fontWeight: token.fontWeightStrong,\n              backgroundColor: controlItemBgHover\n            },\n            '&-active': {\n              backgroundColor: controlItemBgHover\n            }\n          })\n        }\n      })\n    })\n  };\n};\n// ============================== Export ==============================\nexport default genComponentStyleHook('Mentions', token => {\n  const mentionsToken = initInputToken(token);\n  return [genMentionsStyle(mentionsToken)];\n}, token => ({\n  dropdownHeight: 250,\n  controlItemWidth: 100,\n  zIndexPopup: token.zIndexPopupBase + 50\n}));", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { resolveDirective as _resolveDirective, createVNode as _createVNode } from \"vue\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { computed, watch, shallowRef, defineComponent } from 'vue';\nimport classNames from '../_util/classNames';\nimport PropTypes from '../_util/vue-types';\nimport VcMentions from '../vc-mentions';\nimport { mentionsProps as baseMentionsProps } from '../vc-mentions/src/mentionsProps';\nimport useConfigInject from '../config-provider/hooks/useConfigInject';\nimport { flattenChildren, getOptionProps } from '../_util/props-util';\nimport { FormItemInputContext, useInjectFormItemContext } from '../form/FormItemContext';\nimport omit from '../_util/omit';\nimport { optionProps, optionOptions } from '../vc-mentions/src/Option';\nimport { getStatusClassNames, getMergedStatus } from '../_util/statusUtils';\nimport useStyle from './style';\nimport { useProvideOverride } from '../menu/src/OverrideContext';\nimport warning from '../_util/warning';\nimport Spin from '../spin';\nimport devWarning from '../vc-util/devWarning';\nfunction loadingFilterOption() {\n  return true;\n}\nconst getMentions = function () {\n  let value = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  let config = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    prefix = '@',\n    split = ' '\n  } = config;\n  const prefixList = Array.isArray(prefix) ? prefix : [prefix];\n  return value.split(split).map(function () {\n    let str = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n    let hitPrefix = null;\n    prefixList.some(prefixStr => {\n      const startStr = str.slice(0, prefixStr.length);\n      if (startStr === prefixStr) {\n        hitPrefix = prefixStr;\n        return true;\n      }\n      return false;\n    });\n    if (hitPrefix !== null) {\n      return {\n        prefix: hitPrefix,\n        value: str.slice(hitPrefix.length)\n      };\n    }\n    return null;\n  }).filter(entity => !!entity && !!entity.value);\n};\nexport const mentionsProps = () => _extends(_extends({}, baseMentionsProps), {\n  loading: {\n    type: Boolean,\n    default: undefined\n  },\n  onFocus: {\n    type: Function\n  },\n  onBlur: {\n    type: Function\n  },\n  onSelect: {\n    type: Function\n  },\n  onChange: {\n    type: Function\n  },\n  onPressenter: {\n    type: Function\n  },\n  'onUpdate:value': {\n    type: Function\n  },\n  notFoundContent: PropTypes.any,\n  defaultValue: String,\n  id: String,\n  status: String\n});\nconst Mentions = defineComponent({\n  compatConfig: {\n    MODE: 3\n  },\n  name: 'AMentions',\n  inheritAttrs: false,\n  props: mentionsProps(),\n  slots: Object,\n  setup(props, _ref) {\n    let {\n      slots,\n      emit,\n      attrs,\n      expose\n    } = _ref;\n    var _a, _b, _c;\n    // =================== Warning =====================\n    if (process.env.NODE_ENV !== 'production') {\n      devWarning(!flattenChildren(((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)) || []).length, 'Mentions', '`Mentions.Option` is deprecated. Please use `options` instead.');\n    }\n    const {\n      prefixCls,\n      renderEmpty,\n      direction\n    } = useConfigInject('mentions', props);\n    const [wrapSSR, hashId] = useStyle(prefixCls);\n    const focused = shallowRef(false);\n    const vcMentions = shallowRef(null);\n    const value = shallowRef((_c = (_b = props.value) !== null && _b !== void 0 ? _b : props.defaultValue) !== null && _c !== void 0 ? _c : '');\n    const formItemContext = useInjectFormItemContext();\n    const formItemInputContext = FormItemInputContext.useInject();\n    const mergedStatus = computed(() => getMergedStatus(formItemInputContext.status, props.status));\n    useProvideOverride({\n      prefixCls: computed(() => `${prefixCls.value}-menu`),\n      mode: computed(() => 'vertical'),\n      selectable: computed(() => false),\n      onClick: () => {},\n      validator: _ref2 => {\n        let {\n          mode\n        } = _ref2;\n        // Warning if use other mode\n        warning(!mode || mode === 'vertical', 'Mentions', `mode=\"${mode}\" is not supported for Mentions's Menu.`);\n      }\n    });\n    watch(() => props.value, val => {\n      value.value = val;\n    });\n    const handleFocus = e => {\n      focused.value = true;\n      emit('focus', e);\n    };\n    const handleBlur = e => {\n      focused.value = false;\n      emit('blur', e);\n      formItemContext.onFieldBlur();\n    };\n    const handleSelect = function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      emit('select', ...args);\n      focused.value = true;\n    };\n    const handleChange = val => {\n      if (props.value === undefined) {\n        value.value = val;\n      }\n      emit('update:value', val);\n      emit('change', val);\n      formItemContext.onFieldChange();\n    };\n    const getNotFoundContent = () => {\n      const notFoundContent = props.notFoundContent;\n      if (notFoundContent !== undefined) {\n        return notFoundContent;\n      }\n      if (slots.notFoundContent) {\n        return slots.notFoundContent();\n      }\n      return renderEmpty('Select');\n    };\n    const getOptions = () => {\n      var _a;\n      return flattenChildren(((_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots)) || []).map(item => {\n        var _a, _b;\n        return _extends(_extends({}, getOptionProps(item)), {\n          label: (_b = (_a = item.children) === null || _a === void 0 ? void 0 : _a.default) === null || _b === void 0 ? void 0 : _b.call(_a)\n        });\n      });\n    };\n    const focus = () => {\n      vcMentions.value.focus();\n    };\n    const blur = () => {\n      vcMentions.value.blur();\n    };\n    expose({\n      focus,\n      blur\n    });\n    const mentionsfilterOption = computed(() => props.loading ? loadingFilterOption : props.filterOption);\n    return () => {\n      const {\n          disabled,\n          getPopupContainer,\n          rows = 1,\n          id = formItemContext.id.value\n        } = props,\n        restProps = __rest(props, [\"disabled\", \"getPopupContainer\", \"rows\", \"id\"]);\n      const {\n        hasFeedback,\n        feedbackIcon\n      } = formItemInputContext;\n      const {\n          class: className\n        } = attrs,\n        otherAttrs = __rest(attrs, [\"class\"]);\n      const otherProps = omit(restProps, ['defaultValue', 'onUpdate:value', 'prefixCls']);\n      const mergedClassName = classNames({\n        [`${prefixCls.value}-disabled`]: disabled,\n        [`${prefixCls.value}-focused`]: focused.value,\n        [`${prefixCls.value}-rtl`]: direction.value === 'rtl'\n      }, getStatusClassNames(prefixCls.value, mergedStatus.value), !hasFeedback && className, hashId.value);\n      const mentionsProps = _extends(_extends(_extends(_extends({\n        prefixCls: prefixCls.value\n      }, otherProps), {\n        disabled,\n        direction: direction.value,\n        filterOption: mentionsfilterOption.value,\n        getPopupContainer,\n        options: props.loading ? [{\n          value: 'ANTDV_SEARCHING',\n          disabled: true,\n          label: _createVNode(Spin, {\n            \"size\": \"small\"\n          }, null)\n        }] : props.options || getOptions(),\n        class: mergedClassName\n      }), otherAttrs), {\n        rows,\n        onChange: handleChange,\n        onSelect: handleSelect,\n        onFocus: handleFocus,\n        onBlur: handleBlur,\n        ref: vcMentions,\n        value: value.value,\n        id\n      });\n      const mentions = _createVNode(VcMentions, _objectSpread(_objectSpread({}, mentionsProps), {}, {\n        \"dropdownClassName\": hashId.value\n      }), {\n        notFoundContent: getNotFoundContent,\n        option: slots.option\n      });\n      if (hasFeedback) {\n        return wrapSSR(_createVNode(\"div\", {\n          \"class\": classNames(`${prefixCls.value}-affix-wrapper`, getStatusClassNames(`${prefixCls.value}-affix-wrapper`, mergedStatus.value, hasFeedback), className, hashId.value)\n        }, [mentions, _createVNode(\"span\", {\n          \"class\": `${prefixCls.value}-suffix`\n        }, [feedbackIcon])]));\n      }\n      return wrapSSR(mentions);\n    };\n  }\n});\n/* istanbul ignore next */\nexport const MentionsOption = defineComponent(_extends(_extends({\n  compatConfig: {\n    MODE: 3\n  }\n}, optionOptions), {\n  name: 'AMentionsOption',\n  props: optionProps\n}));\nexport default _extends(Mentions, {\n  Option: MentionsOption,\n  getMentions,\n  install: app => {\n    app.component(Mentions.name, Mentions);\n    app.component(MentionsOption.name, MentionsOption);\n    return app;\n  }\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,SAAS,uBAAuB,OAAO;AAC5C,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO,MAAM,MAAM,MAAM,GAAG,cAAc;AAC5C;AAIO,SAAS,oBAAoB,MAAM;AACxC,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,QAAM,aAAa,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AAC3D,SAAO,WAAW,OAAO,CAAC,WAAW,cAAc;AACjD,UAAM,YAAY,KAAK,YAAY,SAAS;AAC5C,QAAI,YAAY,UAAU,UAAU;AAClC,aAAO;AAAA,QACL,UAAU;AAAA,QACV,QAAQ;AAAA,MACV;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG;AAAA,IACD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV,CAAC;AACH;AACA,SAAS,MAAM,MAAM;AACnB,UAAQ,QAAQ,IAAI,YAAY;AAClC;AACA,SAAS,WAAW,MAAM,YAAY,OAAO;AAC3C,QAAM,YAAY,KAAK,CAAC;AACxB,MAAI,CAAC,aAAa,cAAc,OAAO;AACrC,WAAO;AAAA,EACT;AAEA,MAAI,WAAW;AACf,QAAM,gBAAgB,WAAW;AACjC,WAAS,IAAI,GAAG,IAAI,eAAe,KAAK,GAAG;AACzC,QAAI,MAAM,SAAS,CAAC,CAAC,MAAM,MAAM,WAAW,CAAC,CAAC,GAAG;AAC/C,iBAAW,SAAS,MAAM,CAAC;AAC3B;AAAA,IACF,WAAW,MAAM,gBAAgB,GAAG;AAClC,iBAAW,SAAS,MAAM,aAAa;AAAA,IACzC;AAAA,EACF;AACA,SAAO;AACT;AAOO,SAAS,mBAAmB,MAAM,eAAe;AACtD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,MAAI,oBAAoB,KAAK,MAAM,GAAG,eAAe;AACrD,MAAI,kBAAkB,kBAAkB,SAAS,MAAM,MAAM,MAAM,OAAO;AACxE,wBAAoB,kBAAkB,MAAM,GAAG,kBAAkB,SAAS,MAAM,MAAM;AAAA,EACxF;AACA,MAAI,mBAAmB;AACrB,wBAAoB,GAAG,iBAAiB,GAAG,KAAK;AAAA,EAClD;AAEA,MAAI,WAAW,WAAW,KAAK,MAAM,cAAc,GAAG,WAAW,MAAM,iBAAiB,kBAAkB,OAAO,MAAM,GAAG,KAAK;AAC/H,MAAI,SAAS,MAAM,GAAG,MAAM,MAAM,MAAM,OAAO;AAC7C,eAAW,SAAS,MAAM,MAAM,MAAM;AAAA,EACxC;AACA,QAAM,qBAAqB,GAAG,iBAAiB,GAAG,MAAM,GAAG,UAAU,GAAG,KAAK;AAC7E,SAAO;AAAA,IACL,MAAM,GAAG,kBAAkB,GAAG,QAAQ;AAAA,IACtC,mBAAmB,mBAAmB;AAAA,EACxC;AACF;AACO,SAAS,kBAAkB,OAAO,UAAU;AACjD,QAAM,kBAAkB,UAAU,QAAQ;AAK1C,QAAM,KAAK;AACX,QAAM,MAAM;AACd;AACO,SAAS,eAAe,MAAM,OAAO;AAC1C,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO,CAAC,SAAS,KAAK,QAAQ,KAAK,MAAM;AAC3C;AACO,SAAS,aAAa,OAAO,MAAM;AACxC,MAAI;AAAA,IACF,QAAQ;AAAA,EACV,IAAI;AACJ,QAAM,YAAY,MAAM,YAAY;AACpC,SAAO,MAAM,YAAY,EAAE,QAAQ,SAAS,MAAM;AACpD;;;ACtGA,IAAM,iBAAiB,OAAO,gBAAgB;AAC9C,IAAM,iBAAiB,WAAS;AAC9B,UAAQ,gBAAgB,KAAK;AAC/B;AACA,IAAM,gBAAgB,MAAM;AAC1B,SAAO,OAAO,cAAc;AAC9B;AACA,IAAM,iBAAiB,OAAO,gBAAgB;AACvC,IAAM,wBAAwB,iBAAe;AAClD,UAAQ,gBAAgB,WAAW;AACrC;AACO,IAAM,uBAAuB,MAAM;AACxC,SAAO,OAAO,gBAAgB,KAAK;AACrC;AACA,IAAM,2BAA2B,OAAO,0BAA0B;AAClE,IAAM,uBAAuB,gBAAc;AACzC,UAAQ,0BAA0B,UAAU;AAC9C;AACA,IAAM,sBAAsB,MAAM;AAChC,SAAO,OAAO,0BAA0B,IAAI;AAC9C;AACA,IAAM,sBAAsB,gBAAgB;AAAA,EAC1C,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,kBAAkB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,UAAM,cAAc,cAAc;AAClC,UAAM,aAAa,SAAS,CAAC,GAAG,WAAW;AAG3C,QAAI,MAAM,SAAS,QAAW;AAC5B,iBAAW,OAAO,MAAM,OAAO,MAAM;AAAA,IACvC;AACA,QAAI,MAAM,qBAAqB,QAAW;AACxC,iBAAW,mBAAmB,MAAM,OAAO,kBAAkB;AAAA,IAC/D;AACA,mBAAe,UAAU;AACzB,WAAO,MAAM;AACX,UAAI;AACJ,cAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,IAChF;AAAA,EACF;AACF,CAAC;AAED,IAAO,yBAAQ;;;AC7DR,IAAM,oBAAoB,OAAO,gBAAgB;AACjD,IAAM,uBAAuB,OAAO,mBAAmB;;;ACAvD,IAAM,eAAe;AAC5B,IAAM,iBAAiB,OAAO,gBAAgB;AAC9C,IAAM,mBAAmB,MAAM;AAC7B,SAAO,OAAO,gBAAgB;AAAA,IAC5B,iBAAiB,SAAS,MAAM,CAAC,CAAC;AAAA,IAClC,YAAY,SAAS,MAAM,CAAC,CAAC;AAAA,IAC7B,YAAY,CAAC;AAAA,EACf,CAAC;AACH;AACA,IAAM,oBAAoB,CAAC,UAAU,KAAK,aAAa;AACrD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB;AACrB,QAAM,YAAY,SAAS,MAAM,CAAC,GAAG,gBAAgB,OAAO,QAAQ,CAAC;AACrE,QAAM,OAAO,SAAS,MAAM,CAAC,GAAG,WAAW,OAAO,GAAG,CAAC;AACtD,UAAQ,gBAAgB;AAAA,IACtB,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,YAAY;AAAA,EACd,CAAC;AACD,SAAO;AACT;AACA,IAAM,UAAU,OAAO,SAAS;AACzB,IAAM,cAAc,gBAAgB;AAAA,EACzC,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AAEJ,YAAQ,SAAS,IAAI;AACrB,WAAO,MAAM;AACX,UAAI;AACJ,cAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,IAChF;AAAA,EACF;AACF,CAAC;AACM,IAAM,aAAa,MAAM;AAC9B,SAAO,OAAO,SAAS,KAAK;AAC9B;AAEA,IAAO,qBAAQ;;;AC3CA,SAAR,kBAAmC,OAAO;AAC/C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,cAAc;AAClB,SAAO,SAAS,MAAM,KAAK,UAAU,WAAW,OAAO,IAAI,QAAQ;AAAA,IACjE,cAAc,GAAG,MAAM,QAAQ,aAAa,KAAK;AAAA,EACnD,IAAI;AAAA,IACF,aAAa,GAAG,MAAM,QAAQ,aAAa,KAAK;AAAA,EAClD,CAAC;AACH;;;ACEA,IAAI,YAAY;AACT,IAAM,gBAAgB,OAAO;AAAA,EAClC,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,OAAO;AAAA,IACL,MAAM,CAAC,QAAQ,OAAO;AAAA,IACtB,SAAS;AAAA,EACX;AAAA,EACA,MAAM,kBAAU;AAAA,EAChB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,SAAS;AAAA,EACT,WAAW;AAAA,EACX,SAAS;AAAA;AAAA,EAET,iBAAiB,WAAW;AAC9B;AACA,IAAO,mBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,cAAc;AAAA,EACrB,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,WAAW,mBAAmB;AACpC,UAAM,YAAY,WAAW;AAC7B,UAAM,MAAM,OAAO,SAAS,MAAM,QAAQ,WAAW,OAAO,SAAS,MAAM,GAAG,IAAI,SAAS,MAAM;AACjG,uBAAW,OAAO,SAAS,MAAM,QAAQ,UAAU,YAAY,oBAAoB,OAAO,GAAG,CAAC,6BAA6B;AAC3H,UAAM,WAAW,aAAa,EAAE,SAAS,OAAO,GAAG;AACnD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,iBAAiB;AACrB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,cAAc;AAClB,UAAM,aAAa,oBAAoB;AACvC,UAAM,WAAW,WAAW,KAAK;AACjC,UAAM,WAAW,SAAS,MAAM;AAC9B,aAAO,CAAC,GAAG,WAAW,OAAO,GAAG;AAAA,IAClC,CAAC;AAED,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,IACV;AACA,qBAAiB,UAAU,QAAQ;AACnC,oBAAgB,MAAM;AACpB,yBAAmB,QAAQ;AAAA,IAC7B,CAAC;AACD,UAAM,YAAY,MAAM;AACtB,eAAS,QAAQ,CAAC,CAAC,WAAW,MAAM,KAAK,SAAO,QAAQ,GAAG;AAAA,IAC7D,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AACD,UAAM,iBAAiB,SAAS,MAAM,SAAS,SAAS,MAAM,QAAQ;AACtE,UAAM,WAAW,SAAS,MAAM,aAAa,MAAM,SAAS,GAAG,CAAC;AAChE,UAAM,aAAa,SAAS,MAAM;AAChC,YAAM,UAAU,GAAG,UAAU,KAAK;AAClC,aAAO;AAAA,QACL,CAAC,GAAG,OAAO,EAAE,GAAG;AAAA,QAChB,CAAC,GAAG,OAAO,SAAS,GAAG,MAAM;AAAA,QAC7B,CAAC,GAAG,OAAO,SAAS,GAAG,SAAS;AAAA,QAChC,CAAC,GAAG,OAAO,WAAW,GAAG,SAAS;AAAA,QAClC,CAAC,GAAG,OAAO,WAAW,GAAG,eAAe;AAAA,MAC1C;AAAA,IACF,CAAC;AACD,UAAM,eAAe,OAAK;AACxB,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,SAAS,SAAS;AAAA,QAClB,cAAc,CAAC,GAAG,gBAAgB,OAAO,QAAQ;AAAA,QACjD,UAAU;AAAA,QACV,MAAM,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,KAAK;AAAA,MAC3C;AAAA,IACF;AAEA,UAAM,kBAAkB,OAAK;AAC3B,UAAI,eAAe,OAAO;AACxB;AAAA,MACF;AACA,YAAM,OAAO,aAAa,CAAC;AAC3B,WAAK,SAAS,CAAC;AACf,kBAAY,IAAI;AAAA,IAClB;AACA,UAAM,eAAe,WAAS;AAC5B,UAAI,CAAC,eAAe,OAAO;AACzB,yBAAiB,SAAS,KAAK;AAC/B,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,UAAM,eAAe,WAAS;AAC5B,UAAI,CAAC,eAAe,OAAO;AACzB,yBAAiB,CAAC,CAAC;AACnB,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,UAAM,oBAAoB,OAAK;AAC7B,WAAK,WAAW,CAAC;AACjB,UAAI,EAAE,UAAU,gBAAQ,OAAO;AAC7B,cAAM,OAAO,aAAa,CAAC;AAE3B,aAAK,SAAS,CAAC;AACf,oBAAY,IAAI;AAAA,MAClB;AAAA,IACF;AAKA,UAAM,kBAAkB,OAAK;AAC3B,uBAAiB,SAAS,KAAK;AAC/B,WAAK,SAAS,CAAC;AAAA,IACjB;AACA,UAAM,qBAAqB,CAAC,MAAM,aAAa;AAC7C,YAAM,WAAW,YAAa,QAAQ;AAAA,QACpC,SAAS,GAAG,UAAU,KAAK;AAAA,MAC7B,GAAG,CAAC,QAAQ,CAAC;AAGb,UAAI,CAAC,QAAQ,eAAe,QAAQ,KAAK,SAAS,SAAS,QAAQ;AACjE,YAAI,YAAY,gBAAgB,SAAS,cAAc,OAAO,aAAa,UAAU;AACnF,iBAAO,YAAa,OAAO;AAAA,YACzB,SAAS,GAAG,UAAU,KAAK;AAAA,UAC7B,GAAG,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC;AAAA,QACzB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,UAAM,iBAAiB,kBAAkB,SAAS,MAAM,SAAS,MAAM,MAAM,CAAC;AAC9E,WAAO,MAAM;AACX,UAAI,IAAI,IAAI,IAAI,IAAI;AACpB,UAAI,UAAW,QAAO;AACtB,YAAM,SAAS,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,MAAM,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AACvI,YAAM,WAAW,iBAAiB,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC;AACzG,YAAM,iBAAiB,SAAS;AAChC,UAAI,eAAe;AACnB,UAAI,OAAO,UAAU,aAAa;AAChC,uBAAe,cAAc,iBAAiB,WAAW;AAAA,MAC3D,WAAW,UAAU,OAAO;AAC1B,uBAAe;AAAA,MACjB;AACA,YAAM,eAAe;AAAA,QACnB,OAAO;AAAA,MACT;AACA,UAAI,CAAC,eAAe,SAAS,CAAC,gBAAgB,OAAO;AACnD,qBAAa,QAAQ;AAGrB,qBAAa,OAAO;AAAA,MACtB;AAEA,YAAM,kBAAkB,CAAC;AACzB,UAAI,MAAM,SAAS,UAAU;AAC3B,wBAAgB,eAAe,IAAI,SAAS;AAAA,MAC9C;AACA,YAAM,QAAQ,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,MAAM,KAAK,MAAM,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,OAAO,KAAK;AAC3I,aAAO,YAAa,iBAAS,eAAc,eAAc,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG;AAAA,QAC9E,aAAa,IAAI,QAAQ,SAAS;AAAA,QAClC,oBAAoB,GAAG,UAAU,KAAK;AAAA,MACxC,CAAC,GAAG;AAAA,QACF,SAAS,MAAM,CAAC,YAAa,oBAAS,MAAM,eAAc,eAAc,eAAc;AAAA,UACpF,aAAa;AAAA,QACf,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,UACb,MAAM,MAAM;AAAA,UACZ,SAAS,SAAS,SAAS,CAAC,GAAG,MAAM,SAAS,CAAC,CAAC,GAAG,eAAe,KAAK;AAAA,UACvE,SAAS,CAAC,WAAW,OAAO;AAAA,YAC1B,CAAC,GAAG,MAAM,KAAK,EAAE,GAAG,CAAC,CAAC,MAAM;AAAA,YAC5B,CAAC,GAAG,UAAU,KAAK,kBAAkB,IAAI,OAAO,iBAAiB,IAAI,oBAAoB;AAAA,UAC3F,CAAC;AAAA,UACD,QAAQ,MAAM,QAAQ;AAAA,UACtB,YAAY,MAAM,WAAW,OAAO;AAAA,UACpC,gBAAgB;AAAA,UAChB,iBAAiB,MAAM;AAAA,QACzB,GAAG,eAAe,GAAG,CAAC,GAAG;AAAA,UACvB,gBAAgB;AAAA,UAChB,gBAAgB;AAAA,UAChB,WAAW;AAAA,UACX,aAAa;AAAA,UACb,WAAW;AAAA,UACX,SAAS,OAAO,UAAU,WAAW,QAAQ;AAAA,QAC/C,CAAC,GAAG;AAAA,UACF,SAAS,MAAM,CAAC,aAAa,OAAO,SAAS,aAAa,KAAK,MAAM,eAAe,IAAI,MAAM;AAAA,YAC5F,OAAO,GAAG,UAAU,KAAK;AAAA,UAC3B,GAAG,KAAK,GAAG,mBAAmB,MAAM,QAAQ,CAAC;AAAA,QAC/C,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACpOD,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,EACT,SAAS;AACX;AACO,IAAM,aAAa;AAAA,EACxB,SAAS;AAAA,IACP,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,EAAE;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,IACV,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,EACf;AAAA,EACA,SAAS;AAAA,IACP,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,IAAI,CAAC;AAAA,EAChB;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,EACf;AACF;AACO,IAAM,gBAAgB;AAAA,EAC3B,SAAS;AAAA,IACP,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,EAAE;AAAA,EAChB;AAAA,EACA,YAAY;AAAA,IACV,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,IAAI,CAAC;AAAA,EAChB;AAAA,EACA,SAAS;AAAA,IACP,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,UAAU;AAAA,IACV,QAAQ,CAAC,GAAG,CAAC;AAAA,EACf;AACF;;;ACtCA,IAAM,oBAAoB;AAAA,EACxB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,kBAAkB;AACpB;AACA,IAAO,uBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,WAAW;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,IAET,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,iBAAiB;AAAA,EACnB;AAAA,EACA,OAAO;AAAA,EACP,OAAO,CAAC,eAAe;AAAA,EACvB,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,WAAW,KAAK;AACrC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,cAAc;AAClB,UAAM,cAAc,qBAAqB;AACzC,UAAM,YAAY,SAAS,MAAM,IAAI,QAAQ,SAAS,SAAS,CAAC,GAAG,aAAa,GAAG,kBAAkB,KAAK,IAAI,SAAS,SAAS,CAAC,GAAG,UAAU,GAAG,kBAAkB,KAAK,CAAC;AACzK,UAAM,iBAAiB,SAAS,MAAM,kBAAkB,MAAM,IAAI,CAAC;AACnE,UAAM,aAAa,WAAW;AAC9B,UAAM,MAAM,MAAM,SAAS,aAAW;AACpC,iBAAI,OAAO,WAAW,KAAK;AAC3B,iBAAW,QAAQ,WAAI,MAAM;AAC3B,qBAAa,QAAQ;AAAA,MACvB,CAAC;AAAA,IACH,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AACD,oBAAgB,MAAM;AACpB,iBAAI,OAAO,WAAW,KAAK;AAAA,IAC7B,CAAC;AACD,UAAM,kBAAkB,aAAW;AACjC,WAAK,iBAAiB,OAAO;AAAA,IAC/B;AACA,UAAM,eAAe,SAAS,MAAM;AAClC,UAAI,IAAI;AACR,YAAM,IAAI,OAAO,WAAW,KAAK,eAAe,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,IAAI,QAAQ,KAAK,eAAe,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG;AACpL,YAAM,MAAM,OAAO,MAAM,aAAa,EAAE,IAAI;AAC5C,aAAO,MAAM,mBAAmB,IAAI,MAAM;AAAA,QACxC,KAAK;AAAA,MACP,CAAC,IAAI;AAAA,IACP,CAAC;AACD,WAAO,MAAM;AACX,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,aAAO,YAAa,oBAAS;AAAA,QAC3B,aAAa;AAAA,QACb,kBAAkB,mBAAW,GAAG,SAAS,UAAU;AAAA,UACjD,CAAC,GAAG,SAAS,MAAM,GAAG,IAAI;AAAA,QAC5B,GAAG,gBAAgB,cAAc,KAAK;AAAA,QACtC,WAAW,SAAS,eAAe,aAAa;AAAA,QAChD,qBAAqB,kBAAkB;AAAA,QACvC,qBAAqB,UAAU;AAAA,QAC/B,kBAAkB,eAAe;AAAA,QACjC,gBAAgB,aAAa;AAAA,QAC7B,cAAc,eAAe;AAAA,UAC3B,QAAQ;AAAA,QACV;AAAA,QACA,UAAU,WAAW,CAAC,IAAI,CAAC,qBAAqB,KAAK;AAAA,QACrD,mBAAmB,iBAAiB;AAAA,QACpC,mBAAmB,kBAAkB;AAAA,QACrC,wBAAwB;AAAA,QACxB,eAAe,eAAe,mBAAmB;AAAA,QACjD,kBAAkB,aAAa;AAAA,MACjC,GAAG;AAAA,QACD,OAAO,MAAM;AAAA,QACb,SAAS,MAAM;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AC1GD,IAAM,sBAAsB,CAAC,QAAQ,SAAS;AAC5C,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc;AAClB,SAAO,YAAa,MAAM,eAAc,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,IACpE,SAAS,mBAAW,UAAU,OAAO,GAAG,UAAU,KAAK,QAAQ,GAAG,UAAU,KAAK,IAAI,KAAK,UAAU,WAAW,WAAW,UAAU,EAAE;AAAA,IACtI,kBAAkB;AAAA,EACpB,CAAC,GAAG,EAAE,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC,CAAC;AAChF;AACA,oBAAoB,cAAc;AAClC,IAAO,sBAAQ;;;ACff,IAAO,4BAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,IACL,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,UAAM,YAAY,SAAS,MAAM,QAAQ;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,cAAc;AAClB,UAAM,cAAc,SAAS,MAAM,KAAK,UAAU,UAAU,KAAK;AACjE,UAAM,UAAU,IAAI,CAAC,YAAY,KAAK;AACtC,UAAM,aAAa,SAAS,MAAM,YAAY,QAAQ,MAAM,OAAO,KAAK;AAGxE,UAAM,MAAM,MAAM;AAChB,UAAI,YAAY,OAAO;AACrB,gBAAQ,QAAQ;AAAA,MAClB;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,IACT,CAAC;AACD,UAAM,eAAe,SAAS,MAAM;AAClC,UAAI,IAAI;AACR,YAAM,IAAI,OAAO,WAAW,KAAK,eAAe,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,KAAK,QAAQ,KAAK,eAAe,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG;AACzL,YAAM,MAAM,OAAO,MAAM,aAAa,EAAE,IAAI;AAC5C,aAAO,SAAS,SAAS,CAAC,GAAG,GAAG,GAAG;AAAA,QACjC,QAAQ,MAAM,QAAQ,UAAU;AAAA,MAClC,CAAC;AAAA,IACH,CAAC;AACD,WAAO,MAAM;AACX,UAAI;AACJ,UAAI,QAAQ,OAAO;AACjB,eAAO;AAAA,MACT;AACA,aAAO,YAAa,qBAAqB;AAAA,QACvC,QAAQ,UAAU;AAAA,MACpB,GAAG;AAAA,QACD,SAAS,MAAM,CAAC,YAAa,YAAY,aAAa,OAAO;AAAA,UAC3D,SAAS,MAAM,CAAC,eAAgB,YAAa,qBAAa;AAAA,YACxD,MAAM,MAAM;AAAA,UACd,GAAG;AAAA,YACD,SAAS,MAAM,EAAE,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC;AAAA,UAC1F,CAAC,GAAG,CAAC,CAAC,OAAQ,WAAW,KAAK,CAAC,CAAC,CAAC;AAAA,QACnC,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AC9CD,IAAIA,aAAY;AACT,IAAM,eAAe,OAAO;AAAA,EACjC,MAAM,kBAAU;AAAA,EAChB,OAAO,kBAAU;AAAA,EACjB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA;AAAA,EAEd,iBAAiB,WAAW;AAC9B;AACA,IAAO,kBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,aAAa;AAAA,EACpB,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,IAAI;AACR,yBAAqB,KAAK;AAC1B,UAAM,YAAY,WAAW;AAC7B,UAAM,WAAW,mBAAmB;AACpC,UAAM,WAAW,OAAO,SAAS,MAAM,QAAQ,WAAW,OAAO,SAAS,MAAM,GAAG,IAAI,SAAS,MAAM;AACtG,uBAAW,OAAO,SAAS,MAAM,QAAQ,UAAU,WAAW,mBAAmB,OAAO,QAAQ,CAAC,6BAA6B;AAC9H,UAAM,MAAM,gBAAQ,QAAQ,IAAI,WAAW,YAAY,EAAEA,UAAS;AAClE,UAAM,YAAY,KAAK,MAAM,cAAc,QAAQ,OAAO,SAAS,KAAK,gBAAQ,QAAQ,IAAI,YAAY,EAAEA,UAAS,OAAO,QAAQ,KAAK;AACvI,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,iBAAiB;AACrB,UAAM,WAAW,SAAS,MAAM,CAAC,GAAG,WAAW,OAAO,GAAG,CAAC;AAC1D,UAAM,oBAAoB,WAAW,CAAC,CAAC;AACvC,UAAM,WAAW;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,KAAC,KAAK,WAAW,uBAAuB,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,KAAK,QAAQ;AAC/F,oBAAgB,MAAM;AACpB,UAAIC;AACJ,UAAI,WAAW,mBAAmB;AAChC,mBAAW,kBAAkB,SAASA,MAAK,WAAW,uBAAuB,QAAQA,QAAO,SAAS,SAASA,IAAG,MAAM,OAAO,OAAK,KAAK,QAAQ;AAAA,MAClJ;AAAA,IACF,CAAC;AACD,uBAAkB,UAAU,KAAK,QAAQ;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,IACF,IAAI,cAAc;AAClB,UAAM,SAAS,aAAa,UAAa,aAAa;AAGtD,UAAM,cAAc,CAAC,cAAc,qBAAqB,KAAK,CAAC;AAC9D,0BAAsB,WAAW;AACjC,QAAI,aAAa,UAAU,CAAC,aAAa,CAAC,UAAU,aAAa;AAC/D,uBAAiB,UAAU,QAAQ;AACnC,sBAAgB,MAAM;AACpB,2BAAmB,QAAQ;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,UAAM,mBAAmB,SAAS,MAAM,GAAG,UAAU,KAAK,UAAU;AACpE,UAAM,iBAAiB,SAAS,MAAM,gBAAgB,SAAS,MAAM,QAAQ;AAC7E,UAAM,aAAa,WAAW;AAC9B,UAAM,WAAW,WAAW;AAK5B,UAAM,aAAa,SAAS,MAAM,SAAS,MAAM,SAAS,GAAG,CAAC;AAC9D,UAAM,OAAO,SAAS,MAAM,CAAC,iBAAiB,SAAS,WAAW,KAAK;AAEvE,UAAM,mBAAmB,SAAS,MAAM;AACtC,aAAO,oBAAoB,MAAM,SAAS,GAAG;AAAA,IAC/C,CAAC;AACD,UAAM,WAAW,WAAW,KAAK;AACjC,UAAM,YAAY,MAAM;AACtB,eAAS,QAAQ,CAAC,CAAC,WAAW,MAAM,KAAK,SAAO,QAAQ,GAAG;AAAA,IAC7D,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AAGD,UAAM,uBAAuB,OAAK;AAEhC,UAAI,eAAe,OAAO;AACxB;AAAA,MACF;AACA,WAAK,cAAc,GAAG,GAAG;AAEzB,UAAI,KAAK,UAAU,UAAU;AAC3B,qBAAa,KAAK,CAAC,WAAW,KAAK;AAAA,MACrC;AAAA,IACF;AACA,UAAM,eAAe,WAAS;AAC5B,UAAI,CAAC,eAAe,OAAO;AACzB,yBAAiB,SAAS,KAAK;AAC/B,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,UAAM,eAAe,WAAS;AAC5B,UAAI,CAAC,eAAe,OAAO;AACzB,yBAAiB,CAAC,CAAC;AACnB,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACF;AAEA,UAAM,iBAAiB,kBAAkB,SAAS,MAAM,SAAS,MAAM,MAAM,CAAC;AAE9E,UAAM,uBAAuB,gBAAc;AACzC,UAAI,KAAK,UAAU,UAAU;AAC3B,qBAAa,KAAK,UAAU;AAAA,MAC9B;AAAA,IACF;AAKA,UAAM,kBAAkB,MAAM;AAC5B,uBAAiB,SAAS,KAAK;AAAA,IACjC;AAEA,UAAM,UAAU,YAAY,GAAG,QAAQ;AACvC,UAAM,iBAAiB,SAAS,MAAM,mBAAW,UAAU,OAAO,GAAG,UAAU,KAAK,IAAI,MAAM,SAAS,MAAM,KAAK,IAAI,MAAM,cAAc,CAAC;AAC3I,UAAM,cAAc,CAAC,OAAO,SAAS;AACnC,UAAI,CAAC,MAAM;AACT,eAAO,gBAAgB,SAAS,CAAC,WAAW,MAAM,UAAU,SAAS,OAAO,UAAU,WAAW,YAAa,OAAO;AAAA,UACnH,SAAS,GAAG,UAAU,KAAK;AAAA,QAC7B,GAAG,CAAC,MAAM,OAAO,CAAC,CAAC,CAAC,IAAI,YAAa,QAAQ;AAAA,UAC3C,SAAS,GAAG,UAAU,KAAK;AAAA,QAC7B,GAAG,CAAC,KAAK,CAAC;AAAA,MACZ;AAGA,YAAM,cAAc,eAAe,KAAK,KAAK,MAAM,SAAS;AAC5D,aAAO,YAAa,UAAW,MAAM,CAAC,aAAa,OAAO,SAAS,aAAa,KAAK,MAAM,eAAe,IAAI,MAAM;AAAA,QAClH,OAAO,GAAG,UAAU,KAAK;AAAA,MAC3B,GAAG,KAAK,GAAG,cAAc,QAAQ,YAAa,QAAQ;AAAA,QACpD,SAAS,GAAG,UAAU,KAAK;AAAA,MAC7B,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AAAA,IACd;AAEA,UAAM,iBAAiB,SAAS,MAAM;AACpC,aAAO,KAAK,UAAU,YAAY,SAAS,MAAM,SAAS,IAAI,aAAa,KAAK;AAAA,IAClF,CAAC;AACD,UAAM,aAAa,SAAS,MAAM,KAAK,UAAU,eAAe,aAAa,KAAK,KAAK;AACvF,UAAM,wBAAwB,SAAS,MAAM,eAAe,UAAU,eAAe,aAAa,eAAe,KAAK;AACtH,UAAM,gBAAgB,MAAM;AAC1B,UAAIA,KAAIC;AACR,YAAM,wBAAwB,iBAAiB;AAC/C,YAAM,QAAQD,MAAK,MAAM,UAAU,QAAQA,QAAO,SAASA,OAAMC,MAAK,MAAM,UAAU,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,OAAO,KAAK;AAC3I,YAAM,aAAa,MAAM,cAAc,MAAM,cAAc,eAAe;AAC1E,YAAM,QAAQ,YAAY,aAAa,OAAO,OAAO,OAAO,GAAG,IAAI;AACnE,aAAO,YAAa,OAAO;AAAA,QACzB,SAAS,eAAe;AAAA,QACxB,SAAS,GAAG,qBAAqB;AAAA,QACjC,YAAY,eAAe,QAAQ,OAAO;AAAA,QAC1C,OAAO;AAAA,QACP,SAAS,OAAO,UAAU,WAAW,QAAQ;AAAA,QAC7C,gBAAgB;AAAA,QAChB,iBAAiB,KAAK;AAAA,QACtB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB,eAAe;AAAA,QAChC,WAAW;AAAA,QACX,WAAW;AAAA,MACb,GAAG,CAAC,OAAO,KAAK,UAAU,gBAAgB,aAAa,WAAW,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG;AAAA,QAC9F,QAAQ,KAAK;AAAA,MACf,CAAC,CAAC,IAAI,YAAa,KAAK;AAAA,QACtB,SAAS,GAAG,qBAAqB;AAAA,MACnC,GAAG,IAAI,CAAC,CAAC;AAAA,IACX;AACA,WAAO,MAAM;AACX,UAAID;AACJ,UAAI,WAAW;AACb,YAAI,CAAC,QAAQ;AACX,iBAAO;AAAA,QACT;AACA,gBAAQA,MAAK,MAAM,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,KAAK;AAAA,MAChF;AACA,YAAM,wBAAwB,iBAAiB;AAC/C,UAAI,YAAY,MAAM;AACtB,UAAI,CAAC,iBAAiB,SAAS,KAAK,UAAU,UAAU;AACtD,cAAM,cAAc,KAAK,UAAU,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AACjE,oBAAY,MAAM,YAAa,sBAAc;AAAA,UAC3C,QAAQ,eAAe;AAAA,UACvB,aAAa;AAAA,UACb,WAAW,CAAC,MAAM,sBAAsB,KAAK;AAAA,UAC7C,kBAAkB,eAAe;AAAA,UACjC,eAAe,MAAM,eAAe;AAAA,UACpC,YAAY,eAAe;AAAA,UAC3B,mBAAmB;AAAA,QACrB,GAAG;AAAA,UACD,SAAS,MAAM,CAAC,cAAc,CAAC;AAAA,UAC/B,OAAO,MAAM,YAAa,qBAAqB;AAAA,YAC7C,QAAQ,sBAAsB;AAAA,UAChC,GAAG;AAAA,YACD,SAAS,MAAM,CAAC,YAAa,qBAAa;AAAA,cACxC,MAAM;AAAA,cACN,OAAO;AAAA,YACT,GAAG;AAAA,cACD,SAAS,MAAM;AAAA,YACjB,CAAC,CAAC;AAAA,UACJ,CAAC;AAAA,QACH,CAAC;AAAA,MACH,OAAO;AAGL,oBAAY,MAAM,YAAa,sBAAc,MAAM;AAAA,UACjD,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AACA,aAAO,YAAa,qBAAqB;AAAA,QACvC,QAAQ,WAAW;AAAA,MACrB,GAAG;AAAA,QACD,SAAS,MAAM,CAAC,YAAa,oBAAS,MAAM,eAAc,eAAc;AAAA,UACtE,aAAa;AAAA,QACf,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,UACb,QAAQ;AAAA,UACR,SAAS,mBAAW,uBAAuB,GAAG,qBAAqB,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO;AAAA,YAChG,CAAC,GAAG,qBAAqB,OAAO,GAAG,KAAK;AAAA,YACxC,CAAC,GAAG,qBAAqB,SAAS,GAAG,SAAS;AAAA,YAC9C,CAAC,GAAG,qBAAqB,WAAW,GAAG,iBAAiB;AAAA,YACxD,CAAC,GAAG,qBAAqB,WAAW,GAAG,eAAe;AAAA,UACxD,CAAC;AAAA,UACD,gBAAgB;AAAA,UAChB,gBAAgB;AAAA,UAChB,mBAAmB;AAAA,QACrB,CAAC,GAAG;AAAA,UACF,SAAS,MAAM;AACb,mBAAO,YAAa,UAAW,MAAM,CAAC,UAAU,GAAG,CAAC,iBAAiB,SAAS,YAAa,2BAAmB;AAAA,cAC5G,MAAM;AAAA,cACN,QAAQ,KAAK;AAAA,cACb,WAAW,SAAS;AAAA,YACtB,GAAG;AAAA,cACD,SAAS,MAAM;AAAA,YACjB,CAAC,CAAC,CAAC;AAAA,UACL;AAAA,QACF,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;ACxRM,IAAM,qBAAqB,OAAO;AAAA,EACvC,OAAO,kBAAU;AAAA;AAAA,EAEjB,iBAAiB,WAAW;AAC9B;AACA,IAAO,oBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,mBAAmB;AAAA,EAC1B,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc;AAClB,UAAM,iBAAiB,SAAS,MAAM,GAAG,UAAU,KAAK,aAAa;AACrE,UAAM,YAAY,WAAW;AAC7B,WAAO,MAAM;AACX,UAAI,IAAI;AACR,UAAI,UAAW,SAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAC7F,aAAO,YAAa,MAAM,eAAc,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QACpE,WAAW,OAAK,EAAE,gBAAgB;AAAA,QAClC,SAAS,eAAe;AAAA,MAC1B,CAAC,GAAG,CAAC,YAAa,OAAO;AAAA,QACvB,SAAS,OAAO,MAAM,UAAU,WAAW,MAAM,QAAQ;AAAA,QACzD,SAAS,GAAG,eAAe,KAAK;AAAA,MAClC,GAAG,CAAC,aAAa,OAAO,OAAO,OAAO,CAAC,CAAC,GAAG,YAAa,MAAM;AAAA,QAC5D,SAAS,GAAG,eAAe,KAAK;AAAA,MAClC,GAAG,EAAE,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC;AAAA,IACjF;AAAA,EACF;AACF,CAAC;;;AC1CM,IAAM,mBAAmB,OAAO;AAAA,EACrC,WAAW;AAAA,EACX,QAAQ;AACV;AACA,IAAO,kBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,OAAO,iBAAiB;AAAA,EACxB,MAAM,OAAO;AACX,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,cAAc;AAClB,UAAM,MAAM,SAAS,MAAM;AACzB,aAAO;AAAA,QACL,CAAC,GAAG,UAAU,KAAK,eAAe,GAAG;AAAA,QACrC,CAAC,GAAG,UAAU,KAAK,sBAAsB,GAAG,CAAC,CAAC,MAAM;AAAA,MACtD;AAAA,IACF,CAAC;AACD,WAAO,MAAM;AACX,aAAO,YAAa,MAAM;AAAA,QACxB,SAAS,IAAI;AAAA,MACf,GAAG,IAAI;AAAA,IACT;AAAA,EACF;AACF,CAAC;;;AC3BD,IAAI,SAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAMA,SAAS,oBAAoB,MAAM,OAAO,gBAAgB;AACxD,UAAQ,QAAQ,CAAC,GAAG,IAAI,CAAC,KAAK,UAAU;AACtC,QAAI,OAAO,OAAO,QAAQ,UAAU;AAClC,YAAM,KAAK,KACT;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,IACJ,YAAY,OAAO,IAAI,CAAC,SAAS,YAAY,OAAO,MAAM,CAAC;AAC7D,YAAM,YAAY,QAAQ,QAAQ,QAAQ,SAAS,MAAM,OAAO,KAAK;AAErE,YAAM,aAAa,iBAAiB,eAAe,WAAW,MAAM,IAAI,CAAC;AACzE,YAAM,oBAAoB,CAAC;AAE3B,YAAM,WAAW;AAAA,QACf,UAAU;AAAA,QACV,KAAK;AAAA,QACL,iBAAiB,IAAI,UAAU;AAAA,QAC/B,YAAY,IAAI,UAAU;AAAA,QAC1B,mBAAmB,IAAI,iBAAiB;AAAA,QACxC,QAAQ;AAAA,MACV;AAEA,UAAI,YAAY,SAAS,SAAS;AAChC,YAAI,SAAS,SAAS;AACpB,gBAAME,iBAAgB,oBAAoB,UAAU,OAAO,cAAc;AAEzE,iBAAO,YAAa,mBAAW,eAAc,eAAc;AAAA,YACzD,OAAO;AAAA,UACT,GAAG,SAAS,GAAG,CAAC,GAAG;AAAA,YACjB,SAAS;AAAA,YACT,mBAAmB;AAAA,UACrB,CAAC,GAAG;AAAA,YACF,SAAS,MAAM,CAACA,cAAa;AAAA,UAC/B,CAAC;AAAA,QACH;AACA,cAAM,IAAI,WAAW,QAAQ;AAC7B,YAAI,gBAAgB;AAClB,yBAAe,kBAAkB,KAAK,SAAS;AAAA,QACjD;AAEA,cAAM,gBAAgB,oBAAoB,UAAU,OAAO;AAAA,UACzD;AAAA,UACA,YAAY,CAAC,EAAE,OAAO,YAAY,SAAS;AAAA,QAC7C,CAAC;AACD,eAAO,YAAa,iBAAS,eAAc,eAAc;AAAA,UACvD,OAAO;AAAA,QACT,GAAG,SAAS,GAAG,CAAC,GAAG;AAAA,UACjB,SAAS;AAAA,UACT,mBAAmB;AAAA,QACrB,CAAC,GAAG;AAAA,UACF,SAAS,MAAM,CAAC,aAAa;AAAA,QAC/B,CAAC;AAAA,MACH;AAEA,UAAI,SAAS,WAAW;AACtB,eAAO,YAAa,iBAAa,eAAc;AAAA,UAC7C,OAAO;AAAA,QACT,GAAG,SAAS,GAAG,IAAI;AAAA,MACrB;AACA,eAAS,SAAS;AAClB,YAAM,IAAI,WAAW,QAAQ;AAC7B,aAAO,YAAa,kBAAU,eAAc,eAAc;AAAA,QACxD,OAAO;AAAA,MACT,GAAG,SAAS,GAAG,CAAC,GAAG;AAAA,QACjB,mBAAmB;AAAA,MACrB,CAAC,GAAG;AAAA,QACF,SAAS,MAAM,CAAC,KAAK;AAAA,MACvB,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC,EAAE,OAAO,SAAO,GAAG;AACtB;AAMe,SAAR,SAA0B,OAAO;AACtC,QAAM,aAAa,WAAW,CAAC,CAAC;AAChC,QAAM,WAAW,WAAW,KAAK;AACjC,QAAM,QAAQ,WAAW,oBAAI,IAAI,CAAC;AAClC,QAAM,MAAM,MAAM,OAAO,MAAM;AAC7B,UAAM,WAAW,oBAAI,IAAI;AACzB,aAAS,QAAQ;AACjB,QAAI,MAAM,OAAO;AACf,eAAS,QAAQ;AACjB,iBAAW,QAAQ,oBAAoB,MAAM,OAAO,QAAQ;AAAA,IAC9D,OAAO;AACL,iBAAW,QAAQ;AAAA,IACrB;AACA,UAAM,QAAQ;AAAA,EAChB,GAAG;AAAA,IACD,WAAW;AAAA,IACX,MAAM;AAAA,EACR,CAAC;AACD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACtHA,IAAM,qBAAqB,WAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,CAAC,GAAG,YAAY,aAAa,GAAG;AAAA,MAC9B,YAAY,GAAG,oBAAoB;AAAA,MACnC,QAAQ;AAAA,MACR,cAAc,GAAG,SAAS,MAAM,QAAQ,IAAI,UAAU;AAAA,MACtD,WAAW;AAAA,MACX,YAAY;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA;AAAA,MAEA,CAAC,GAAG,YAAY,UAAU,YAAY,UAAU,GAAG;AAAA,QACjD,UAAU;AAAA,QACV,SAAS;AAAA,QACT,eAAe;AAAA,QACf,eAAe;AAAA,MACjB;AAAA,MACA,CAAC,KAAK,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ,YAAY,YAAY,YAAY,sBAAsB,GAAG;AAAA,QACjE,iBAAiB;AAAA,MACnB;AAAA,MACA,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG;AAAA,QACvD,YAAY,CAAC,gBAAgB,kBAAkB,IAAI,cAAc,kBAAkB,EAAE,EAAE,KAAK,GAAG;AAAA,MACjG;AAAA;AAAA,MAEA,CAAC,GAAG,YAAY,gBAAgB,GAAG;AAAA,QACjC,SAAS;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,qBAAQ;;;AC5Cf,IAAM,cAAc,UAAQ;AAC1B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,CAAC,GAAG,YAAY,MAAM,GAAG;AAAA,MACvB,WAAW;AAAA,IACb;AAAA,IACA,CAAC,GAAG,YAAY,cAAc,GAAG;AAAA,MAC/B,iBAAiB;AAAA,IACnB;AAAA;AAAA,IAEA,CAAC,GAAG,YAAY,OAAO,YAAY;AAAA,MACjC,YAAY,gBAAgB,YAAY,WAAW,GAAG;AAAA,MACtD,CAAC,GAAG,YAAY,gBAAgB,GAAG;AAAA,QACjC,aAAa;AAAA,UACX,WAAW,8BAA8B,eAAe;AAAA,QAC1D;AAAA,QACA,YAAY;AAAA,UACV,WAAW,4BAA4B,eAAe;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,cAAQ;;;ACxBf,IAAM,qBAAqB,WAAS,SAAS,CAAC,GAAG,gBAAgB,KAAK,CAAC;AACvE,IAAM,gBAAgB,CAAC,OAAO,gBAAgB;AAC5C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,CAAC,GAAG,YAAY,IAAI,WAAW,EAAE,GAAG;AAAA,MAClC,OAAO;AAAA,MACP,YAAY;AAAA,MACZ,CAAC,IAAI,YAAY,qBAAqB,GAAG,SAAS,CAAC,GAAG,mBAAmB,KAAK,CAAC;AAAA;AAAA,MAE/E,CAAC,GAAG,YAAY,mBAAmB,GAAG;AAAA,QACpC,OAAO;AAAA,MACT;AAAA,MACA,CAAC,GAAG,YAAY,mBAAmB,GAAG;AAAA,QACpC,CAAC,KAAK,YAAY,gBAAgB,GAAG;AAAA,UACnC,OAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,GAAG,YAAY,mBAAmB,YAAY,mBAAmB,GAAG;AAAA,QACnE,OAAO,GAAG,qBAAqB;AAAA,MACjC;AAAA;AAAA,MAEA,CAAC,GAAG,YAAY,gBAAgB,YAAY,sBAAsB,GAAG;AAAA,QACnE,CAAC,SAAS,YAAY,uBAAuB,YAAY,oBAAoB,GAAG;AAAA,UAC9E,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,CAAC,SAAS,YAAY,cAAc,GAAG;AAAA,QACrC,CAAC,GAAG,YAAY,aAAa,YAAY,iBAAiB,GAAG;AAAA,UAC3D,WAAW;AAAA,YACT,iBAAiB;AAAA,UACnB;AAAA,UACA,YAAY;AAAA,YACV,iBAAiB;AAAA,UACnB;AAAA,QACF;AAAA,QACA,CAAC,GAAG,YAAY,gBAAgB,GAAG;AAAA,UACjC,WAAW;AAAA,YACT,iBAAiB;AAAA,UACnB;AAAA,UACA,YAAY;AAAA,YACV,iBAAiB;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,GAAG,YAAY,cAAc,GAAG;AAAA,QAC/B,OAAO;AAAA,QACP,CAAC,IAAI,YAAY,aAAa,GAAG;AAAA,UAC/B,CAAC,SAAS,YAAY,uBAAuB,YAAY,oBAAoB,GAAG;AAAA,YAC9E,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,CAAC,IAAI,YAAY,cAAc,GAAG;AAAA,UAChC,YAAY;AAAA,QACd;AAAA,MACF;AAAA,MACA,CAAC,GAAG,YAAY,SAAS,GAAG;AAAA,QAC1B,cAAc;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,CAAC,GAAG,YAAY,gBAAgB,GAAG;AAAA,QACjC,OAAO;AAAA;AAAA,QAEP,CAAC,IAAI,YAAY,cAAc,GAAG;AAAA,UAChC,OAAO;AAAA,QACT;AAAA,QACA,CAAC,YAAY,GAAG;AAAA,UACd,OAAO;AAAA,QACT;AAAA,MACF;AAAA,MACA,CAAC,KAAK,YAAY,gBAAgB,GAAG;AAAA,QACnC,iBAAiB;AAAA;AAAA,QAEjB,CAAC,IAAI,YAAY,cAAc,GAAG;AAAA,UAChC,iBAAiB;AAAA,QACnB;AAAA,MACF;AAAA,MACA,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG;AAAA,QACvD,CAAC,SAAS,YAAY,+BAA+B,GAAG,SAAS,CAAC,GAAG,mBAAmB,KAAK,CAAC;AAAA,MAChG;AAAA,MACA,CAAC,IAAI,YAAY,cAAc,YAAY,EAAE,GAAG;AAAA,QAC9C,iBAAiB;AAAA,MACnB;AAAA,MACA,CAAC,IAAI,YAAY,YAAY,YAAY,EAAE,GAAG;AAAA,QAC5C,iBAAiB;AAAA,MACnB;AAAA;AAAA,MAEA,CAAC,IAAI,YAAY,aAAa,GAAG,SAAS,SAAS,CAAC,GAAG,gBAAgB,SAAS;AAAA,QAC9E,cAAc;AAAA,MAChB,IAAI,CAAC,CAAC,GAAG;AAAA,QACP,CAAC,KAAK,YAAY,YAAY,YAAY,UAAU,GAAG;AAAA,UACrD,KAAK;AAAA,UACL,WAAW,CAAC;AAAA,UACZ,cAAc;AAAA,UACd,cAAc;AAAA,UACd,YAAY;AAAA,YACV,UAAU;AAAA,YACV,aAAa;AAAA,YACb,QAAQ;AAAA,YACR,cAAc,GAAG,oBAAoB;AAAA,YACrC,YAAY,gBAAgB,kBAAkB,IAAI,eAAe;AAAA,YACjE,SAAS;AAAA,UACX;AAAA,UACA,CAAC,2BAA2B,GAAG;AAAA,YAC7B,YAAY;AAAA,cACV,mBAAmB;AAAA,cACnB,mBAAmB;AAAA,YACrB;AAAA,UACF;AAAA,UACA,CAAC,YAAY,GAAG;AAAA,YACd,OAAO;AAAA,YACP,iBAAiB;AAAA,YACjB,YAAY;AAAA,cACV,mBAAmB;AAAA,cACnB,mBAAmB;AAAA,YACrB;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA;AAAA;AAAA,MAGD,CAAC,IAAI,YAAY,OAAO,GAAG;AAAA,QACzB,CAAC,IAAI,YAAY,aAAa,YAAY,WAAW,GAAG;AAAA,UACtD,iBAAiB,GAAG,wBAAwB,MAAM,QAAQ,IAAI,UAAU;AAAA,QAC1E;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,IAAI,YAAY,SAAS,GAAG;AAAA;AAAA,QAE3B,CAAC,GAAG,YAAY,OAAO,YAAY,SAAS,GAAG;AAAA,UAC7C,YAAY;AAAA,QACd;AAAA;AAAA,QAEA,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG,4BAA4B,sBAAsB;AAAA,UACzG,OAAO,eAAe,wBAAwB;AAAA,QAChD,IAAI,CAAC;AAAA,QACL,CAAC,GAAG,YAAY,OAAO,GAAG;AAAA,UACxB,UAAU;AAAA,UACV,YAAY;AAAA,YACV,UAAU;AAAA,YACV,YAAY;AAAA,YACZ,gBAAgB;AAAA,YAChB,iBAAiB,GAAG,mBAAmB,YAAY,qBAAqB;AAAA,YACxE,WAAW;AAAA,YACX,SAAS;AAAA,YACT,YAAY,CAAC,aAAa,iBAAiB,IAAI,aAAa,IAAI,WAAW,iBAAiB,IAAI,aAAa,EAAE,EAAE,KAAK,GAAG;AAAA,YACzH,SAAS;AAAA,UACX;AAAA;AAAA,UAEA,CAAC,IAAI,YAAY,cAAc,GAAG;AAAA,YAChC,YAAY;AAAA,cACV,sBAAsB;AAAA,YACxB;AAAA,UACF;AAAA,QACF;AAAA,QACA,CAAC,GAAG,YAAY,cAAc,YAAY,gBAAgB,GAAG;AAAA,UAC3D,YAAY;AAAA,YACV,WAAW;AAAA,YACX,SAAS;AAAA,YACT,YAAY,CAAC,aAAa,iBAAiB,IAAI,eAAe,IAAI,WAAW,iBAAiB,IAAI,eAAe,EAAE,EAAE,KAAK,GAAG;AAAA,UAC/H;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,gBAAQ;;;ACxMf,IAAM,yBAAyB,WAAS;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,mBAAmB,UAAU,gBAAgB;AACnD,SAAO;AAAA,IACL,CAAC,GAAG,YAAY,OAAO,GAAG;AAAA,MACxB,UAAU;AAAA,IACZ;AAAA,IACA,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG;AAAA,MACvD,QAAQ;AAAA,MACR,YAAY,GAAG,cAAc;AAAA,MAC7B,eAAe;AAAA,MACf,UAAU;AAAA,MACV,cAAc;AAAA,MACd,cAAc;AAAA,MACd,aAAa;AAAA,MACb,OAAO,eAAe,mBAAmB,CAAC;AAAA,IAC5C;AAAA;AAAA,IAEA,CAAC,GAAG,YAAY,UAAU,GAAG;AAAA,MAC3B,eAAe;AAAA,IACjB;AAAA,IACA,CAAC,KAAK,YAAY;AAAA,gBACN,YAAY,cAAc,YAAY,gBAAgB,GAAG;AAAA,MACnE,QAAQ;AAAA,MACR,YAAY,GAAG,cAAc;AAAA,IAC/B;AAAA,IACA,CAAC,GAAG,YAAY,oBAAoB,YAAY;AAAA,cACtC,YAAY,gBAAgB,GAAG;AAAA,MACvC,kBAAkB;AAAA,IACpB;AAAA,EACF;AACF;AACA,IAAM,mBAAmB,WAAS;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB;AAAA,IACtB,QAAQ;AAAA,IACR,YAAY,GAAG,cAAc;AAAA,IAC7B,mBAAmB;AAAA,IACnB,eAAe;AAAA,EACjB;AACA,SAAO;AAAA,IAAC;AAAA,MACN,CAAC,YAAY,GAAG;AAAA,QACd,CAAC,sBAAsB,GAAG,SAAS;AAAA,UACjC,CAAC,IAAI,YAAY,OAAO,GAAG;AAAA,YACzB,WAAW;AAAA,UACb;AAAA,QACF,GAAG,uBAAuB,KAAK,CAAC;AAAA,MAClC;AAAA,MACA,CAAC,GAAG,YAAY,gBAAgB,GAAG;AAAA,QACjC,CAAC,GAAG,YAAY,WAAW,GAAG,SAAS,SAAS,CAAC,GAAG,uBAAuB,KAAK,CAAC,GAAG;AAAA,UAClF,WAAW;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA,IAEA;AAAA,MACE,CAAC,GAAG,YAAY,kBAAkB,YAAY,YAAY,YAAY,MAAM,GAAG;AAAA,QAC7E,UAAU;AAAA,QACV,WAAW,gBAAgB,kBAAkB,GAAG;AAAA,QAChD,SAAS;AAAA,QACT,UAAU;AAAA,QACV,iBAAiB;AAAA;AAAA;AAAA,QAGjB,6BAA6B;AAAA,UAC3B,WAAW;AAAA,UACX,WAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA;AAAA,MACE,CAAC,GAAG,YAAY,SAAS,GAAG;AAAA,QAC1B,OAAO;AAAA;AAAA,QAEP,CAAC,IAAI,YAAY,OAAO,GAAG;AAAA,UACzB,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG;AAAA,YACvD,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,YAAY,CAAC,gBAAgB,kBAAkB,IAAI,cAAc,kBAAkB,IAAI,WAAW,iBAAiB,IAAI,aAAa,EAAE,EAAE,KAAK,GAAG;AAAA,YAChJ,CAAC,KAAK,YAAY,gBAAgB,GAAG;AAAA,cACnC,MAAM;AAAA,cACN,UAAU;AAAA,cACV,UAAU;AAAA,cACV,cAAc;AAAA,YAChB;AAAA,YACA,OAAO;AAAA,cACL,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAEA,CAAC,GAAG,YAAY,OAAO,YAAY,SAAS,GAAG;AAAA,UAC7C,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,cAAc;AAAA,UACd,WAAW;AAAA,UACX,CAAC,OAAO,YAAY,cAAc,YAAY,gBAAgB,GAAG;AAAA,UACjE,CAAC,KAAK,YAAY,mBAAmB,GAAG;AAAA,YACtC,oBAAoB;AAAA,UACtB;AAAA,QACF;AAAA;AAAA,QAEA,CAAC,GAAG,YAAY,OAAO,GAAG;AAAA,MAC5B;AAAA,IACF;AAAA;AAAA,IAEA;AAAA,MACE,CAAC,GAAG,YAAY,mBAAmB,GAAG;AAAA,QACpC,OAAO,iBAAiB;AAAA,QACxB,CAAC,IAAI,YAAY,OAAO,GAAG;AAAA,UACzB,CAAC,GAAG,YAAY,UAAU,YAAY,YAAY,YAAY,gBAAgB,GAAG;AAAA,YAC/E,CAAC,KAAK,YAAY,0BAA0B,GAAG;AAAA,cAC7C,UAAU;AAAA,cACV,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,QACA,CAAC,KAAK,YAAY;AAAA,cACV,YAAY,iBAAiB,YAAY,sBAAsB,YAAY;AAAA,cAC3E,YAAY,iBAAiB,YAAY,sBAAsB,YAAY,cAAc,YAAY;AAAA,cACrG,YAAY,cAAc,YAAY,gBAAgB,GAAG;AAAA,UAC/D,kBAAkB;AAAA,UAClB,eAAe,cAAc,UAAU;AAAA,UACvC,cAAc;AAAA,UACd,CAAC;AAAA,cACK,YAAY;AAAA,cACZ,YAAY;AAAA,WACf,GAAG;AAAA,YACJ,SAAS;AAAA,UACX;AAAA,UACA,CAAC,GAAG,YAAY,eAAe,OAAO,EAAE,GAAG;AAAA,YACzC,QAAQ;AAAA,YACR,UAAU;AAAA,YACV,YAAY,GAAG,cAAc;AAAA,YAC7B,UAAU;AAAA,cACR,SAAS;AAAA,cACT,SAAS;AAAA,YACX;AAAA,UACF;AAAA,QACF;AAAA,QACA,CAAC,GAAG,YAAY,eAAe,OAAO,EAAE,GAAG;AAAA,UACzC,SAAS;AAAA,QACX;AAAA,QACA,aAAa;AAAA,UACX,eAAe;AAAA,UACf,CAAC,GAAG,YAAY,eAAe,OAAO,EAAE,GAAG;AAAA,YACzC,SAAS;AAAA,UACX;AAAA,UACA,cAAc;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,CAAC,GAAG,YAAY,mBAAmB,GAAG,SAAS,SAAS,CAAC,GAAG,YAAY,GAAG;AAAA,UACzE,eAAe;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EAAC;AACH;AACA,IAAO,mBAAQ;;;AC9Kf,IAAM,mBAAmB,WAAS;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA;AAAA,IAEL,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG;AAAA,MACvD,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,YAAY,CAAC,gBAAgB,kBAAkB,IAAI,cAAc,kBAAkB,IAAI,WAAW,kBAAkB,IAAI,eAAe,EAAE,EAAE,KAAK,GAAG;AAAA,MACnJ,CAAC,GAAG,YAAY,eAAe,OAAO,EAAE,GAAG;AAAA,QACzC,UAAU;AAAA,QACV;AAAA,QACA,YAAY,CAAC,aAAa,iBAAiB,IAAI,aAAa,IAAI,UAAU,kBAAkB,IAAI,eAAe,IAAI,SAAS,kBAAkB,EAAE,EAAE,KAAK,GAAG;AAAA,QAC1J,UAAU;AAAA,UACR,mBAAmB,kBAAkB;AAAA,UACrC,SAAS;AAAA,UACT,YAAY,CAAC,WAAW,kBAAkB,IAAI,eAAe,IAAI,UAAU,kBAAkB,IAAI,SAAS,kBAAkB,EAAE,EAAE,KAAK,GAAG;AAAA,QAC1I;AAAA,MACF;AAAA,MACA,CAAC,GAAG,YAAY,YAAY,GAAG,SAAS,CAAC,GAAG,UAAU,CAAC;AAAA,MACvD,CAAC,IAAI,YAAY,kBAAkB,GAAG;AAAA,QACpC,CAAC,KAAK,OAAO,OAAO,YAAY,YAAY,GAAG;AAAA,UAC7C,iBAAiB;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA;AAAA,IAEA,CAAC,GAAG,YAAY,mBAAmB,YAAY,mBAAmB,GAAG;AAAA,MACnE,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,YAAY;AAAA,QACV,aAAa;AAAA,MACf;AAAA,MACA,GAAG;AAAA,QACD,OAAO;AAAA,MACT;AAAA,MACA,CAAC,KAAK,YAAY,gBAAgB,GAAG;AAAA,QACnC,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,uBAAuB,WAAS;AACpC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL,CAAC,GAAG,YAAY,UAAU,GAAG;AAAA,MAC3B,CAAC,wBAAwB,GAAG;AAAA,QAC1B,UAAU;AAAA,QACV,KAAK;AAAA,QACL,gBAAgB,MAAM;AAAA,QACtB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,QACX,YAAY,aAAa,kBAAkB,IAAI,eAAe,aAAa,kBAAkB;AAAA,MAC/F;AAAA,MACA,WAAW;AAAA;AAAA,QAET,uBAAuB;AAAA,UACrB,UAAU;AAAA,UACV,OAAO,gBAAgB;AAAA,UACvB,QAAQ,gBAAgB;AAAA,UACxB,iBAAiB;AAAA,UACjB;AAAA,UACA,YAAY,CAAC,cAAc,kBAAkB,IAAI,eAAe,IAAI,aAAa,kBAAkB,IAAI,eAAe,IAAI,OAAO,kBAAkB,IAAI,eAAe,IAAI,SAAS,kBAAkB,IAAI,eAAe,EAAE,EAAE,KAAK,GAAG;AAAA,UACpO,SAAS;AAAA,QACX;AAAA,QACA,aAAa;AAAA,UACX,WAAW,6BAA6B,eAAe;AAAA,QACzD;AAAA,QACA,YAAY;AAAA,UACV,WAAW,6BAA6B,eAAe;AAAA,QACzD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,eAAe,WAAS;AAC5B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA;AAAA,IAEP;AAAA,MACE,IAAI;AAAA,QACF,CAAC,GAAG,YAAY,EAAE,GAAG,SAAS,SAAS,CAAC,GAAG,SAAS,CAAC,GAAG;AAAA;AAAA,UAEtD,CAAC,UAAU,GAAG;AAAA,YACZ,SAAS;AAAA,UACX;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,CAAC,GAAG,YAAY,iBAAiB,GAAG;AAAA,QAClC,SAAS;AAAA,MACX;AAAA,IACF;AAAA,IAAG;AAAA,MACD,CAAC,YAAY,GAAG,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS,CAAC,GAAG,eAAe,KAAK,CAAC,GAAG,SAAS,CAAC,GAAG;AAAA,QACtH,cAAc;AAAA,QACd,oBAAoB;AAAA;AAAA,QAEpB;AAAA,QACA,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,SAAS;AAAA,QACT,YAAY,SAAS,kBAAkB;AAAA,QACvC,CAAC,QAAQ,GAAG;AAAA,UACV,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,WAAW;AAAA,QACb;AAAA;AAAA,QAEA,CAAC,YAAY,GAAG;AAAA,UACd,SAAS;AAAA,UACT,CAAC,GAAG,YAAY,OAAO,GAAG;AAAA,YACxB,MAAM;AAAA,UACR;AAAA,QACF;AAAA,QACA,CAAC,GAAG,YAAY,UAAU,YAAY,aAAa,YAAY,gBAAgB,GAAG;AAAA,UAChF,cAAc,MAAM;AAAA,QACtB;AAAA,QACA,CAAC,GAAG,YAAY,mBAAmB,GAAG;AAAA,UACpC,SAAS,GAAG,SAAS,MAAM,OAAO;AAAA,UAClC;AAAA,UACA;AAAA,UACA,YAAY,OAAO,kBAAkB;AAAA,QACvC;AAAA,QACA,CAAC,gBAAgB,YAAY,UAAU,GAAG;AAAA,UACxC,YAAY,CAAC,gBAAgB,kBAAkB,IAAI,eAAe,IAAI,cAAc,kBAAkB,IAAI,eAAe,EAAE,EAAE,KAAK,GAAG;AAAA,QACvI;AAAA,QACA,CAAC,GAAG,YAAY,aAAa,YAAY,iBAAiB,GAAG;AAAA,UAC3D,YAAY,CAAC,gBAAgB,kBAAkB,IAAI,eAAe,IAAI,cAAc,kBAAkB,IAAI,eAAe,IAAI,WAAW,iBAAiB,IAAI,eAAe,EAAE,EAAE,KAAK,GAAG;AAAA,QAC1L;AAAA,QACA,CAAC,GAAG,YAAY,YAAY,YAAY,MAAM,GAAG;AAAA,UAC/C,QAAQ;AAAA,UACR,YAAY,CAAC,cAAc,kBAAkB,IAAI,eAAe,IAAI,WAAW,kBAAkB,IAAI,eAAe,EAAE,EAAE,KAAK,GAAG;AAAA,QAClI;AAAA,QACA,CAAC,GAAG,YAAY,gBAAgB,GAAG;AAAA,UACjC,YAAY,SAAS,kBAAkB;AAAA,QACzC;AAAA,QACA,CAAC,GAAG,YAAY,SAAS,GAAG;AAAA,UAC1B,aAAa;AAAA,YACX,UAAU;AAAA,YACV,OAAO;AAAA,YACP,iBAAiB;AAAA,YACjB,SAAS;AAAA,UACX;AAAA,QACF;AAAA;AAAA;AAAA;AAAA,QAIA,CAAC,GAAG,YAAY,eAAe,GAAG;AAAA,UAChC,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,aAAa;AAAA,UACb,aAAa;AAAA,UACb,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,SAAS;AAAA,UACT,YAAY;AAAA,YACV,aAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF,CAAC,GAAG,iBAAiB,KAAK,CAAC,GAAG;AAAA,QAC5B,CAAC,GAAG,YAAY,aAAa,GAAG;AAAA,UAC9B,CAAC,GAAG,YAAY,kBAAkB,GAAG;AAAA,YACnC,QAAQ;AAAA,YACR,SAAS;AAAA,YACT,CAAC,GAAG,YAAY,UAAU,YAAY,gBAAgB,GAAG;AAAA,cACvD,eAAe,GAAG,WAAW,CAAC,MAAM,OAAO;AAAA,YAC7C;AAAA,UACF;AAAA,QACF;AAAA;AAAA,QAEA,aAAa;AAAA,UACX,WAAW;AAAA,YACT,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,YAAY;AAAA,YACZ,cAAc;AAAA,YACd,WAAW;AAAA,YACX,iBAAiB;AAAA;AAAA,YAEjB,aAAa;AAAA,cACX,UAAU;AAAA,cACV,OAAO,GAAG,kBAAkB;AAAA,cAC5B,QAAQ;AAAA,cACR,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,SAAS;AAAA,cACT,SAAS;AAAA,YACX;AAAA,UACF;AAAA;AAAA,UAEA,gCAAgC;AAAA,YAC9B,KAAK;AAAA,YACL,kBAAkB;AAAA,UACpB;AAAA,UACA,CAAC,KAAK,YAAY,EAAE,GAAG,SAAS,SAAS,SAAS;AAAA,YAChD,cAAc;AAAA,UAChB,GAAG,iBAAiB,KAAK,CAAC,GAAG,qBAAqB,KAAK,CAAC,GAAG;AAAA,YACzD,CAAC,GAAG,YAAY,UAAU,YAAY,cAAc,YAAY,gBAAgB,GAAG;AAAA,cACjF,cAAc;AAAA,YAChB;AAAA,YACA,CAAC,GAAG,YAAY,uBAAuB,GAAG;AAAA,cACxC,YAAY,aAAa,kBAAkB,IAAI,eAAe;AAAA,YAChE;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC,GAAG,qBAAqB,KAAK,CAAC,GAAG;AAAA,QAChC,CAAC,sBAAsB,YAAY;AAAA,mBACtB,YAAY,gBAAgB,GAAG;AAAA;AAAA,UAE1C,aAAa;AAAA,YACX,WAAW,6BAA6B,eAAe;AAAA,UACzD;AAAA,UACA,YAAY;AAAA,YACV,WAAW,6BAA6B,eAAe;AAAA,UACzD;AAAA,QACF;AAAA,QACA,CAAC,GAAG,YAAY,gBAAgB,YAAY,qBAAqB,YAAY,oBAAoB,YAAY,gBAAgB,GAAG;AAAA;AAAA,UAE9H,WAAW,eAAe,gBAAgB,GAAG;AAAA,UAC7C,YAAY;AAAA,YACV,WAAW,8BAA8B,eAAe;AAAA,UAC1D;AAAA,UACA,aAAa;AAAA,YACX,WAAW,4BAA4B,eAAe;AAAA,UACxD;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA,IAEA;AAAA,MACE,CAAC,GAAG,MAAM,gBAAgB,GAAG;AAAA,QAC3B,CAAC,YAAY,GAAG;AAAA,UACd,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA,EAAC;AACH;AAEA,IAAO,gBAAS,CAAC,WAAW,gBAAgB;AAC1C,QAAM,gBAAgB,sBAAsB,QAAQ,CAAC,OAAO,SAAS;AACnE,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AAEJ,SAAK,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,WAAW,OAAO;AAC3F,aAAO,CAAC;AAAA,IACV;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,gBAAgB,WAAW,IAAI;AAErC,UAAM,YAAY,MAAW,OAAO;AAAA,MAClC,gBAAgB;AAAA,MAChB,uBAAuB,MAAM;AAAA,MAC7B;AAAA,MACA,sBAAsB,kBAAkB;AAAA,MACxC,iBAAiB,GAAG,gBAAgB,IAAI;AAAA,MACxC,oBAAoB;AAAA,MACpB,eAAe;AAAA,IACjB,CAAC;AACD,UAAM,gBAAgB,IAAI,UAAU,mBAAmB,EAAE,SAAS,IAAI,EAAE,YAAY;AACpF,UAAM,gBAAgB,MAAW,WAAW;AAAA,MAC1C,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,iBAAiB;AAAA,MACjB,uBAAuB;AAAA,MACvB,aAAa;AAAA,MACb,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,0BAA0B;AAAA;AAAA,MAE1B,uBAAuB,IAAI,UAAU,mBAAmB,EAAE,SAAS,IAAI,EAAE,YAAY;AAAA;AAAA,MAErF,qBAAqB;AAAA,MACrB,0BAA0B;AAAA,MAC1B,6BAA6B;AAAA,MAC7B,yBAAyB;AAAA,MACzB,2BAA2B;AAAA,MAC3B,eAAe;AAAA;AAAA,MAEf,iCAAiC;AAAA,MACjC,+BAA+B;AAAA,IACjC,GAAG,SAAS,CAAC,GAAG,sBAAsB,CAAC;AACvC,WAAO;AAAA;AAAA,MAEP,aAAa,SAAS;AAAA;AAAA,MAEtB,mBAAmB,SAAS;AAAA;AAAA,MAE5B,iBAAiB,SAAS;AAAA;AAAA,MAE1B,cAAc,WAAW,OAAO;AAAA,MAAG,cAAc,eAAe,MAAM;AAAA;AAAA,MAEtE,YAAY,SAAS;AAAA;AAAA,MAErB,iBAAkB,SAAS;AAAA,MAAG,gBAAgB,WAAW,UAAU;AAAA,MAAG,gBAAgB,WAAW,YAAY;AAAA,MAAG,eAAe,WAAW,UAAU;AAAA,IAAC;AAAA,EACvJ,GAAG,WAAS;AACV,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO;AAAA,MACL,eAAe;AAAA,MACf,aAAa,MAAM,kBAAkB;AAAA,MACrC,YAAY,MAAM;AAAA,MAClB,mBAAmB,MAAM;AAAA,MACzB,eAAe;AAAA,MACf,oBAAoB;AAAA,MACpB,8BAA8B;AAAA,MAC9B,iBAAiB;AAAA,MACjB,uBAAuB;AAAA,MACvB,iCAAiC;AAAA,MACjC,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,qBAAqB;AAAA,MACrB,+BAA+B;AAAA,MAC/B,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,0BAA0B;AAAA;AAAA,MAE1B,uBAAuB;AAAA;AAAA,MAEvB,qBAAqB;AAAA,MACrB,0BAA0B;AAAA,MAC1B,6BAA6B;AAAA,MAC7B,yBAAyB;AAAA,MACzB,2BAA2B;AAAA,MAC3B,kBAAkB,MAAM;AAAA,IAC1B;AAAA,EACF,CAAC;AACD,SAAO,cAAc,SAAS;AAChC;;;ACrZO,IAAM,qBAAqB,OAAO,oBAAoB;AACtD,IAAM,oBAAoB,MAAM;AACrC,SAAO,OAAO,oBAAoB,MAAS;AAC7C;AACO,IAAM,qBAAqB,WAAS;AACzC,MAAI,IAAI,IAAI;AACZ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,kBAAkB,KAAK,CAAC;AAC5B,UAAQ,oBAAoB;AAAA,IAC1B,WAAW,SAAS,MAAM;AACxB,UAAIC,KAAIC;AACR,cAAQA,OAAMD,MAAK,MAAM,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW,QAAQC,QAAO,SAASA,MAAK,cAAc,QAAQ,cAAc,SAAS,SAAS,UAAU;AAAA,IACtL,CAAC;AAAA,IACD,MAAM,SAAS,MAAM;AACnB,UAAID,KAAIC;AACR,cAAQA,OAAMD,MAAK,MAAM,UAAU,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW,QAAQC,QAAO,SAASA,MAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK;AAAA,IAClK,CAAC;AAAA,IACD,YAAY,SAAS,MAAM;AACzB,UAAID,KAAIC;AACR,cAAQA,OAAMD,MAAK,MAAM,gBAAgB,QAAQA,QAAO,SAAS,SAASA,IAAG,WAAW,QAAQC,QAAO,SAASA,MAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW;AAAA,IAC1L,CAAC;AAAA,IACD,YAAY,KAAK,MAAM,eAAe,QAAQ,OAAO,SAAS,KAAK;AAAA,IACnE,UAAU,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,KAAK;AAAA,IAC/D,aAAa,KAAK,MAAM,gBAAgB,QAAQ,OAAO,SAAS,KAAK,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW;AAAA,EAC1I,CAAC;AACH;;;ACXO,IAAM,YAAY,OAAO;AAAA,EAC9B,IAAI;AAAA,EACJ,WAAW;AAAA;AAAA,EAEX,OAAO;AAAA,EACP,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,UAAU;AAAA,IACR,MAAM,CAAC,QAAQ,MAAM;AAAA,EACvB;AAAA,EACA,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,mBAAmB;AAAA,IACjB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,mBAAmB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA,sBAAsB;AAAA,IACpB,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,SAAS,CAAC,UAAU,KAAK;AAAA,EACzB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,sBAAsB;AACxB;AACA,IAAM,aAAa,CAAC;AACpB,IAAO,eAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,UAAU;AAAA,EACjB,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,wBAAgB,QAAQ,KAAK;AACjC,UAAM,WAAW,kBAAkB;AACnC,UAAM,YAAY,SAAS,MAAM;AAC/B,UAAI;AACJ,aAAO,aAAa,QAAQ,MAAM,eAAe,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,IAC9K,CAAC;AACD,UAAM,CAAC,SAAS,MAAM,IAAI,cAAS,WAAW,SAAS,MAAM;AAC3D,aAAO,CAAC;AAAA,IACV,CAAC,CAAC;AACF,UAAM,QAAQ,WAAW,oBAAI,IAAI,CAAC;AAClC,UAAM,iBAAiB,OAAO,mBAAmB,IAAI,MAAS,CAAC;AAC/D,UAAM,kBAAkB,SAAS,MAAM;AACrC,UAAI,eAAe,UAAU,QAAW;AACtC,eAAO,eAAe;AAAA,MACxB;AACA,aAAO,MAAM;AAAA,IACf,CAAC;AACD,UAAM;AAAA,MACJ;AAAA,IACF,IAAI,SAAS,KAAK;AAClB,UAAM,YAAY,WAAW,KAAK;AAClC,cAAU,MAAM;AACd,gBAAU,QAAQ;AAAA,IACpB,CAAC;AACD,gBAAY,MAAM;AAChB,yBAAW,EAAE,MAAM,oBAAoB,QAAQ,MAAM,SAAS,WAAW,QAAQ,8DAA8D;AAC/I,yBAAW,EAAE,eAAe,UAAU,UAAa,MAAM,oBAAoB,OAAO,QAAQ,0FAA0F;AAAA,IAMxL,CAAC;AACD,UAAM,aAAa,IAAI,CAAC,CAAC;AACzB,UAAM,qBAAqB,IAAI,CAAC,CAAC;AACjC,UAAM,cAAc,IAAI,CAAC,CAAC;AAC1B,UAAM,OAAO,MAAM;AACjB,YAAM,iBAAiB,CAAC;AACxB,iBAAW,YAAY,MAAM,MAAM,OAAO,GAAG;AAC3C,uBAAe,SAAS,GAAG,IAAI;AAAA,MACjC;AACA,kBAAY,QAAQ;AAAA,IACtB,GAAG;AAAA,MACD,OAAO;AAAA,IACT,CAAC;AACD,gBAAY,MAAM;AAChB,UAAI,MAAM,cAAc,QAAW;AACjC,YAAI,OAAO,CAAC;AACZ,cAAM,WAAW,MAAM,YAAY,YAAY,MAAM,MAAM,SAAS,IAAI;AACxE,YAAI,YAAY,MAAM,cAAc,QAAW;AAC7C,iBAAO,aAAK,CAAC,EAAE,OAAO,MAAM,SAAS,UAAU,GAAG,MAAM,SAAS,CAAC;AAAA,QACpE,OAAO;AACL,iBAAO,CAAC;AAAA,QACV;AACA,YAAI,CAAC,qBAAa,WAAW,OAAO,IAAI,GAAG;AACzC,qBAAW,QAAQ;AAAA,QACrB;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,MAAM,MAAM,cAAc,kBAAgB;AAC9C,UAAI,cAAc;AAChB,2BAAmB,QAAQ,aAAa,MAAM;AAAA,MAChD;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,UAAM,sBAAsB,IAAI,CAAC,CAAC;AAClC,UAAM,CAAC,aAAa,kBAAkB,GAAG,MAAM;AAC7C,UAAI,oBAAoB,CAAC;AACzB,yBAAmB,MAAM,QAAQ,SAAO;AACtC,cAAM,WAAW,YAAY,MAAM,GAAG;AACtC,YAAI,UAAU;AACZ,8BAAoB,kBAAkB,OAAO,MAAM,SAAS,UAAU,CAAC;AAAA,QACzE;AAAA,MACF,CAAC;AACD,0BAAoB,aAAK,iBAAiB;AAC1C,UAAI,CAAC,qBAAa,oBAAoB,OAAO,iBAAiB,GAAG;AAC/D,4BAAoB,QAAQ;AAAA,MAC9B;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AAED,UAAM,mBAAmB,UAAQ;AAC/B,UAAI,MAAM,YAAY;AAEpB,cAAM;AAAA,UACJ,KAAK;AAAA,QACP,IAAI;AACJ,cAAM,QAAQ,mBAAmB,MAAM,SAAS,SAAS;AACzD,YAAI;AACJ,YAAI,MAAM,UAAU;AAClB,cAAI,OAAO;AACT,8BAAkB,mBAAmB,MAAM,OAAO,SAAO,QAAQ,SAAS;AAAA,UAC5E,OAAO;AACL,8BAAkB,CAAC,GAAG,mBAAmB,OAAO,SAAS;AAAA,UAC3D;AAAA,QACF,OAAO;AACL,4BAAkB,CAAC,SAAS;AAAA,QAC9B;AAEA,cAAM,aAAa,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG;AAAA,UAC9C,cAAc;AAAA,QAChB,CAAC;AACD,YAAI,CAAC,qBAAa,iBAAiB,mBAAmB,KAAK,GAAG;AAC5D,cAAI,MAAM,iBAAiB,QAAW;AACpC,+BAAmB,QAAQ;AAAA,UAC7B;AACA,eAAK,uBAAuB,eAAe;AAC3C,cAAI,SAAS,MAAM,UAAU;AAC3B,iBAAK,YAAY,UAAU;AAAA,UAC7B,OAAO;AACL,iBAAK,UAAU,UAAU;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAEA,UAAI,WAAW,UAAU,YAAY,CAAC,MAAM,YAAY,eAAe,MAAM,QAAQ;AACnF,wBAAgB,UAAU;AAAA,MAC5B;AAAA,IACF;AACA,UAAM,iBAAiB,IAAI,CAAC,CAAC;AAC7B,UAAM,MAAM,MAAM,UAAU,WAAY;AACtC,UAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,eAAe;AAClG,UAAI,CAAC,qBAAa,eAAe,OAAO,QAAQ,GAAG;AACjD,uBAAe,QAAQ,SAAS,MAAM;AAAA,MACxC;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AACD,QAAI;AACJ,UAAM,mBAAmB,UAAQ;AAC/B,mBAAa,OAAO;AACpB,gBAAU,WAAW,MAAM;AACzB,YAAI,MAAM,cAAc,QAAW;AACjC,qBAAW,QAAQ;AAAA,QACrB;AACA,aAAK,oBAAoB,KAAK,KAAK,SAAS,CAAC,CAAC;AAAA,MAChD,CAAC;AAAA,IACH;AACA,UAAM,WAAW,SAAS,MAAM,CAAC,CAAC,MAAM,QAAQ;AAChD,UAAM,QAAQ,SAAS,MAAM,UAAU,UAAU,KAAK;AACtD,UAAM,aAAa,IAAI,UAAU;AACjC,UAAM,wBAAwB,WAAW,KAAK;AAC9C,gBAAY,MAAM;AAChB,UAAI;AACJ,WAAK,MAAM,SAAS,YAAY,MAAM,SAAS,eAAe,gBAAgB,OAAO;AACnF,mBAAW,QAAQ;AACnB,8BAAsB,QAAQ,gBAAgB;AAAA,MAChD,OAAO;AACL,mBAAW,QAAQ,MAAM;AACzB,8BAAsB,QAAQ;AAAA,MAChC;AACA,WAAK,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO;AAC1H,mBAAW,QAAQ,SAAS,KAAK;AAAA,MACnC;AAAA,IACF,CAAC;AACD,UAAM,eAAe,SAAS,MAAM,WAAW,UAAU,QAAQ;AACjE,UAAM,kBAAkB,UAAQ;AAC9B,qBAAe,QAAQ;AACvB,WAAK,mBAAmB,IAAI;AAC5B,WAAK,cAAc,IAAI;AAAA,IACzB;AAEA,UAAM,sBAAsB,IAAI,eAAe,KAAK;AACpD,UAAM,WAAW,WAAW,KAAK;AAEjC,UAAM,gBAAgB,MAAM;AAC1B,UAAI,aAAa,OAAO;AACtB,4BAAoB,QAAQ,eAAe;AAAA,MAC7C;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AAED,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,SAAS,OAAO;AACnB,iBAAS,QAAQ;AACjB;AAAA,MACF;AACA,UAAI,aAAa,OAAO;AACtB,uBAAe,QAAQ,oBAAoB;AAAA,MAC7C,OAAO;AAEL,wBAAgB,UAAU;AAAA,MAC5B;AAAA,IACF,GAAG;AAAA,MACD,WAAW;AAAA,IACb,CAAC;AACD,UAAM,YAAY,SAAS,MAAM;AAC/B,aAAO;AAAA,QACL,CAAC,GAAG,UAAU,KAAK,EAAE,GAAG;AAAA,QACxB,CAAC,GAAG,UAAU,KAAK,OAAO,GAAG;AAAA,QAC7B,CAAC,GAAG,UAAU,KAAK,IAAI,WAAW,KAAK,EAAE,GAAG;AAAA,QAC5C,CAAC,GAAG,UAAU,KAAK,mBAAmB,GAAG,sBAAsB;AAAA,QAC/D,CAAC,GAAG,UAAU,KAAK,MAAM,GAAG,MAAM;AAAA,QAClC,CAAC,GAAG,UAAU,KAAK,IAAI,MAAM,KAAK,EAAE,GAAG;AAAA,MACzC;AAAA,IACF,CAAC;AACD,UAAM,gBAAgB,SAAS,MAAM,aAAa,CAAC;AACnD,UAAM,iBAAiB,SAAS,OAAO;AAAA,MACrC,YAAY;AAAA,QACV,MAAM,GAAG,cAAc,KAAK;AAAA,MAC9B;AAAA,MACA,QAAQ,uBAAe,GAAG,cAAc,KAAK,kBAAkB;AAAA,MAC/D,OAAO;AAAA,QACL,MAAM,GAAG,cAAc,KAAK;AAAA,MAC9B;AAAA,IACF,EAAE;AACF,yBAAqB,IAAI;AACzB,UAAM,kBAAkB,WAAY;AAClC,UAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACrF,YAAM,OAAO,CAAC;AACd,YAAM,aAAa,MAAM;AACzB,gBAAU,QAAQ,cAAY;AAC5B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,WAAW,IAAI,QAAQ;AAC3B,aAAK,KAAK,KAAK,GAAG,gBAAgB,MAAM,iBAAiB,CAAC,CAAC;AAAA,MAC7D,CAAC;AACD,aAAO;AAAA,IACT;AAKA,UAAM,kBAAkB,UAAQ;AAC9B,UAAI;AACJ,WAAK,SAAS,IAAI;AAClB,uBAAiB,IAAI;AACrB,OAAC,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,QAAQ;AAAA,IACnI;AACA,UAAM,uBAAuB,CAAC,KAAK,SAAS;AAC1C,UAAI;AACJ,YAAM,sBAAsB,KAAK,YAAY,MAAM,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,sBAAsB,CAAC;AACxH,UAAI,cAAc,eAAe,MAAM,OAAO,OAAK,MAAM,GAAG;AAC5D,UAAI,MAAM;AACR,oBAAY,KAAK,GAAG;AAAA,MACtB,WAAW,WAAW,UAAU,UAAU;AAExC,cAAM,cAAc,gBAAgB,MAAM,iBAAiB,CAAC;AAC5D,sBAAc,aAAK,YAAY,OAAO,OAAK,CAAC,YAAY,SAAS,CAAC,CAAC,CAAC;AAAA,MACtE;AACA,UAAI,CAAC,qBAAa,gBAAgB,WAAW,GAAG;AAC9C,wBAAgB,WAAW;AAAA,MAC7B;AAAA,IACF;AACA,UAAM,mBAAmB,CAAC,KAAK,SAAS;AACtC,YAAM,MAAM,IAAI,KAAK,IAAI;AACzB,YAAM,QAAQ,IAAI,IAAI,MAAM,KAAK;AAAA,IACnC;AACA,UAAM,qBAAqB,SAAO;AAChC,YAAM,MAAM,OAAO,GAAG;AACtB,YAAM,QAAQ,IAAI,IAAI,MAAM,KAAK;AAAA,IACnC;AACA,UAAM,mBAAmB,IAAI,CAAC;AAC9B,UAAM,aAAa,SAAS,MAAM;AAChC,UAAI;AACJ,aAAO,MAAM,cAAc,MAAM,gBAAgB,KAAK,aAAa,QAAQ,aAAa,SAAS,SAAS,SAAS,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,SAAO;AACrL,YAAI,OAAO,MAAM,cAAc,MAAM;AACrC,eAAO,OAAO,SAAS,aAAa,KAAK,GAAG,IAAI;AAChD,eAAO,aAAa,MAAM;AAAA,UACxB,OAAO,GAAG,UAAU,KAAK;AAAA,QAC3B,GAAG,KAAK;AAAA,MACV,IAAI;AAAA,IACN,CAAC;AACD,2BAAe;AAAA,MACb;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,cAAc;AAAA,MACd;AAAA,MACA;AAAA,MACA,KAAK;AAAA,MACL,MAAM;AAAA,MACN,cAAc,SAAS,MAAM,MAAM,YAAY;AAAA,MAC/C,mBAAmB,SAAS,MAAM,MAAM,iBAAiB;AAAA,MACzD,kBAAkB,SAAS,MAAM,MAAM,gBAAgB;AAAA,MACvD,mBAAmB,SAAS,MAAM,MAAM,iBAAiB;AAAA,MACzD,sBAAsB,SAAS,MAAM,MAAM,oBAAoB;AAAA,MAC/D,mBAAmB,SAAS,MAAM,MAAM,iBAAiB;AAAA,MACzD,iBAAiB;AAAA,MACjB,OAAO,SAAS,MAAM,MAAM,KAAK;AAAA,MACjC;AAAA,MACA,gBAAgB,SAAS,MAAM,UAAU,QAAQ,eAAe,QAAQ,IAAI;AAAA,MAC5E,QAAQ,SAAS,MAAM,UAAU,QAAQ,MAAM,SAAS,IAAI;AAAA,MAC5D,kBAAkB,WAAW,MAAS;AAAA,MACtC,cAAc;AAAA,MACd,aAAa;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,oBAAoB,SAAS,MAAM,MAAM,kBAAkB;AAAA,MAC3D,eAAe;AAAA,IACjB,CAAC;AACD,UAAM,kBAAkB,MAAM;AAC5B,UAAI;AACJ,aAAO,WAAW,SAAS,iBAAiB,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC;AAAA,IACrH;AACA,WAAO,MAAM;AACX,UAAI;AACJ,YAAM,YAAY,gBAAgB;AAClC,YAAM,aAAa,iBAAiB,SAAS,UAAU,SAAS,KAAK,WAAW,UAAU,gBAAgB,MAAM;AAEhH,YAAM,iBAAiB,CAAAC,eAAa;AAClC,eAAO,WAAW,UAAU,gBAAgB,MAAM,mBAAmBA;AAAA;AAAA,UAErEA,WAAU,IAAI,CAAC,OAAO;AAAA;AAAA,YACtB,YAAa,qBAAqB;AAAA,cAChC,OAAO,MAAM;AAAA,cACb,oBAAoB,QAAQ,iBAAiB;AAAA,YAC/C,GAAG;AAAA,cACD,SAAS,MAAM;AAAA,YACjB,CAAC;AAAA,WAAC;AAAA;AAAA,MACJ;AACA,YAAM,wBAAwB,KAAK,MAAM,yBAAyB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,MAAM,YAAa,0BAAkB,MAAM,IAAI;AAC/J,aAAO,QAAQ,YAAa,qBAAU,eAAc,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QAChF,eAAe,MAAM;AAAA,QACrB,aAAa,GAAG,UAAU,KAAK;AAAA,QAC/B,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,SAAS,CAAC,UAAU,OAAO,MAAM,OAAO,OAAO,KAAK;AAAA,QACpD,QAAQ;AAAA,QACR,MAAM,MAAM;AAAA,QACZ,QAAQ,eAAe,SAAS;AAAA,QAChC,iBAAiB,UAAQ;AAAA,QACzB,iBAAiB,eAAa;AAE5B,gBAAM,MAAM,UAAU;AACtB,gBAAM,kBAAkB,MAAM,UAAU,MAAM,CAAC,GAAG,IAAI;AACtD,iBAAO,YAAa,UAAW,MAAM,CAAC,YAAa,iBAAS;AAAA,YAC1D,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,sBAAsB,QAAQ;AAAA,UAChC,GAAG;AAAA,YACD,SAAS,MAAM;AAAA,UACjB,CAAC,GAAG,YAAa,aAAa,MAAM;AAAA,YAClC,SAAS,MAAM,CAAC,YAAa,iBAAS;AAAA,cACpC,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,sBAAsB,QAAQ;AAAA,YAChC,GAAG;AAAA,cACD,SAAS,MAAM;AAAA,YACjB,CAAC,CAAC;AAAA,UACJ,CAAC,CAAC,CAAC;AAAA,QACL;AAAA,QACA,YAAY,WAAW,UAAU,gBAAgB,MAAM,mBAAmB,oBAAS,aAAa,oBAAS;AAAA,QACzG,OAAO;AAAA,QACP,kBAAkB;AAAA,QAClB,mBAAmB,kBAAgB;AACjC,2BAAiB,QAAQ;AAAA,QAC3B;AAAA,MACF,CAAC,GAAG;AAAA,QACF,SAAS,MAAM,CAAC,YAAa,UAAU;AAAA,UACrC,MAAM;AAAA,QACR,GAAG;AAAA,UACD,SAAS,MAAM,CAAC,YAAa,OAAO;AAAA,YAClC,SAAS;AAAA,cACP,SAAS;AAAA,YACX;AAAA,YACA,eAAe;AAAA,UACjB,GAAG,CAAC,YAAa,aAAa,MAAM;AAAA,YAClC,SAAS,MAAM,CAAC,eAAe,gBAAgB,CAAC,CAAC;AAAA,UACnD,CAAC,CAAC,CAAC,CAAC;AAAA,QACN,CAAC,CAAC;AAAA,MACJ,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AACF,CAAC;;;AC1dD,aAAK,UAAU,SAAU,KAAK;AAC5B,MAAI,UAAU,aAAK,MAAM,YAAI;AAC7B,MAAI,UAAU,iBAAS,MAAM,gBAAQ;AACrC,MAAI,UAAU,gBAAQ,MAAM,eAAO;AACnC,MAAI,UAAU,gBAAQ,MAAM,eAAO;AACnC,MAAI,UAAU,kBAAU,MAAM,iBAAS;AACvC,SAAO;AACT;AACA,aAAK,OAAO;AACZ,aAAK,UAAU;AACf,aAAK,UAAU;AACf,aAAK,YAAY;AAEjB,IAAO,eAAQ;;;ACnBf,IAAM,qBAAqB,OAAO,oBAAoB;AACtD,IAAO,0BAAQ;;;ACsBA,SAAA,SAAUC,OAAOC,UAAUC,SAAS;AAClD,MAAAC,OAIID,WAAW,CAAA,GAAEE,kBAAAD,KAHhBE,YAAAA,aAAUD,oBAAG,SAAA,QAAKA,iBAAAE,iBAAAH,KAClBI,WAAAA,YAASD,mBAAG,SAAA,QAAKA,gBAAAE,oBAAAL,KACjBM,cAAAA,eAAYD,sBAAGE,SAAAA,SAASF;AAOzB,MAAIG;AACJ,MAAIC,YAAY;AAGhB,MAAIC,WAAW;AAGf,WAASC,uBAAuB;AAC/B,QAAIH,WAAW;AACdI,mBAAaJ,SAAS;IACvB;EACD;AAGA,WAASK,OAAOd,UAAS;AACxB,QAAAe,QAAiCf,YAAW,CAAA,GAAEgB,qBAAAD,MAAtCE,cAAAA,eAAYD,uBAAG,SAAA,QAAKA;AAC5BJ,yBAAoB;AACpBF,gBAAY,CAACO;EACd;AAOA,WAASC,UAAuB;AAAA,aAAAC,OAAAC,UAAAC,QAAZC,aAAUC,IAAAA,MAAAJ,IAAA,GAAAK,OAAA,GAAAA,OAAAL,MAAAK,QAAA;AAAVF,iBAAUE,IAAA,IAAAJ,UAAAI,IAAA;IAAA;AAC7B,QAAIC,OAAO;AACX,QAAIC,UAAUC,KAAKC,IAAG,IAAKjB;AAE3B,QAAID,WAAW;AACd;IACD;AAGA,aAASmB,OAAO;AACflB,iBAAWgB,KAAKC,IAAG;AACnB7B,eAAS+B,MAAML,MAAMH,UAAU;IAChC;AAMA,aAASS,QAAQ;AAChBtB,kBAAYD;IACb;AAEA,QAAI,CAACH,aAAaE,gBAAgB,CAACE,WAAW;AAM7CoB,WAAI;IACL;AAEAjB,yBAAoB;AAEpB,QAAIL,iBAAiBC,UAAakB,UAAU5B,OAAO;AAClD,UAAIO,WAAW;AAMdM,mBAAWgB,KAAKC,IAAG;AACnB,YAAI,CAACzB,YAAY;AAChBM,sBAAYuB,WAAWzB,eAAewB,QAAQF,MAAM/B,KAAK;QAC1D;MACD,OAAO;AAKN+B,aAAI;MACL;IACD,WAAW1B,eAAe,MAAM;AAY/BM,kBAAYuB,WACXzB,eAAewB,QAAQF,MACvBtB,iBAAiBC,SAAYV,QAAQ4B,UAAU5B,KAChD;IACD;EACD;AAEAoB,UAAQJ,SAASA;AAGjB,SAAOI;AACR;AClHe,SAAA,SAAUpB,OAAOC,UAAUC,SAAS;AAClD,MAAAC,OAA4BD,WAAW,CAAA,GAAEiC,eAAAhC,KAAjCiC,SAAAA,UAAOD,iBAAG,SAAA,QAAKA;AACvB,SAAOE,SAASrC,OAAOC,UAAU;IAAEQ,cAAc2B,YAAY;EAAM,CAAC;AACrE;;;AClBA,IAAM,cAAc,IAAI,kBAAU,eAAe;AAAA,EAC/C,IAAI;AAAA,IACF,SAAS;AAAA,EACX;AACF,CAAC;AACD,IAAM,YAAY,IAAI,kBAAU,aAAa;AAAA,EAC3C,IAAI;AAAA,IACF,WAAW;AAAA,EACb;AACF,CAAC;AACD,IAAM,eAAe,YAAU;AAAA,EAC7B,CAAC,GAAG,MAAM,YAAY,EAAE,GAAG,SAAS,SAAS,CAAC,GAAG,eAAe,KAAK,CAAC,GAAG;AAAA,IACvE,UAAU;AAAA,IACV,SAAS;AAAA,IACT,OAAO,MAAM;AAAA,IACb,WAAW;AAAA,IACX,eAAe;AAAA,IACf,SAAS;AAAA,IACT,YAAY,aAAa,MAAM,kBAAkB,IAAI,MAAM,mBAAmB;AAAA,IAC9E,cAAc;AAAA,MACZ,UAAU;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,oBAAoB;AAAA,MAClB,UAAU;AAAA,MACV,CAAC,WAAW,MAAM,YAAY,EAAE,GAAG;AAAA,QACjC,UAAU;AAAA,QACV,KAAK;AAAA,QACL,kBAAkB;AAAA,QAClB,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,WAAW,MAAM;AAAA,QACjB,CAAC,GAAG,MAAM,YAAY,MAAM,GAAG;AAAA,UAC7B,UAAU;AAAA,UACV,KAAK;AAAA,UACL,kBAAkB;AAAA,UAClB,QAAQ,CAAC,MAAM,cAAc;AAAA,QAC/B;AAAA,QACA,CAAC,GAAG,MAAM,YAAY,OAAO,GAAG;AAAA,UAC9B,UAAU;AAAA,UACV,KAAK;AAAA,UACL,OAAO;AAAA,UACP,aAAa,MAAM,cAAc,MAAM,YAAY,IAAI;AAAA,UACvD,YAAY,aAAa,MAAM,gBAAgB;AAAA;AAAA,QACjD;AAAA,QACA,CAAC,IAAI,MAAM,YAAY,cAAc,MAAM,YAAY,MAAM,GAAG;AAAA,UAC9D,WAAW,EAAE,MAAM,cAAc,KAAK;AAAA,QACxC;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,GAAG,MAAM,YAAY,MAAM,GAAG;AAAA,YAC7B,QAAQ,CAAC,MAAM,gBAAgB;AAAA,UACjC;AAAA,UACA,CAAC,GAAG,MAAM,YAAY,OAAO,GAAG;AAAA,YAC9B,aAAa,MAAM,gBAAgB,MAAM,YAAY,IAAI;AAAA,UAC3D;AAAA,UACA,CAAC,IAAI,MAAM,YAAY,cAAc,MAAM,YAAY,MAAM,GAAG;AAAA,YAC9D,WAAW,EAAE,MAAM,gBAAgB,KAAK;AAAA,UAC1C;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,CAAC,GAAG,MAAM,YAAY,MAAM,GAAG;AAAA,YAC7B,QAAQ,EAAE,MAAM,gBAAgB;AAAA,UAClC;AAAA,UACA,CAAC,GAAG,MAAM,YAAY,OAAO,GAAG;AAAA,YAC9B,aAAa,MAAM,gBAAgB,MAAM,YAAY,IAAI;AAAA,UAC3D;AAAA,UACA,CAAC,IAAI,MAAM,YAAY,cAAc,MAAM,YAAY,MAAM,GAAG;AAAA,YAC9D,WAAW,EAAE,MAAM,gBAAgB,KAAK;AAAA,UAC1C;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,GAAG,MAAM,YAAY,YAAY,GAAG;AAAA,QACnC,UAAU;AAAA,QACV,YAAY,WAAW,MAAM,kBAAkB;AAAA,QAC/C,YAAY;AAAA,UACV,UAAU;AAAA,UACV,KAAK;AAAA,UACL,gBAAgB;AAAA,UAChB,QAAQ;AAAA,UACR,kBAAkB;AAAA,UAClB,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,YAAY,MAAM;AAAA,UAClB,SAAS;AAAA,UACT,YAAY,OAAO,MAAM,kBAAkB;AAAA,UAC3C,SAAS;AAAA,UACT,eAAe;AAAA,QACjB;AAAA,MACF;AAAA,MACA,CAAC,GAAG,MAAM,YAAY,OAAO,GAAG;AAAA,QAC9B,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,CAAC,UAAU,GAAG;AAAA,UACZ,SAAS;AAAA,UACT,eAAe;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAAA;AAAA;AAAA,IAGA,CAAC,OAAO,GAAG;AAAA,MACT,OAAO,MAAM;AAAA,IACf;AAAA;AAAA;AAAA,IAGA,CAAC,GAAG,MAAM,YAAY,MAAM,GAAG;AAAA,MAC7B,UAAU;AAAA,MACV,SAAS;AAAA,MACT,UAAU,MAAM;AAAA,MAChB,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,UAAU;AAAA,QACR,UAAU;AAAA,QACV,SAAS;AAAA,QACT,QAAQ,MAAM,cAAc,MAAM,YAAY,KAAK;AAAA,QACnD,SAAS,MAAM,cAAc,MAAM,YAAY,KAAK;AAAA,QACpD,iBAAiB,MAAM;AAAA,QACvB,cAAc;AAAA,QACd,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,yBAAyB;AAAA,QACzB,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,UAChB,KAAK;AAAA,UACL,kBAAkB;AAAA,QACpB;AAAA,QACA,kBAAkB;AAAA,UAChB,KAAK;AAAA,UACL,gBAAgB;AAAA,UAChB,gBAAgB;AAAA,QAClB;AAAA,QACA,kBAAkB;AAAA,UAChB,gBAAgB;AAAA,UAChB,QAAQ;AAAA,UACR,gBAAgB;AAAA,QAClB;AAAA,QACA,kBAAkB;AAAA,UAChB,QAAQ;AAAA,UACR,kBAAkB;AAAA,UAClB,gBAAgB;AAAA,QAClB;AAAA,MACF;AAAA,MACA,UAAU;AAAA,QACR,WAAW;AAAA,QACX,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,yBAAyB;AAAA,QACzB,yBAAyB;AAAA,MAC3B;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAIA,CAAC,QAAQ,MAAM,YAAY,MAAM,GAAG;AAAA,MAClC,UAAU,MAAM;AAAA,MAChB,GAAG;AAAA,QACD,QAAQ,MAAM,gBAAgB,MAAM,YAAY,KAAK;AAAA,QACrD,SAAS,MAAM,gBAAgB,MAAM,YAAY,KAAK;AAAA,MACxD;AAAA,IACF;AAAA;AAAA,IAEA,CAAC,QAAQ,MAAM,YAAY,MAAM,GAAG;AAAA,MAClC,UAAU,MAAM;AAAA,MAChB,GAAG;AAAA,QACD,QAAQ,MAAM,gBAAgB,MAAM,aAAa;AAAA,QACjD,SAAS,MAAM,gBAAgB,MAAM,aAAa;AAAA,MACpD;AAAA,IACF;AAAA,IACA,CAAC,IAAI,MAAM,YAAY,cAAc,MAAM,YAAY,OAAO,GAAG;AAAA,MAC/D,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AACH;AAEA,IAAOE,iBAAQ,sBAAsB,QAAQ,WAAS;AACpD,QAAM,YAAY,MAAW,OAAO;AAAA,IAClC,gBAAgB,MAAM;AAAA,IACtB,aAAa,MAAM,kBAAkB;AAAA,IACrC,eAAe,MAAM,kBAAkB;AAAA,IACvC,eAAe,MAAM;AAAA,EACvB,CAAC;AACD,SAAO,CAAC,aAAa,SAAS,CAAC;AACjC,GAAG;AAAA,EACD,eAAe;AACjB,CAAC;;;ACpMD,IAAIC,UAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAQO,IAAM,YAAY,OAAO;AAAA,EAC9B,WAAW;AAAA,EACX,UAAU;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,MAAM;AAAA,EACN,kBAAkB;AAAA,EAClB,KAAK,kBAAU;AAAA,EACf,OAAO;AAAA,EACP,WAAW,kBAAU;AACvB;AAEA,IAAI,mBAAmB;AACvB,SAAS,YAAY,UAAU,OAAO;AACpC,SAAO,CAAC,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,MAAM,OAAO,KAAK,CAAC;AACtD;AACO,SAAS,oBAAoB,SAAS;AAC3C,QAAM,YAAY,QAAQ;AAC1B,qBAAmB,OAAO,cAAc,aAAa,YAAY,MAAM,YAAa,WAAW,MAAM,IAAI;AAC3G;AACA,IAAO,eAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,yBAAiB,UAAU,GAAG;AAAA,IACnC,MAAM;AAAA,IACN,UAAU;AAAA,IACV,kBAAkB;AAAA,EACpB,CAAC;AAAA,EACD,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,wBAAgB,QAAQ,KAAK;AACjC,UAAM,CAAC,SAAS,MAAM,IAAIC,eAAS,SAAS;AAC5C,UAAM,YAAY,WAAW,MAAM,YAAY,CAAC,YAAY,MAAM,UAAU,MAAM,KAAK,CAAC;AACxF,QAAI;AACJ,UAAM,CAAC,MAAM,MAAM,UAAU,MAAM,MAAM,KAAK,GAAG,MAAM;AACrD,yBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,OAAO;AACtF,uBAAiB,SAAS,MAAM,OAAO,MAAM;AAC3C,kBAAU,QAAQ,MAAM;AAAA,MAC1B,CAAC;AACD,yBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe;AAAA,IACjF,GAAG;AAAA,MACD,WAAW;AAAA,MACX,OAAO;AAAA,IACT,CAAC;AACD,oBAAgB,MAAM;AACpB,yBAAmB,QAAQ,mBAAmB,SAAS,SAAS,eAAe,OAAO;AAAA,IACxF,CAAC;AACD,WAAO,MAAM;AACX,UAAI,IAAI;AACR,YAAM;AAAA,QACF,OAAO;AAAA,MACT,IAAI,OACJ,WAAWD,QAAO,OAAO,CAAC,OAAO,CAAC;AACpC,YAAM;AAAA,QACJ,OAAO,KAAK,MAAM,SAAS,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,MAC3E,IAAI;AACJ,YAAM,YAAY,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AACxF,YAAM,gBAAgB;AAAA,QACpB,CAAC,OAAO,KAAK,GAAG;AAAA,QAChB,CAAC,UAAU,KAAK,GAAG;AAAA,QACnB,CAAC,GAAG,UAAU,KAAK,KAAK,GAAG,KAAK,UAAU;AAAA,QAC1C,CAAC,GAAG,UAAU,KAAK,KAAK,GAAG,KAAK,UAAU;AAAA,QAC1C,CAAC,GAAG,UAAU,KAAK,WAAW,GAAG,UAAU;AAAA,QAC3C,CAAC,GAAG,UAAU,KAAK,YAAY,GAAG,CAAC,CAAC;AAAA,QACpC,CAAC,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU;AAAA,QAChD,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,MACX;AACA,eAAS,gBAAgBE,YAAW;AAClC,cAAM,eAAe,GAAGA,UAAS;AACjC,YAAI,YAAY,aAAa,OAAO,OAAO,WAAW;AAEtD,YAAI,cAAc,MAAM;AACtB,iBAAO;AAAA,QACT;AACA,YAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,sBAAY,UAAU,WAAW,IAAI,UAAU,CAAC,IAAI;AAAA,QACtD;AACA,YAAI,QAAQ,SAAS,GAAG;AACtB,iBAAO,WAAW,WAAW;AAAA,YAC3B,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,YAAI,oBAAoB,QAAQ,iBAAiB,CAAC,GAAG;AACnD,iBAAO,WAAW,iBAAiB,GAAG;AAAA,YACpC,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,eAAO,YAAa,QAAQ;AAAA,UAC1B,SAAS,GAAG,YAAY,IAAIA,UAAS;AAAA,QACvC,GAAG,CAAC,YAAa,KAAK;AAAA,UACpB,SAAS,GAAGA,UAAS;AAAA,QACvB,GAAG,IAAI,GAAG,YAAa,KAAK;AAAA,UAC1B,SAAS,GAAGA,UAAS;AAAA,QACvB,GAAG,IAAI,GAAG,YAAa,KAAK;AAAA,UAC1B,SAAS,GAAGA,UAAS;AAAA,QACvB,GAAG,IAAI,GAAG,YAAa,KAAK;AAAA,UAC1B,SAAS,GAAGA,UAAS;AAAA,QACvB,GAAG,IAAI,CAAC,CAAC;AAAA,MACX;AACA,YAAM,cAAc,YAAa,OAAO,eAAc,eAAc,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG;AAAA,QACrF,SAAS;AAAA,QACT,aAAa;AAAA,QACb,aAAa,UAAU;AAAA,MACzB,CAAC,GAAG,CAAC,gBAAgB,UAAU,KAAK,GAAG,MAAM,YAAa,OAAO;AAAA,QAC/D,SAAS,GAAG,UAAU,KAAK;AAAA,MAC7B,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;AACjB,UAAI,YAAY,YAAY,QAAQ,EAAE,QAAQ;AAC5C,cAAM,qBAAqB;AAAA,UACzB,CAAC,GAAG,UAAU,KAAK,YAAY,GAAG;AAAA,UAClC,CAAC,GAAG,UAAU,KAAK,OAAO,GAAG,UAAU;AAAA,QACzC;AACA,eAAO,QAAQ,YAAa,OAAO;AAAA,UACjC,SAAS,CAAC,GAAG,UAAU,KAAK,mBAAmB,MAAM,kBAAkB,OAAO,KAAK;AAAA,QACrF,GAAG,CAAC,UAAU,SAAS,YAAa,OAAO;AAAA,UACzC,OAAO;AAAA,QACT,GAAG,CAAC,WAAW,CAAC,GAAG,YAAa,OAAO;AAAA,UACrC,SAAS;AAAA,UACT,OAAO;AAAA,QACT,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAAA,MAClB;AACA,aAAO,QAAQ,WAAW;AAAA,IAC5B;AAAA,EACF;AACF,CAAC;;;ACrJD,aAAK,sBAAsB;AAE3B,aAAK,UAAU,SAAU,KAAK;AAC5B,MAAI,UAAU,aAAK,MAAM,YAAI;AAC7B,SAAO;AACT;AACA,IAAO,eAAQ;;;ACHf,SAAS,OAAO;AAAC;AACjB,IAAO,uBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,OAAO;AAAA,IACL,WAAW;AAAA,IACX,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAM,CAAC;AAAA,IAClB;AAAA,EACF;AAAA,EACA,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,IACF,IAAI,OAAO,yBAAoB;AAAA,MAC7B,aAAa,WAAW;AAAA,MACxB,SAAS,WAAW,KAAK;AAAA,IAC3B,CAAC;AACD,QAAI;AACJ,UAAM,cAAc,OAAK;AACvB,mBAAa,SAAS;AACtB,kBAAY,WAAW,MAAM;AAC3B,gBAAQ,CAAC;AAAA,MACX,CAAC;AAAA,IACH;AACA,oBAAgB,MAAM;AACpB,mBAAa,SAAS;AAAA,IACxB,CAAC;AACD,WAAO,MAAM;AACX,UAAI;AACJ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,eAAe,QAAQ,YAAY,KAAK,KAAK,CAAC;AACpD,aAAO,YAAa,cAAM;AAAA,QACxB,aAAa,GAAG,SAAS;AAAA,QACzB,aAAa,aAAa;AAAA,QAC1B,YAAY,WAAS;AACnB,cAAI;AAAA,YACF;AAAA,UACF,IAAI;AACJ,gBAAM,SAAS,QAAQ,KAAK,WAAS;AACnC,gBAAI;AAAA,cACF;AAAA,YACF,IAAI;AACJ,mBAAO,UAAU;AAAA,UACnB,CAAC;AACD,uBAAa,MAAM;AAAA,QACrB;AAAA,QACA,eAAe;AAAA,MACjB,GAAG;AAAA,QACD,SAAS,MAAM,CAAC,CAAC,QAAQ,SAAS,QAAQ,IAAI,CAAC,QAAQ,UAAU;AAC/D,cAAIC,KAAI;AACR,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA,QAAQ,OAAO;AAAA,YACf,OAAO;AAAA,YACP;AAAA,UACF,IAAI;AACJ,iBAAO,YAAa,kBAAU;AAAA,YAC5B,OAAO;AAAA,YACP,YAAY;AAAA,YACZ,gBAAgB,MAAM;AACpB,6BAAe,KAAK;AAAA,YACtB;AAAA,YACA,SAAS;AAAA,YACT,SAAS;AAAA,UACX,GAAG;AAAA,YACD,SAAS,MAAM,EAAE,MAAMA,MAAK,MAAM,YAAY,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,OAAO,MAAM,OAAO,QAAQ,OAAO,SAAS,KAAK,OAAO,UAAU,aAAa,MAAM,MAAM,IAAI,KAAK;AAAA,UAC7L,CAAC;AAAA,QACH,CAAC,GAAG,CAAC,QAAQ,SAAS,QAAQ,WAAW,IAAI,YAAa,kBAAU;AAAA,UAClE,OAAO;AAAA,UACP,YAAY;AAAA,QACd,GAAG;AAAA,UACD,SAAS,MAAM,EAAE,KAAK,MAAM,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,CAAC;AAAA,QAClG,CAAC,IAAI,MAAM,QAAQ,SAAS,YAAa,kBAAU;AAAA,UACjD,OAAO;AAAA,UACP,YAAY;AAAA,QACd,GAAG;AAAA,UACD,SAAS,MAAM,CAAC,YAAa,cAAM;AAAA,YACjC,QAAQ;AAAA,UACV,GAAG,IAAI,CAAC;AAAA,QACV,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AClGD,IAAM,sBAAsB;AAAA,EAC1B,aAAa;AAAA,IACX,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,QAAQ,CAAC,GAAG,CAAC;AAAA,IACb,UAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,QAAQ,CAAC,GAAG,CAAC;AAAA,IACb,UAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,QAAQ,CAAC,GAAG,EAAE;AAAA,IACd,UAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,SAAS;AAAA,IACP,QAAQ,CAAC,MAAM,IAAI;AAAA,IACnB,QAAQ,CAAC,GAAG,EAAE;AAAA,IACd,UAAU;AAAA,MACR,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AAAA,EACF;AACF;AACA,IAAO,yBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,OAAO;AAAA,IACL,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAM,CAAC;AAAA,IAClB;AAAA,IACA,WAAW;AAAA,IACX,WAAW;AAAA,IACX,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,mBAAmB;AAAA,EACrB;AAAA,EACA,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,UAAM,oBAAoB,MAAM;AAC9B,aAAO,GAAG,MAAM,SAAS;AAAA,IAC3B;AACA,UAAM,qBAAqB,MAAM;AAC/B,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,aAAO,YAAa,sBAAc;AAAA,QAChC,aAAa,kBAAkB;AAAA,QAC/B,WAAW;AAAA,MACb,GAAG;AAAA,QACD,iBAAiB,MAAM;AAAA,QACvB,QAAQ,MAAM;AAAA,MAChB,CAAC;AAAA,IACH;AACA,UAAM,iBAAiB,SAAS,MAAM;AACpC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,UAAIC,kBAAiB;AACrB,UAAI,cAAc,OAAO;AACvB,QAAAA,kBAAiB,cAAc,QAAQ,YAAY;AAAA,MACrD,OAAO;AACL,QAAAA,kBAAiB,cAAc,QAAQ,aAAa;AAAA,MACtD;AACA,aAAOA;AAAA,IACT,CAAC;AACD,WAAO,MAAM;AACX,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,aAAO,YAAa,oBAAS;AAAA,QAC3B,aAAa,kBAAkB;AAAA,QAC/B,gBAAgB;AAAA,QAChB,SAAS,mBAAmB;AAAA,QAC5B,kBAAkB,MAAM;AAAA,QACxB,kBAAkB,eAAe;AAAA,QACjC,uBAAuB;AAAA,QACvB,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,MACvB,GAAG;AAAA,QACD,SAAS,MAAM;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AC9GM,IAAM,YAAY,MAAM,OAAO,QAAQ;AACvC,IAAM,gBAAgB;AAAA,EAC3B,WAAW;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,QAAQ,kBAAU,UAAU,CAAC,kBAAU,QAAQ,kBAAU,QAAQ,kBAAU,MAAM,CAAC,CAAC;AAAA,EACnF,WAAW;AAAA,EACX,OAAO;AAAA,EACP,UAAU;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,WAAW,kBAAU,MAAM,SAAS;AAAA,EACpC,WAAW,kBAAU;AAAA,EACrB,iBAAiB;AAAA,EACjB,cAAc;AAAA,IACZ,MAAM,CAAC,SAAS,QAAQ;AAAA,EAC1B;AAAA,EACA,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,IACjB,MAAM;AAAA,EACR;AAAA,EACA,SAAS,UAAU;AAAA,EACnB,SAAS;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,MAAM,CAAC,QAAQ,MAAM;AAAA,EACrB,WAAW;AAAA,IACT,MAAM;AAAA,EACR;AACF;AACO,IAAM,kBAAkB,SAAS,SAAS,CAAC,GAAG,aAAa,GAAG;AAAA,EACnE,mBAAmB;AACrB,CAAC;AACM,IAAM,eAAe;AAAA,EAC1B,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN;AAAA,EACA,cAAc,MAAM;AACtB;AACA,IAAO,wBAAQ,yBAAiB,iBAAiB,YAAY;;;AC/C7D,IAAIC,UAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAWA,SAASC,QAAO;AAAC;AACjB,IAAO,mBAAQ,gBAAgB;AAAA,EAC7B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO,yBAAiB,iBAAiB,YAAY;AAAA,EACrD,OAAO,CAAC,UAAU,UAAU,UAAU,SAAS,QAAQ,YAAY;AAAA,EACnE,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAMC,WAAU,IAAI,IAAI;AACxB,UAAM,WAAW,IAAI,IAAI;AACzB,UAAM,UAAU,IAAI;AACpB,UAAM,QAAQ,SAAS;AAAA,MACrB,OAAO,MAAM,SAAS;AAAA,MACtB,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,aAAa;AAAA,MACb,eAAe;AAAA,MACf,aAAa;AAAA,MACb,SAAS;AAAA,IACX,CAAC;AACD,gBAAY,MAAM;AAChB,YAAM,QAAQ,MAAM;AAAA,IACtB,CAAC;AACD,UAAM,gBAAgB,SAAO;AAC3B,WAAK,UAAU,GAAG;AAAA,IACpB;AACA,UAAM,WAAW,WAAS;AACxB,UAAI;AAAA,QACF,QAAQ;AAAA,UACN;AAAA,QACF;AAAA,MACF,IAAI;AACJ,oBAAc,KAAK;AAAA,IACrB;AACA,UAAM,eAAe,CAAC,aAAa,eAAe,oBAAoB;AACpE,eAAS,OAAO;AAAA,QACd,WAAW;AAAA,QACX;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa;AAAA,MACf,CAAC;AAAA,IACH;AACA,UAAM,cAAc,cAAY;AAC9B,eAAS,OAAO;AAAA,QACd,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,aAAa;AAAA,MACf,CAAC;AACD,mBAAa,QAAQ,aAAa,SAAS,SAAS,SAAS;AAAA,IAC/D;AACA,UAAM,YAAY,WAAS;AACzB,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AAEJ,UAAI,CAAC,MAAM,WAAW;AACpB;AAAA,MACF;AACA,UAAI,UAAU,gBAAQ,MAAM,UAAU,gBAAQ,MAAM;AAElD,cAAM,YAAY,QAAQ,MAAM;AAChC,cAAM,SAAS,UAAU,gBAAQ,KAAK,KAAK;AAC3C,cAAM,kBAAkB,MAAM,cAAc,SAAS,aAAa;AAClE,cAAM,cAAc;AACpB,cAAM,eAAe;AAAA,MACvB,WAAW,UAAU,gBAAQ,KAAK;AAChC,oBAAY;AAAA,MACd,WAAW,UAAU,gBAAQ,OAAO;AAElC,cAAM,eAAe;AACrB,YAAI,CAAC,QAAQ,MAAM,QAAQ;AACzB,sBAAY;AACZ;AAAA,QACF;AACA,cAAM,SAAS,QAAQ,MAAM,MAAM,WAAW;AAC9C,qBAAa,MAAM;AAAA,MACrB;AAAA,IACF;AACA,UAAM,UAAU,WAAS;AACvB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,aAAa;AAAA,QACb;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ;AAAA,QACA,gBAAAC;AAAA,MACF,IAAI;AACJ,YAAM,SAAS,MAAM;AACrB,UAAI,OAAO,WAAW;AACpB;AAAA,MACF;AACA,YAAM,qBAAqB,uBAAuB,MAAM;AACxD,YAAM;AAAA,QACJ,UAAU;AAAA,QACV,QAAQ;AAAA,MACV,IAAI,oBAAoB,oBAAoB,MAAM;AAElD,UAAI,CAAC,gBAAQ,KAAK,gBAAQ,IAAI,gBAAQ,MAAM,gBAAQ,KAAK,EAAE,QAAQ,KAAK,MAAM,IAAI;AAChF;AAAA,MACF;AACA,UAAI,iBAAiB,IAAI;AACvB,cAAM,cAAc,mBAAmB,MAAM,eAAe,cAAc,MAAM;AAChF,cAAM,kBAAkBA,gBAAe,aAAa,KAAK;AACzD,cAAM,cAAc,CAAC,CAAC,WAAW,WAAW,EAAE;AAC9C,YAAI,iBAAiB;AACnB,cAAI,QAAQ,iBAAiB,QAAQ,WAAW,aAAa,gBAAgB,mBAAmB,aAAa;AAC3G,yBAAa,aAAa,eAAe,YAAY;AAAA,UACvD;AAAA,QACF,WAAW,WAAW;AAEpB,sBAAY;AAAA,QACd;AAKA,YAAI,iBAAiB;AACnB,eAAK,UAAU,aAAa,aAAa;AAAA,QAC3C;AAAA,MACF,WAAW,WAAW;AACpB,oBAAY;AAAA,MACd;AAAA,IACF;AACA,UAAM,eAAe,WAAS;AAC5B,UAAI,CAAC,MAAM,WAAW;AACpB,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,UAAM,eAAe,WAAS;AAC5B,cAAQ,KAAK;AAAA,IACf;AACA,UAAM,cAAc,WAAS;AAC3B,aAAO,KAAK;AAAA,IACd;AACA,UAAM,UAAU,WAAS;AACvB,mBAAa,QAAQ,KAAK;AAC1B,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,UAAI,CAAC,WAAW,OAAO;AACrB,aAAK,SAAS,KAAK;AAAA,MACrB;AACA,YAAM,UAAU;AAAA,IAClB;AACA,UAAM,SAAS,WAAS;AACtB,cAAQ,QAAQ,WAAW,MAAM;AAC/B,cAAM,UAAU;AAChB,oBAAY;AACZ,aAAK,QAAQ,KAAK;AAAA,MACpB,GAAG,GAAG;AAAA,IACR;AACA,UAAM,eAAe,YAAU;AAC7B,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,OAAO,eAAe;AAAA,MACxB,IAAI;AACJ,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,mBAAmB,MAAM,OAAO;AAAA,QAClC,iBAAiB,MAAM;AAAA,QACvB,YAAY;AAAA,QACZ,QAAQ,MAAM;AAAA,QACd,gBAAgB,SAAS,MAAM,kBAAkB;AAAA,QACjD;AAAA,MACF,CAAC;AACD,oBAAc,IAAI;AAClB,kBAAY,MAAM;AAEhB,0BAAkB,SAAS,MAAM,OAAO,iBAAiB;AAAA,MAC3D,CAAC;AACD,WAAK,UAAU,QAAQ,MAAM,aAAa;AAAA,IAC5C;AACA,UAAM,iBAAiB,iBAAe;AACpC,YAAM,cAAc;AAAA,IACtB;AACA,UAAM,aAAa,iBAAe;AAChC,YAAM,oBAAoB,eAAe,MAAM,eAAe;AAC9D,YAAM;AAAA,QACJ,cAAAC;AAAA,MACF,IAAI;AACJ,YAAM,OAAO,MAAM,QAAQ,OAAO,YAAU;AAE1C,YAAI,CAAC,CAACA,kBAAiB,OAAO;AAC5B,iBAAO;AAAA,QACT;AACA,eAAOA,cAAa,mBAAmB,MAAM;AAAA,MAC/C,CAAC;AACD,aAAO;AAAA,IACT;AACA,UAAM,UAAU,SAAS,MAAM;AAC7B,aAAO,WAAW;AAAA,IACpB,CAAC;AACD,UAAM,QAAQ,MAAM;AAClB,eAAS,MAAM,MAAM;AAAA,IACvB;AACA,UAAM,OAAO,MAAM;AACjB,eAAS,MAAM,KAAK;AAAA,IACtB;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF,CAAC;AACD,YAAQ,yBAAoB;AAAA,MAC1B,aAAa,MAAM,OAAO,aAAa;AAAA,MACvC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS,MAAM,OAAO,SAAS;AAAA,IACjC,CAAC;AACD,cAAU,MAAM;AACd,eAAS,MAAM;AACb,YAAI,MAAM,WAAW;AACnB,UAAAF,SAAQ,MAAM,YAAY,SAAS,MAAM,aAAa;AAAA,QACxD;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,WAAO,MAAM;AACX,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,OACJ,YAAYF,QAAO,OAAO,CAAC,aAAa,aAAa,kBAAkB,qBAAqB,WAAW,CAAC;AAC1G,YAAM;AAAA,QACF,OAAO;AAAA,QACP;AAAA,MACF,IAAI,OACJ,aAAaA,QAAO,OAAO,CAAC,SAAS,OAAO,CAAC;AAC/C,YAAM,aAAa,aAAK,WAAW,CAAC,SAAS,UAAU,SAAS,kBAAkB,gBAAgB,WAAW,SAAS,CAAC;AACvH,YAAM,gBAAgB,SAAS,SAAS,SAAS,CAAC,GAAG,UAAU,GAAG,UAAU,GAAG;AAAA,QAC7E,UAAUC;AAAA,QACV,UAAUA;AAAA,QACV,OAAO,MAAM;AAAA,QACb,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,SAAS;AAAA,QACT,SAAS;AAAA,QACT,cAAc;AAAA,MAChB,CAAC;AACD,aAAO,YAAa,OAAO;AAAA,QACzB,SAAS,mBAAW,WAAW,SAAS;AAAA,QACxC,SAAS;AAAA,MACX,GAAG,CAAC,YAAa,mBAAW,eAAc,eAAc,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG;AAAA,QAC9E,OAAO;AAAA,QACP,OAAO;AAAA,MACT,CAAC,GAAG,IAAI,GAAG,aAAa,YAAa,OAAO;AAAA,QAC1C,OAAOC;AAAA,QACP,SAAS,GAAG,SAAS;AAAA,MACvB,GAAG,CAAC,MAAM,MAAM,MAAM,GAAG,eAAe,GAAG,YAAa,wBAAgB;AAAA,QACtE,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,qBAAqB,MAAM;AAAA,QAC3B,aAAa;AAAA,QACb,WAAW,YAAY,QAAQ,QAAQ,CAAC;AAAA,QACxC,WAAW;AAAA,QACX,aAAa;AAAA,QACb,qBAAqB;AAAA,MACvB,GAAG;AAAA,QACD,SAAS,MAAM,CAAC,YAAa,QAAQ,MAAM,CAAC,aAAa,CAAC,CAAC;AAAA,QAC3D,iBAAiB,MAAM;AAAA,QACvB,QAAQ,MAAM;AAAA,MAChB,CAAC,GAAG,MAAM,MAAM,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAC,CAAC,CAAC;AAAA,IAClE;AAAA,EACF;AACF,CAAC;;;ACnTM,IAAM,mBAAmB;AAAA,EAC9B,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS,WAAW;AACtB;AACO,IAAM,cAAc,SAAS,SAAS,CAAC,GAAG,gBAAgB,GAAG;AAAA,EAClE,OAAO,QAAQ,CAAC,CAAC;AACnB,CAAC;AACM,IAAM,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO,QAAQ,MAAM;AACnB,QAAI;AAAA,MACF;AAAA,IACF,IAAI;AACJ,QAAI;AACJ,YAAQ,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK;AAAA,EAChF;AACF;AACA,IAAO,iBAAQ,gBAAgB,SAAS;AAAA,EACtC,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AACF,GAAG,aAAa,CAAC;;;ACtBjB,IAAO,sBAAQ;;;ACAf,IAAM,mBAAmB,WAAS;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,sBAAsB,KAAK,OAAO,MAAM,gBAAgB,MAAM,WAAW,MAAM,cAAc,CAAC;AACpG,SAAO;AAAA,IACL,CAAC,YAAY,GAAG,SAAS,SAAS,SAAS,SAAS,SAAS,CAAC,GAAG,eAAe,KAAK,CAAC,GAAG,mBAAmB,KAAK,CAAC,GAAG;AAAA,MACnH,UAAU;AAAA,MACV,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV;AAAA,MACA,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB,CAAC,GAAG,eAAe,OAAO,YAAY,CAAC,GAAG;AAAA,MACxC,cAAc;AAAA,QACZ,cAAc,SAAS,CAAC,GAAG,iBAAiB,KAAK,CAAC;AAAA,MACpD;AAAA,MACA,aAAa,SAAS,CAAC,GAAG,eAAe,KAAK,CAAC;AAAA,MAC/C,CAAC,mBAAmB,YAAY,SAAS,GAAG;AAAA,QAC1C,UAAU;AAAA,QACV,KAAK;AAAA,QACL,gBAAgB;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,MACV;AAAA;AAAA,MAEA,CAAC,eAAe,YAAY,UAAU,GAAG;AAAA,QACvC,OAAO;AAAA,QACP,WAAW;AAAA,QACX,WAAW,gBAAgB;AAAA,QAC3B,QAAQ;AAAA,QACR,SAAS,GAAG,oBAAoB,MAAM,sBAAsB;AAAA,QAC5D,UAAU;AAAA,QACV,WAAW;AAAA,QACX,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,eAAe;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,QACX,SAAS;AAAA,MACX;AAAA,MACA,cAAc,SAAS;AAAA,QACrB,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,iBAAiB;AAAA,MACnB,GAAG,oBAAoB,MAAM,oBAAoB,CAAC;AAAA,MAClD,CAAC,GAAG,YAAY,UAAU,GAAG;AAAA,QAC3B,UAAU;AAAA,QACV,KAAK;AAAA,QACL,gBAAgB;AAAA,QAChB,QAAQ;AAAA,QACR,kBAAkB;AAAA,QAClB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,eAAe;AAAA,QACf,UAAU;AAAA,UACR,SAAS;AAAA,UACT,WAAW;AAAA,QACb;AAAA,MACF;AAAA;AAAA,MAEA,cAAc,SAAS,SAAS,CAAC,GAAG,eAAe,KAAK,CAAC,GAAG;AAAA,QAC1D,UAAU;AAAA,QACV,KAAK;AAAA,QACL,kBAAkB;AAAA,QAClB,QAAQ,MAAM;AAAA,QACd,WAAW;AAAA,QACX;AAAA,QACA,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,SAAS;AAAA,QACT,WAAW;AAAA,QACX,YAAY;AAAA,UACV,SAAS;AAAA,QACX;AAAA,QACA,CAAC,GAAG,YAAY,gBAAgB,GAAG;AAAA,UACjC,WAAW,MAAM;AAAA,UACjB,cAAc;AAAA,UACd,oBAAoB;AAAA,UACpB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,SAAS;AAAA,UACT,UAAU,SAAS,SAAS,CAAC,GAAG,YAAY,GAAG;AAAA,YAC7C,UAAU;AAAA,YACV,SAAS;AAAA,YACT,UAAU,MAAM;AAAA,YAChB,SAAS,GAAG,mBAAmB,MAAM,wBAAwB;AAAA,YAC7D,OAAO;AAAA,YACP,YAAY;AAAA,YACZ;AAAA,YACA,QAAQ;AAAA,YACR,YAAY,cAAc,kBAAkB;AAAA,YAC5C,WAAW;AAAA,cACT,iBAAiB;AAAA,YACnB;AAAA,YACA,iBAAiB;AAAA,cACf,wBAAwB;AAAA,cACxB,sBAAsB;AAAA,cACtB,sBAAsB;AAAA,cACtB,oBAAoB;AAAA,YACtB;AAAA,YACA,gBAAgB;AAAA,cACd,wBAAwB;AAAA,cACxB,sBAAsB;AAAA,cACtB,sBAAsB;AAAA,cACtB,oBAAoB;AAAA,YACtB;AAAA,YACA,cAAc;AAAA,cACZ,OAAO;AAAA,cACP,QAAQ;AAAA,cACR,WAAW;AAAA,gBACT,OAAO;AAAA,gBACP,iBAAiB;AAAA,gBACjB,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,YACA,cAAc;AAAA,cACZ,OAAO;AAAA,cACP,YAAY,MAAM;AAAA,cAClB,iBAAiB;AAAA,YACnB;AAAA,YACA,YAAY;AAAA,cACV,iBAAiB;AAAA,YACnB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAEA,IAAOG,iBAAQ,sBAAsB,YAAY,WAAS;AACxD,QAAM,gBAAgB,eAAe,KAAK;AAC1C,SAAO,CAAC,iBAAiB,aAAa,CAAC;AACzC,GAAG,YAAU;AAAA,EACX,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,aAAa,MAAM,kBAAkB;AACvC,EAAE;;;AC3KF,IAAIC,UAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAiBA,SAAS,sBAAsB;AAC7B,SAAO;AACT;AACA,IAAM,cAAc,WAAY;AAC9B,MAAI,QAAQ,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAChF,MAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,QAAM;AAAA,IACJ,SAAS;AAAA,IACT,QAAQ;AAAA,EACV,IAAI;AACJ,QAAM,aAAa,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AAC3D,SAAO,MAAM,MAAM,KAAK,EAAE,IAAI,WAAY;AACxC,QAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAC9E,QAAI,YAAY;AAChB,eAAW,KAAK,eAAa;AAC3B,YAAM,WAAW,IAAI,MAAM,GAAG,UAAU,MAAM;AAC9C,UAAI,aAAa,WAAW;AAC1B,oBAAY;AACZ,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,cAAc,MAAM;AACtB,aAAO;AAAA,QACL,QAAQ;AAAA,QACR,OAAO,IAAI,MAAM,UAAU,MAAM;AAAA,MACnC;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC,EAAE,OAAO,YAAU,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,KAAK;AAChD;AACO,IAAMC,iBAAgB,MAAM,SAAS,SAAS,CAAC,GAAG,aAAiB,GAAG;AAAA,EAC3E,SAAS;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,MAAM;AAAA,EACR;AAAA,EACA,QAAQ;AAAA,IACN,MAAM;AAAA,EACR;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,EACR;AAAA,EACA,UAAU;AAAA,IACR,MAAM;AAAA,EACR;AAAA,EACA,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,kBAAkB;AAAA,IAChB,MAAM;AAAA,EACR;AAAA,EACA,iBAAiB,kBAAU;AAAA,EAC3B,cAAc;AAAA,EACd,IAAI;AAAA,EACJ,QAAQ;AACV,CAAC;AACD,IAAM,WAAW,gBAAgB;AAAA,EAC/B,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AAAA,EACA,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAOA,eAAc;AAAA,EACrB,OAAO;AAAA,EACP,MAAM,OAAO,MAAM;AACjB,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,IAAI,IAAI;AAEZ,QAAI,MAAuC;AACzC,yBAAW,CAAC,kBAAkB,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,KAAK,MAAM,CAAC,CAAC,EAAE,QAAQ,YAAY,gEAAgE;AAAA,IACpM;AACA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,wBAAgB,YAAY,KAAK;AACrC,UAAM,CAAC,SAAS,MAAM,IAAIC,eAAS,SAAS;AAC5C,UAAM,UAAU,WAAW,KAAK;AAChC,UAAM,aAAa,WAAW,IAAI;AAClC,UAAM,QAAQ,YAAY,MAAM,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,KAAK,MAAM,kBAAkB,QAAQ,OAAO,SAAS,KAAK,EAAE;AAC1I,UAAM,kBAAkB,yBAAyB;AACjD,UAAM,uBAAuB,qBAAqB,UAAU;AAC5D,UAAM,eAAe,SAAS,MAAM,gBAAgB,qBAAqB,QAAQ,MAAM,MAAM,CAAC;AAC9F,uBAAmB;AAAA,MACjB,WAAW,SAAS,MAAM,GAAG,UAAU,KAAK,OAAO;AAAA,MACnD,MAAM,SAAS,MAAM,UAAU;AAAA,MAC/B,YAAY,SAAS,MAAM,KAAK;AAAA,MAChC,SAAS,MAAM;AAAA,MAAC;AAAA,MAChB,WAAW,WAAS;AAClB,YAAI;AAAA,UACF;AAAA,QACF,IAAI;AAEJ,wBAAQ,CAAC,QAAQ,SAAS,YAAY,YAAY,SAAS,IAAI,yCAAyC;AAAA,MAC1G;AAAA,IACF,CAAC;AACD,UAAM,MAAM,MAAM,OAAO,SAAO;AAC9B,YAAM,QAAQ;AAAA,IAChB,CAAC;AACD,UAAM,cAAc,OAAK;AACvB,cAAQ,QAAQ;AAChB,WAAK,SAAS,CAAC;AAAA,IACjB;AACA,UAAM,aAAa,OAAK;AACtB,cAAQ,QAAQ;AAChB,WAAK,QAAQ,CAAC;AACd,sBAAgB,YAAY;AAAA,IAC9B;AACA,UAAM,eAAe,WAAY;AAC/B,eAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,aAAK,IAAI,IAAI,UAAU,IAAI;AAAA,MAC7B;AACA,WAAK,UAAU,GAAG,IAAI;AACtB,cAAQ,QAAQ;AAAA,IAClB;AACA,UAAM,eAAe,SAAO;AAC1B,UAAI,MAAM,UAAU,QAAW;AAC7B,cAAM,QAAQ;AAAA,MAChB;AACA,WAAK,gBAAgB,GAAG;AACxB,WAAK,UAAU,GAAG;AAClB,sBAAgB,cAAc;AAAA,IAChC;AACA,UAAM,qBAAqB,MAAM;AAC/B,YAAM,kBAAkB,MAAM;AAC9B,UAAI,oBAAoB,QAAW;AACjC,eAAO;AAAA,MACT;AACA,UAAI,MAAM,iBAAiB;AACzB,eAAO,MAAM,gBAAgB;AAAA,MAC/B;AACA,aAAO,YAAY,QAAQ;AAAA,IAC7B;AACA,UAAM,aAAa,MAAM;AACvB,UAAIC;AACJ,aAAO,kBAAkBA,MAAK,MAAM,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,KAAK,KAAK,MAAM,CAAC,CAAC,EAAE,IAAI,UAAQ;AACnH,YAAIA,KAAIC;AACR,eAAO,SAAS,SAAS,CAAC,GAAG,eAAe,IAAI,CAAC,GAAG;AAAA,UAClD,QAAQA,OAAMD,MAAK,KAAK,cAAc,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa,QAAQC,QAAO,SAAS,SAASA,IAAG,KAAKD,GAAE;AAAA,QACpI,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,UAAM,QAAQ,MAAM;AAClB,iBAAW,MAAM,MAAM;AAAA,IACzB;AACA,UAAM,OAAO,MAAM;AACjB,iBAAW,MAAM,KAAK;AAAA,IACxB;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,uBAAuB,SAAS,MAAM,MAAM,UAAU,sBAAsB,MAAM,YAAY;AACpG,WAAO,MAAM;AACX,YAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA,OAAO;AAAA,QACP,KAAK,gBAAgB,GAAG;AAAA,MAC1B,IAAI,OACJ,YAAYH,QAAO,OAAO,CAAC,YAAY,qBAAqB,QAAQ,IAAI,CAAC;AAC3E,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACF,OAAO;AAAA,MACT,IAAI,OACJ,aAAaA,QAAO,OAAO,CAAC,OAAO,CAAC;AACtC,YAAM,aAAa,aAAK,WAAW,CAAC,gBAAgB,kBAAkB,WAAW,CAAC;AAClF,YAAM,kBAAkB,mBAAW;AAAA,QACjC,CAAC,GAAG,UAAU,KAAK,WAAW,GAAG;AAAA,QACjC,CAAC,GAAG,UAAU,KAAK,UAAU,GAAG,QAAQ;AAAA,QACxC,CAAC,GAAG,UAAU,KAAK,MAAM,GAAG,UAAU,UAAU;AAAA,MAClD,GAAG,oBAAoB,UAAU,OAAO,aAAa,KAAK,GAAG,CAAC,eAAe,WAAW,OAAO,KAAK;AACpG,YAAMC,iBAAgB,SAAS,SAAS,SAAS,SAAS;AAAA,QACxD,WAAW,UAAU;AAAA,MACvB,GAAG,UAAU,GAAG;AAAA,QACd;AAAA,QACA,WAAW,UAAU;AAAA,QACrB,cAAc,qBAAqB;AAAA,QACnC;AAAA,QACA,SAAS,MAAM,UAAU,CAAC;AAAA,UACxB,OAAO;AAAA,UACP,UAAU;AAAA,UACV,OAAO,YAAa,cAAM;AAAA,YACxB,QAAQ;AAAA,UACV,GAAG,IAAI;AAAA,QACT,CAAC,IAAI,MAAM,WAAW,WAAW;AAAA,QACjC,OAAO;AAAA,MACT,CAAC,GAAG,UAAU,GAAG;AAAA,QACf;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,QACV,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,KAAK;AAAA,QACL,OAAO,MAAM;AAAA,QACb;AAAA,MACF,CAAC;AACD,YAAM,WAAW,YAAa,qBAAY,eAAc,eAAc,CAAC,GAAGA,cAAa,GAAG,CAAC,GAAG;AAAA,QAC5F,qBAAqB,OAAO;AAAA,MAC9B,CAAC,GAAG;AAAA,QACF,iBAAiB;AAAA,QACjB,QAAQ,MAAM;AAAA,MAChB,CAAC;AACD,UAAI,aAAa;AACf,eAAO,QAAQ,YAAa,OAAO;AAAA,UACjC,SAAS,mBAAW,GAAG,UAAU,KAAK,kBAAkB,oBAAoB,GAAG,UAAU,KAAK,kBAAkB,aAAa,OAAO,WAAW,GAAG,WAAW,OAAO,KAAK;AAAA,QAC3K,GAAG,CAAC,UAAU,YAAa,QAAQ;AAAA,UACjC,SAAS,GAAG,UAAU,KAAK;AAAA,QAC7B,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AAAA,MACtB;AACA,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAAA,EACF;AACF,CAAC;AAEM,IAAM,iBAAiB,gBAAgB,SAAS,SAAS;AAAA,EAC9D,cAAc;AAAA,IACZ,MAAM;AAAA,EACR;AACF,GAAG,aAAa,GAAG;AAAA,EACjB,MAAM;AAAA,EACN,OAAO;AACT,CAAC,CAAC;AACF,IAAO,mBAAQ,SAAS,UAAU;AAAA,EAChC,QAAQ;AAAA,EACR;AAAA,EACA,SAAS,SAAO;AACd,QAAI,UAAU,SAAS,MAAM,QAAQ;AACrC,QAAI,UAAU,eAAe,MAAM,cAAc;AACjD,WAAO;AAAA,EACT;AACF,CAAC;", "names": ["indexGuid", "_a", "_b", "childrenNodes", "_a", "_b", "childList", "delay", "callback", "options", "_ref", "_ref$noTrailing", "noTrailing", "_ref$noLeading", "noLeading", "_ref$debounceMode", "debounceMode", "undefined", "timeoutID", "cancelled", "lastExec", "clearExistingTimeout", "clearTimeout", "cancel", "_ref2", "_ref2$upcomingOnly", "upcomingOnly", "wrapper", "_len", "arguments", "length", "arguments_", "Array", "_key", "self", "elapsed", "Date", "now", "exec", "apply", "clear", "setTimeout", "_ref$atBegin", "atBegin", "throttle", "style_default", "__rest", "style_default", "prefixCls", "_a", "popupPlacement", "__rest", "noop", "measure", "validateSearch", "filterOption", "style_default", "__rest", "mentionsProps", "style_default", "_a", "_b"]}